/**
 * ========================================
 * ASG LESSONS ACCESS CHECK - lessons.php
 * ========================================
 * 
 * JavaScript para verificar acceso a lecciones en lessons.php
 * Bloquea contenido si el usuario no está inscrito
 * Incluye tracking de progreso de lecciones
 * 
 * Uso: Incluir al final de lessons.php
 * Requiere: COURSE_CODE definido globalmente
 */

// Configuración del curso (se debe definir en lessons.php)
const COURSE_CODE = window.COURSE_CODE || new URLSearchParams(window.location.search).get('course') || 'course_1';

// Estado de verificación
let accessVerified = false;
let userEnrolled = false;

// Verificar acceso al cargar la página
document.addEventListener('DOMContentLoaded', function() {
    console.log('ASG: Verificando acceso a lecciones para curso:', COURSE_CODE);
    verifyLessonAccess();
});

// Función principal de verificación de acceso
async function verifyLessonAccess() {
    try {
        // Mostrar loading mientras verifica
        showAccessLoading();
        
        const response = await fetch(`/wp-json/asg/v1/check-enrollment?course=${COURSE_CODE}`);
        const result = await response.json();
        
        if (result.success && result.enrolled) {
            // Usuario tiene acceso
            userEnrolled = true;
            accessVerified = true;
            showLessonsContent();
            initializeLessonTracking();
            console.log('ASG: Acceso verificado para usuario:', result.user_info);
        } else {
            // Usuario no tiene acceso
            userEnrolled = false;
            accessVerified = true;
            showAccessDenied(result.reason);
        }
        
    } catch (error) {
        console.error('ASG: Error verificando acceso:', error);
        showAccessError();
    }
}

// Mostrar loading durante verificación
function showAccessLoading() {
    const loadingHtml = `
        <div id="asg-access-loading" class="access-loading">
            <div class="loading-spinner"></div>
            <h3>Verificando acceso...</h3>
            <p>Comprobando tu inscripción al curso</p>
        </div>
    `;
    
    // Ocultar contenido de lecciones
    const lessonsContent = document.querySelector('.lessons-content, .course-content, main');
    if (lessonsContent) {
        lessonsContent.style.display = 'none';
    }
    
    // Mostrar loading
    document.body.insertAdjacentHTML('afterbegin', loadingHtml);
}

// Mostrar contenido de lecciones (acceso permitido)
function showLessonsContent() {
    // Remover loading
    const loading = document.getElementById('asg-access-loading');
    if (loading) {
        loading.remove();
    }
    
    // Mostrar contenido
    const lessonsContent = document.querySelector('.lessons-content, .course-content, main');
    if (lessonsContent) {
        lessonsContent.style.display = 'block';
    }
    
    // Agregar mensaje de bienvenida
    addWelcomeMessage();
}

// Mostrar acceso denegado
function showAccessDenied(reason) {
    // Remover loading
    const loading = document.getElementById('asg-access-loading');
    if (loading) {
        loading.remove();
    }
    
    // Ocultar contenido de lecciones
    const lessonsContent = document.querySelector('.lessons-content, .course-content, main');
    if (lessonsContent) {
        lessonsContent.style.display = 'none';
    }
    
    // Mostrar mensaje de acceso denegado
    const deniedHtml = getDeniedMessage(reason);
    document.body.insertAdjacentHTML('afterbegin', deniedHtml);
}

// Obtener mensaje de acceso denegado según la razón
function getDeniedMessage(reason) {
    let title, message, actionButton;
    
    switch (reason) {
        case 'not_logged_in':
            title = '🔐 Inicia Sesión';
            message = 'Debes iniciar sesión para acceder a las lecciones de este curso.';
            actionButton = `<a href="/wp-login.php?redirect_to=${encodeURIComponent(window.location.href)}" class="btn btn-primary">Iniciar Sesión</a>`;
            break;
            
        case 'not_enrolled':
            title = '📚 Inscripción Requerida';
            message = 'No estás inscrito en este curso. Inscríbete para acceder a todas las lecciones.';
            actionButton = `<a href="/vistapreliminar/?course=${COURSE_CODE}" class="btn btn-primary">Ver Curso e Inscribirse</a>`;
            break;
            
        case 'invalid_user':
            title = '❌ Usuario Inválido';
            message = 'Hay un problema con tu cuenta. Por favor, contacta al soporte técnico.';
            actionButton = `<a href="/contact" class="btn btn-secondary">Contactar Soporte</a>`;
            break;
            
        default:
            title = '🚫 Acceso Restringido';
            message = 'No tienes acceso a este contenido. Verifica tu inscripción al curso.';
            actionButton = `<a href="/" class="btn btn-secondary">Volver al Inicio</a>`;
    }
    
    return `
        <div id="asg-access-denied" class="access-denied">
            <div class="denied-content">
                <h2>${title}</h2>
                <p>${message}</p>
                <div class="denied-actions">
                    ${actionButton}
                    <a href="/" class="btn btn-outline-secondary">Explorar Cursos</a>
                </div>
                <div class="course-info">
                    <h4>Curso: ${COURSE_CODE}</h4>
                    <p>Para acceder a las lecciones, necesitas estar inscrito y haber completado el pago.</p>
                </div>
            </div>
        </div>
    `;
}

// Mostrar error de verificación
function showAccessError() {
    const loading = document.getElementById('asg-access-loading');
    if (loading) {
        loading.remove();
    }
    
    const errorHtml = `
        <div id="asg-access-error" class="access-error">
            <div class="error-content">
                <h2>⚠️ Error de Conexión</h2>
                <p>No pudimos verificar tu acceso al curso. Esto puede ser un problema temporal.</p>
                <div class="error-actions">
                    <button onclick="location.reload()" class="btn btn-primary">Reintentar</button>
                    <a href="/" class="btn btn-secondary">Volver al Inicio</a>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('afterbegin', errorHtml);
}

// Agregar mensaje de bienvenida
function addWelcomeMessage() {
    const welcomeHtml = `
        <div id="asg-welcome-banner" class="welcome-banner">
            <div class="welcome-content">
                <h3>🎉 ¡Bienvenido a tu curso!</h3>
                <p>Tienes acceso completo a todas las lecciones. ¡Comienza tu aprendizaje!</p>
                <button onclick="document.getElementById('asg-welcome-banner').remove()" class="close-welcome">×</button>
            </div>
        </div>
    `;
    
    const lessonsContent = document.querySelector('.lessons-content, .course-content, main');
    if (lessonsContent) {
        lessonsContent.insertAdjacentHTML('afterbegin', welcomeHtml);
        
        // Auto-remover después de 5 segundos
        setTimeout(() => {
            const banner = document.getElementById('asg-welcome-banner');
            if (banner) {
                banner.style.opacity = '0';
                setTimeout(() => banner.remove(), 300);
            }
        }, 5000);
    }
}

// Inicializar tracking de lecciones
function initializeLessonTracking() {
    if (!userEnrolled) return;
    
    console.log('ASG: Inicializando tracking de lecciones');
    
    // Buscar botones de "completar lección"
    const completeButtons = document.querySelectorAll('[data-lesson-id], .complete-lesson-btn, .lesson-complete');
    completeButtons.forEach(button => {
        button.addEventListener('click', handleLessonComplete);
    });
    
    // Buscar enlaces de lecciones para tracking de visualización
    const lessonLinks = document.querySelectorAll('.lesson-link, [href*="lesson"]');
    lessonLinks.forEach(link => {
        link.addEventListener('click', trackLessonView);
    });
}

// Manejar completar lección
async function handleLessonComplete(event) {
    const button = event.target;
    const lessonId = button.dataset.lessonId || button.getAttribute('data-lesson-id');
    
    if (!lessonId) {
        console.error('ASG: No se encontró lesson_id en el botón');
        return;
    }
    
    try {
        // Mostrar loading en el botón
        const originalText = button.textContent;
        button.textContent = 'Completando...';
        button.disabled = true;
        
        const response = await fetch(`/wp-json/asg/v1/lesson/${lessonId}/complete`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-WP-Nonce': wp.rest_nonce || ''
            }
        });
        
        const result = await response.json();
        
        if (result.success) {
            // Lección completada exitosamente
            button.textContent = '✅ Completada';
            button.classList.add('completed');
            
            // Mostrar notificación de éxito
            showLessonCompleteNotification(result.data);
            
            // Actualizar progreso visual si existe
            updateProgressBar(result.data.course_progress);
            
        } else {
            throw new Error(result.error || 'Error completando lección');
        }
        
    } catch (error) {
        console.error('ASG: Error completando lección:', error);
        button.textContent = originalText;
        button.disabled = false;
        
        // Mostrar error
        showErrorNotification('Error completando la lección: ' + error.message);
    }
}

// Tracking de visualización de lecciones
function trackLessonView(event) {
    const link = event.target;
    const lessonId = extractLessonIdFromUrl(link.href);
    
    if (lessonId) {
        console.log('ASG: Tracking visualización de lección:', lessonId);
        // Aquí podrías agregar analytics o tracking adicional
    }
}

// Extraer lesson_id de URL
function extractLessonIdFromUrl(url) {
    const match = url.match(/lesson[=\/](\d+)/i);
    return match ? match[1] : null;
}

// Mostrar notificación de lección completada
function showLessonCompleteNotification(data) {
    const notification = document.createElement('div');
    notification.className = 'asg-notification asg-success';
    notification.innerHTML = `
        <div class="notification-content">
            <h4>🎉 ¡Lección Completada!</h4>
            <p><strong>${data.lesson_title}</strong> ha sido marcada como completada.</p>
            ${data.course_progress ? `
                <div class="progress-info">
                    <p>Progreso del curso: ${data.course_progress.completed_lessons}/${data.course_progress.total_lessons} lecciones (${data.course_progress.progress_percentage}%)</p>
                </div>
            ` : ''}
            <button onclick="this.parentElement.parentElement.remove()" class="btn btn-sm btn-secondary">Cerrar</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remover después de 8 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 8000);
}

// Mostrar notificación de error
function showErrorNotification(message) {
    const notification = document.createElement('div');
    notification.className = 'asg-notification asg-error';
    notification.innerHTML = `
        <div class="notification-content">
            <h4>❌ Error</h4>
            <p>${message}</p>
            <button onclick="this.parentElement.parentElement.remove()" class="btn btn-sm btn-secondary">Cerrar</button>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// Actualizar barra de progreso
function updateProgressBar(progressData) {
    if (!progressData) return;
    
    const progressBars = document.querySelectorAll('.progress-bar, .course-progress');
    progressBars.forEach(bar => {
        const percentage = progressData.progress_percentage || 0;
        bar.style.width = percentage + '%';
        bar.textContent = `${percentage}%`;
        
        // Actualizar texto de progreso
        const progressText = bar.parentElement.querySelector('.progress-text');
        if (progressText) {
            progressText.textContent = `${progressData.completed_lessons}/${progressData.total_lessons} lecciones completadas`;
        }
    });
}

// Función global para completar lección (llamada desde HTML)
window.completeLesson = function(lessonId) {
    const event = {
        target: {
            dataset: { lessonId: lessonId.toString() }
        }
    };
    handleLessonComplete(event);
};

console.log('ASG Lessons Access Check initialized for course:', COURSE_CODE);

// Inyectar estilos CSS para la página de lecciones
function injectLessonsStyles() {
    if (document.getElementById('asg-lessons-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'asg-lessons-styles';
    styles.textContent = `
        /* ASG Lessons Access Styles */
        .access-loading, .access-denied, .access-error {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 60vh;
            padding: 40px 20px;
            text-align: center;
        }

        .access-loading {
            flex-direction: column;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .access-loading h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 24px;
        }

        .access-loading p {
            margin: 0;
            color: #666;
            font-size: 16px;
        }

        .denied-content, .error-content {
            max-width: 500px;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .access-denied .denied-content {
            border-top: 4px solid #dc3545;
        }

        .access-error .error-content {
            border-top: 4px solid #ffc107;
        }

        .denied-content h2, .error-content h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 28px;
        }

        .denied-content p, .error-content p {
            margin: 0 0 25px 0;
            color: #666;
            font-size: 16px;
            line-height: 1.5;
        }

        .denied-actions, .error-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 25px;
        }

        .course-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .course-info h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 18px;
        }

        .course-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            position: relative;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-content {
            text-align: center;
        }

        .welcome-content h3 {
            margin: 0 0 10px 0;
            font-size: 22px;
        }

        .welcome-content p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .close-welcome {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .close-welcome:hover {
            background: rgba(255,255,255,0.2);
        }

        /* Notification Styles */
        .asg-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        .asg-notification.asg-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .asg-notification.asg-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }
    `;

    document.head.appendChild(styles);
}

// Inyectar estilos al cargar
document.addEventListener('DOMContentLoaded', function() {
    injectLessonsStyles();
});

// Inyectar estilos CSS para la página de lecciones
function injectLessonsStyles() {
    if (document.getElementById('asg-lessons-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'asg-lessons-styles';
    styles.textContent = `
        /* ASG Lessons Access Styles */
        .access-loading, .access-denied, .access-error {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 60vh;
            padding: 40px 20px;
            text-align: center;
        }

        .access-loading {
            flex-direction: column;
        }

        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .access-loading h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 24px;
        }

        .access-loading p {
            margin: 0;
            color: #666;
            font-size: 16px;
        }

        .denied-content, .error-content {
            max-width: 500px;
            background: white;
            padding: 40px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }

        .access-denied .denied-content {
            border-top: 4px solid #dc3545;
        }

        .access-error .error-content {
            border-top: 4px solid #ffc107;
        }

        .denied-content h2, .error-content h2 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 28px;
        }

        .denied-content p, .error-content p {
            margin: 0 0 25px 0;
            color: #666;
            font-size: 16px;
            line-height: 1.5;
        }

        .denied-actions, .error-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            flex-wrap: wrap;
            margin-bottom: 25px;
        }

        .course-info {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .course-info h4 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 18px;
        }

        .course-info p {
            margin: 0;
            color: #666;
            font-size: 14px;
        }

        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            position: relative;
            animation: slideDown 0.5s ease-out;
        }

        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .welcome-content {
            text-align: center;
        }

        .welcome-content h3 {
            margin: 0 0 10px 0;
            font-size: 22px;
        }

        .welcome-content p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .close-welcome {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: background 0.2s ease;
        }

        .close-welcome:hover {
            background: rgba(255,255,255,0.2);
        }

        /* Notification Styles */
        .asg-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        .asg-notification.asg-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .asg-notification.asg-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .notification-content h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }

        .notification-content p {
            margin: 0 0 15px 0;
            font-size: 14px;
        }

        .progress-info {
            background: rgba(255,255,255,0.3);
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }

        .progress-info p {
            margin: 0;
            font-size: 13px;
            font-weight: 500;
        }

        /* Button Styles */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .btn-sm {
            padding: 8px 16px;
            font-size: 12px;
        }

        .btn-primary {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }

        .btn-primary:hover {
            background: #5a6fd8;
            transform: translateY(-1px);
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border-color: #6c757d;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-outline-secondary {
            background: transparent;
            color: #6c757d;
            border-color: #6c757d;
        }

        .btn-outline-secondary:hover {
            background: #6c757d;
            color: white;
        }

        /* Lesson Complete Button Styles */
        .complete-lesson-btn, .lesson-complete {
            background: #28a745;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .complete-lesson-btn:hover, .lesson-complete:hover {
            background: #218838;
            transform: translateY(-1px);
        }

        .complete-lesson-btn.completed, .lesson-complete.completed {
            background: #6c757d;
            cursor: default;
        }

        .complete-lesson-btn:disabled, .lesson-complete:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
        }

        /* Progress Bar Styles */
        .progress-bar {
            background: #667eea;
            height: 20px;
            border-radius: 10px;
            transition: width 0.5s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 500;
        }

        .course-progress {
            background: #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            margin: 10px 0;
        }

        .progress-text {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .denied-content, .error-content {
                margin: 20px;
                padding: 30px 20px;
            }

            .denied-actions, .error-actions {
                flex-direction: column;
                align-items: center;
            }

            .btn {
                width: 100%;
                max-width: 250px;
            }

            .asg-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }

            .welcome-banner {
                margin: 10px;
                padding: 15px;
            }
        }
    `;

    document.head.appendChild(styles);
}

// Inyectar estilos al cargar
document.addEventListener('DOMContentLoaded', function() {
    injectLessonsStyles();
});
