/**
 * ========================================
 * ASG OPTIMIZED API ENDPOINTS - V1 ENHANCED
 * ========================================
 * 
 * Intelligent API architecture with:
 * - Smart routing system
 * - Consolidated endpoints
 * - Resource-based organization
 * - Query optimization
 * - Bulk operations support
 * 
 * Version: 1.0.0 - OPTIMIZED ARCHITECTURE
 * Author: ING. Bryan Marc S.M
 * Compatibility: Maintains /v1 namespace
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * OPTIMIZED API ROUTES REGISTRATION
 * ========================================
 */
add_action('rest_api_init', function () {
    
    // ===== OPTIMIZED COURSES ENDPOINT =====
    register_rest_route('asg/v1', '/courses/api', array(
        'methods' => ['GET', 'POST'],
        'callback' => 'asg_optimized_courses_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== DRAFT COURSES ENDPOINT (must be before generic identifier route) =====
    register_rest_route('asg/v1', '/courses/api/draft', array(
        'methods' => 'GET',
        'callback' => 'asg_draft_courses_handler',
        'permission_callback' => '__return_true',
    ));

    register_rest_route('asg/v1', '/courses/api/(?P<identifier>[a-zA-Z0-9_-]+)', array(
        'methods' => ['GET', 'PUT', 'DELETE'],
        'callback' => 'asg_optimized_course_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== OPTIMIZED MODULES ENDPOINT =====
    register_rest_route('asg/v1', '/courses/(?P<course_id>[a-zA-Z0-9_-]+)/modules/api', array(
        'methods' => ['GET', 'POST', 'PUT'],
        'callback' => 'asg_optimized_modules_handler',
        'permission_callback' => '__return_true',
    ));

    register_rest_route('asg/v1', '/courses/(?P<course_id>[a-zA-Z0-9_-]+)/modules/api/(?P<module_id>\d+)', array(
        'methods' => ['GET', 'PUT', 'DELETE'],
        'callback' => 'asg_optimized_module_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== OPTIMIZED LESSONS ENDPOINT =====
    register_rest_route('asg/v1', '/courses/(?P<course_id>[a-zA-Z0-9_-]+)/modules/(?P<module_id>\d+)/lessons/api', array(
        'methods' => ['GET', 'POST', 'PUT'],
        'callback' => 'asg_optimized_lessons_handler',
        'permission_callback' => '__return_true',
    ));

    register_rest_route('asg/v1', '/courses/(?P<course_id>[a-zA-Z0-9_-]+)/modules/(?P<module_id>\d+)/lessons/(?P<lesson_id>\d+)/api', array(
        'methods' => ['GET', 'PUT', 'DELETE'],
        'callback' => 'asg_optimized_lesson_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== LESSON VIEWER ENDPOINT =====
    register_rest_route('asg/v1', '/lessons/(?P<lesson_id>\d+)', array(
        'methods' => 'GET',
        'callback' => 'asg_lesson_viewer_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== OPTIMIZED MEDIA ENDPOINT =====
    register_rest_route('asg/v1', '/media/api', array(
        'methods' => 'POST',
        'callback' => 'asg_optimized_media_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== OPTIMIZED DASHBOARD ENDPOINT =====
    register_rest_route('asg/v1', '/dashboard/api', array(
        'methods' => 'GET',
        'callback' => 'asg_optimized_dashboard_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== QUIZ VALIDATION ENDPOINT =====
    register_rest_route('asg/v1', '/quiz/validate', array(
        'methods' => 'POST',
        'callback' => 'asg_quiz_validation_handler',
        'permission_callback' => '__return_true',
    ));

    // ===== QUIZ SCORING ENDPOINT =====
    register_rest_route('asg/v1', '/quiz/score', array(
        'methods' => 'POST',
        'callback' => 'asg_quiz_scoring_handler',
        'permission_callback' => '__return_true',
    ));
	
	// ===== LESSON COMMENTS ENDPOINT 'comentarios' =====
	register_rest_route('asg/v1', '/lessons/(?P<lesson_id>\d+)/comments', array(
		'methods' => ['GET', 'POST'],
		'callback' => 'asg_lesson_comments_handler',
		'permission_callback' => function () {
			// Solo usuarios logueados pueden enviar comentarios (GET es público)
			return true;
		}
	));

    // ===== STUDENT PROGRESS ENDPOINTS =====

    // Endpoint para obtener progreso del usuario
    register_rest_route('asg/v1', '/my-courses', array(
        'methods' => 'GET',
        'callback' => 'asg_my_courses_handler',
        'permission_callback' => function () {
            return is_user_logged_in();
        }
    ));

    // Endpoint para completar lecciones
    register_rest_route('asg/v1', '/complete-lesson', array(
        'methods' => 'POST',
        'callback' => 'asg_complete_lesson_handler',
        'permission_callback' => function () {
            return is_user_logged_in();
        }
    ));

    // Endpoint para verificar enrollment
    register_rest_route('asg/v1', '/check-enrollment', array(
        'methods' => 'GET',
        'callback' => 'asg_check_enrollment_handler',
        'permission_callback' => function () {
            return is_user_logged_in();
        }
    ));

});

/**
 * ========================================
 * SMART QUERY BUILDER CLASS
 * ========================================
 */
class ASG_Smart_Query {
    private $wpdb;
    
    public function __construct() {
        global $wpdb;
        $this->wpdb = $wpdb;
    }
    
    /**
     * Build intelligent course queries with eager loading
     */
    public function courses($params = array()) {
        $select = "SELECT c.*";
        $from = "FROM wpic_courses c";
        $where = "WHERE c.status_course != 'archived' AND c.is_deleted = 0";
        $values = array();
        
        // Smart filtering
        if (!empty($params['category'])) {
            $where .= " AND c.category_course = %s";
            $values[] = $params['category'];
        }
        
        if (!empty($params['status'])) {
            $where .= " AND c.status_course = %s";
            $values[] = $params['status'];
        }
        
        if (!empty($params['search'])) {
            $where .= " AND (c.name_course LIKE %s OR c.description_course LIKE %s)";
            $search_term = '%' . $this->wpdb->esc_like($params['search']) . '%';
            $values[] = $search_term;
            $values[] = $search_term;
        }
        
        // Smart ordering
        $order = "ORDER BY c.created_at DESC";
        if (!empty($params['order_by'])) {
            $allowed_orders = ['created_at', 'name_course', 'price_course', 'updated_at'];
            if (in_array($params['order_by'], $allowed_orders)) {
                $direction = (!empty($params['order']) && $params['order'] === 'asc') ? 'ASC' : 'DESC';
                $order = "ORDER BY c.{$params['order_by']} {$direction}";
            }
        }
        
        // Smart pagination
        $limit = "";
        if (!empty($params['per_page'])) {
            $per_page = intval($params['per_page']);
            $page = intval($params['page'] ?? 1);
            $offset = ($page - 1) * $per_page;
            $limit = "LIMIT {$per_page} OFFSET {$offset}";
        }
        
        $query = "{$select} {$from} {$where} {$order} {$limit}";

        // Obtener cursos sin JOIN
        if (empty($values)) {
            $courses = $this->wpdb->get_results($query, ARRAY_A);
        } else {
            $courses = $this->wpdb->get_results($this->wpdb->prepare($query, $values), ARRAY_A);
        }

        // Agregar imágenes por separado para cada curso
        foreach ($courses as &$course) {
            $image = $this->wpdb->get_row($this->wpdb->prepare(
                "SELECT thumbnail_url, medium_url, large_url
                 FROM wpic_course_images
                 WHERE course_id = %d AND is_active = 1
                 ORDER BY id DESC LIMIT 1",
                $course['id_course']
            ), ARRAY_A);

            $course['thumbnail_url'] = $image['thumbnail_url'] ?? null;
            $course['medium_url'] = $image['medium_url'] ?? null;
            $course['large_url'] = $image['large_url'] ?? null;
        }

        return $courses;
    }
    
    /**
     * Get course with smart identifier detection (ID or code)
     */
    public function course_by_identifier($identifier, $include = array()) {
        // Detect if identifier is numeric (ID) or string (code)
        $is_id = is_numeric($identifier);
        
        $field = $is_id ? 'id_course' : 'code_course';
        $value = $is_id ? intval($identifier) : sanitize_text_field($identifier);
        
        $query = "
            SELECT c.*, img.thumbnail_url, img.medium_url, img.large_url
            FROM wpic_courses c
            LEFT JOIN wpic_course_images img ON c.id_course = img.course_id AND img.is_active = 1 AND img.file_type = 'course_image' AND img.id = (SELECT MAX(id) FROM wpic_course_images WHERE course_id = c.id_course AND is_active = 1 AND file_type = 'course_image')
            WHERE c.{$field} = %s AND c.status_course != 'archived'
        ";
        
        $course = $this->wpdb->get_row($this->wpdb->prepare($query, $value), ARRAY_A);
        
        if (!$course) {
            return null;
        }
        
        // Smart eager loading
        if (in_array('modules', $include)) {
            $course['modules'] = $this->course_modules($course['id_course'], $include);
        }
        
        if (in_array('objectives', $include)) {
            // Objectives and benefits are already in the course data from wpic_courses table
            $course['objectives_list'] = $this->parse_objectives($course['objectives'] ?? '');
            $course['benefits_list'] = $this->parse_objectives($course['benefits'] ?? '');
        }
        
        if (in_array('stats', $include)) {
            $course['stats'] = $this->course_stats($course['id_course']);
        }
        
        return $course;
    }
    
    /**
     * Get course modules with optional lesson inclusion (HYBRID APPROACH)
     */
    public function course_modules($course_id, $include = array()) {
        // Check if course_id column exists in wpic_modules (hybrid approach)
        $has_course_id_column = $this->check_course_id_column();

        if ($has_course_id_column) {
            // NEW WAY: Use direct course_id relationship (more efficient)
            $modules = $this->wpdb->get_results($this->wpdb->prepare(
                "SELECT * FROM wpic_modules WHERE course_id = %d AND is_deleted = 0 ORDER BY order_module ASC",
                $course_id
            ), ARRAY_A);

            error_log("ASG Hybrid: Using course_id for modules query (efficient)");
        } else {
            // OLD WAY: Fallback to code_course relationship (compatibility)
            $course_code = $this->wpdb->get_var($this->wpdb->prepare(
                "SELECT code_course FROM wpic_courses WHERE id_course = %d",
                $course_id
            ));

            if (!$course_code) return array();

            $modules = $this->wpdb->get_results($this->wpdb->prepare(
                "SELECT * FROM wpic_modules WHERE code_course = %s AND is_deleted = 0 ORDER BY order_module ASC",
                $course_code
            ), ARRAY_A);

            error_log("ASG Hybrid: Using code_course for modules query (fallback)");
        }

        // Smart lesson loading and counting
        foreach ($modules as &$module) {
            $module_id = isset($module['id_modules']) ? $module['id_modules'] : null;

            // Agregar imágenes del módulo
            $module_image = $this->wpdb->get_row($this->wpdb->prepare(
                "SELECT thumbnail_url, medium_url, large_url
                 FROM wpic_course_images
                 WHERE course_id = %d
                 AND file_type = 'module_cover'
                 AND is_active = 1
                 ORDER BY id DESC LIMIT 1",
                $course_id
            ), ARRAY_A);

            $module['module_thumbnail_url'] = $module_image['thumbnail_url'] ?? null;
            $module['module_medium_url'] = $module_image['medium_url'] ?? null;
            $module['module_large_url'] = $module_image['large_url'] ?? null;

            if (in_array('lessons', $include)) {
                // Load full lessons data
                $module['lessons'] = $this->module_lessons($module['code_module'], $module_id, $course_id);
                $module['lessons_count'] = count($module['lessons']);
            } else {
                // Just get the count without loading full lesson data (more efficient)
                $module['lessons_count'] = $this->get_module_lessons_count($module['code_module'], $module_id);
            }
        }

        return $modules;
    }
    
    /**
     * Get module lessons (HYBRID APPROACH)
     */
    public function module_lessons($module_code, $module_id = null, $course_id = null) {
        // Check if module_id column exists in wpic_lessons (hybrid approach)
        $has_module_id_column = $this->check_lessons_module_id_column();

        if ($has_module_id_column && $module_id) {
            // NEW WAY: Use direct module_id relationship (more efficient)
            $lessons = $this->wpdb->get_results($this->wpdb->prepare(
                "SELECT * FROM wpic_lessons WHERE module_id = %d AND is_deleted = 0 ORDER BY order_lesson ASC",
                $module_id
            ), ARRAY_A);

            error_log("ASG Hybrid: Using module_id for lessons query (efficient)");
        } else {
            // OLD WAY: Fallback to code_module relationship (compatibility)
            $lessons = $this->wpdb->get_results($this->wpdb->prepare(
                "SELECT * FROM wpic_lessons WHERE code_module = %s AND is_deleted = 0 ORDER BY order_lesson ASC",
                $module_code
            ), ARRAY_A);

            error_log("ASG Hybrid: Using code_module for lessons query (fallback)");
        }

        // Agregar imágenes a cada lección
        foreach ($lessons as &$lesson) {
            if ($course_id) {
                // Usar course_id directamente si está disponible
                $image = $this->wpdb->get_row($this->wpdb->prepare(
                    "SELECT thumbnail_url, medium_url, large_url
                     FROM wpic_course_images
                     WHERE course_id = %d
                     AND file_type = 'lesson_cover'
                     AND is_active = 1
                     ORDER BY id DESC LIMIT 1",
                    $course_id
                ), ARRAY_A);
            } else {
                // Fallback usando code_course
                $image = $this->wpdb->get_row($this->wpdb->prepare(
                    "SELECT thumbnail_url, medium_url, large_url
                     FROM wpic_course_images
                     WHERE course_id = (SELECT id_course FROM wpic_courses WHERE code_course = %s)
                     AND file_type = 'lesson_cover'
                     AND is_active = 1
                     ORDER BY id DESC LIMIT 1",
                    $lesson['code_course']
                ), ARRAY_A);
            }

            $lesson['lesson_thumbnail_url'] = $image['thumbnail_url'] ?? null;
            $lesson['lesson_medium_url'] = $image['medium_url'] ?? null;
            $lesson['lesson_large_url'] = $image['large_url'] ?? null;
        }

        return $lessons;
    }

    /**
     * Get module lessons count only (HYBRID APPROACH) - More efficient than loading full lessons
     */
    public function get_module_lessons_count($module_code, $module_id = null) {
        // Check if module_id column exists in wpic_lessons (hybrid approach)
        $has_module_id_column = $this->check_lessons_module_id_column();

        if ($has_module_id_column && $module_id) {
            // NEW WAY: Use direct module_id relationship (more efficient)
            $count = $this->wpdb->get_var($this->wpdb->prepare(
                "SELECT COUNT(*) FROM wpic_lessons WHERE module_id = %d AND is_deleted = 0",
                $module_id
            ));

            error_log("ASG Hybrid: Using module_id for lessons count query (efficient)");
        } else {
            // OLD WAY: Fallback to code_module relationship (compatibility)
            $count = $this->wpdb->get_var($this->wpdb->prepare(
                "SELECT COUNT(*) FROM wpic_lessons WHERE code_module = %s AND is_deleted = 0",
                $module_code
            ));

            error_log("ASG Hybrid: Using code_module for lessons count query (fallback)");
        }

        return intval($count);
    }

    /**
     * Parse objectives/benefits string into array
     */
    public function parse_objectives($objectives_string) {
        if (empty($objectives_string)) {
            return array();
        }

        $items = explode('|', $objectives_string);
        $parsed = array();

        foreach ($items as $index => $item) {
            $item = trim($item);
            if (!empty($item)) {
                $parsed[] = array(
                    'id' => $index + 1,
                    'text' => $item,
                    'order' => $index + 1
                );
            }
        }

        return $parsed;
    }
    
    /**
     * Get course statistics
     */
    public function course_stats($course_id) {
        $course_code = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT code_course FROM wpic_courses WHERE id_course = %d", 
            $course_id
        ));
        
        if (!$course_code) return array();
        
        $modules_count = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM wpic_modules WHERE code_course = %s AND is_deleted = 0",
            $course_code
        ));
        
        $lessons_count = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT COUNT(*) FROM wpic_lessons WHERE code_course = %s AND is_deleted = 0",
            $course_code
        ));
        
        return array(
            'modules_count' => intval($modules_count),
            'lessons_count' => intval($lessons_count),
            'total_duration' => $this->calculate_course_duration($course_code)
        );
    }
    
    /**
     * Calculate total course duration
     */
    private function calculate_course_duration($course_code) {
        $total = $this->wpdb->get_var($this->wpdb->prepare(
            "SELECT SUM(duration_lesson) FROM wpic_lessons WHERE code_course = %s AND is_deleted = 0",
            $course_code
        ));

        return intval($total ?: 0);
    }

    /**
     * Check if course_id column exists in wpic_modules table (HYBRID APPROACH)
     */
    private function check_course_id_column() {
        static $has_column = null;

        if ($has_column === null) {
            $columns = $this->wpdb->get_results("SHOW COLUMNS FROM wpic_modules LIKE 'course_id'");
            $has_column = !empty($columns);

            error_log("ASG Hybrid: course_id column exists in wpic_modules: " . ($has_column ? 'YES' : 'NO'));
        }

        return $has_column;
    }

    /**
     * Check if module_id column exists in wpic_lessons table (HYBRID APPROACH)
     */
    private function check_lessons_module_id_column() {
        static $has_column = null;

        if ($has_column === null) {
            $columns = $this->wpdb->get_results("SHOW COLUMNS FROM wpic_lessons LIKE 'module_id'");
            $has_column = !empty($columns);

            error_log("ASG Hybrid: module_id column exists in wpic_lessons: " . ($has_column ? 'YES' : 'NO'));
        }

        return $has_column;
    }
}

// Initialize smart query builder
if (!isset($GLOBALS['asg_smart_query'])) {
    $GLOBALS['asg_smart_query'] = new ASG_Smart_Query();
}
$asg_smart_query = $GLOBALS['asg_smart_query'];

/**
 * ========================================
 * HELPER FUNCTIONS
 * ========================================
 */

/**
 * Get or create smart query builder instance
 */
function asg_get_smart_query() {
    if (!isset($GLOBALS['asg_smart_query'])) {
        $GLOBALS['asg_smart_query'] = new ASG_Smart_Query();
    }
    return $GLOBALS['asg_smart_query'];
}

/**
 * ========================================
 * SMART HANDLER FUNCTIONS
 * ========================================
 */

/**
 * Optimized courses handler - handles multiple operations
 */
function asg_optimized_courses_handler($request) {
    global $asg_smart_query;
    $method = $request->get_method();

    try {
        switch ($method) {
            case 'GET':
                return asg_optimized_get_courses($request);
            case 'POST':
                return asg_optimized_create_course($request);
            default:
                return new WP_Error('method_not_allowed', 'Method not allowed', ['status' => 405]);
        }
    } catch (Exception $e) {
        error_log('ASG Optimized Courses Error: ' . $e->getMessage());
        return new WP_Error('server_error', 'Internal server error', ['status' => 500]);
    }
}

/**
 * Optimized individual course handler
 */
function asg_optimized_course_handler($request) {
    global $asg_smart_query;
    $method = $request->get_method();
    $identifier = $request['identifier'];

    error_log("ASG Course Handler: Method=$method, Identifier=$identifier");

    try {
        switch ($method) {
            case 'GET':
                error_log("ASG Course Handler: Calling GET for $identifier");
                return asg_optimized_get_course($request, $identifier);
            case 'PUT':
                error_log("ASG Course Handler: Calling PUT for $identifier");
                $result = asg_optimized_update_course($request, $identifier);
                error_log("ASG Course Handler: PUT result: " . json_encode($result));
                return $result;
            case 'DELETE':
                error_log("ASG Course Handler: Calling DELETE for $identifier");
                return asg_optimized_delete_course($request, $identifier);
            default:
                error_log("ASG Course Handler: Method not allowed: $method");
                return new WP_Error('method_not_allowed', 'Method not allowed', ['status' => 405]);
        }
    } catch (Exception $e) {
        error_log('ASG Course Handler Exception: ' . $e->getMessage());
        error_log('ASG Course Handler Stack: ' . $e->getTraceAsString());
        return new WP_Error('server_error', 'Internal server error', ['status' => 500]);
    }
}

/**
 * Get courses with intelligent filtering and pagination
 */
function asg_optimized_get_courses($request) {
    try {
        $asg_smart_query = asg_get_smart_query();

        $params = array(
            'category' => $request->get_param('category'),
            'status' => $request->get_param('status'),
            'search' => $request->get_param('search'),
            'order_by' => $request->get_param('order_by'),
            'order' => $request->get_param('order'),
            'page' => $request->get_param('page') ?: 1,
            'per_page' => $request->get_param('per_page') ?: 10,
        );

        // Intelligent include parameter
        $include = $request->get_param('include');
        if ($include) {
            $include = explode(',', $include);
        } else {
            $include = array();
        }

        $courses = $asg_smart_query->courses($params);

    } catch (Exception $e) {
        error_log('ASG Optimized Courses Error: ' . $e->getMessage());
        error_log('ASG Error Stack: ' . $e->getTraceAsString());
        return new WP_Error('courses_error', 'Error loading courses: ' . $e->getMessage(), ['status' => 500]);
    }

    // Add related data if requested
    if (in_array('stats', $include)) {
        foreach ($courses as &$course) {
            $course['stats'] = $asg_smart_query->course_stats($course['id_course']);
        }
    }

    // Get categories if requested
    $categories = array();
    if ($request->get_param('include_categories')) {
        $categories = asg_get_optimized_categories();
    }

    return new WP_REST_Response(array(
        'success' => true,
        'data' => $courses,
        'categories' => $categories,
        'pagination' => array(
            'page' => intval($params['page']),
            'per_page' => intval($params['per_page']),
            'total' => count($courses)
        )
    ), 200);
}

/**
 * Get single course with intelligent identifier detection
 */
function asg_optimized_get_course($request, $identifier) {
    $asg_smart_query = asg_get_smart_query();

    // Intelligent include parameter
    $include = $request->get_param('include');
    if ($include) {
        $include = explode(',', $include);
    } else {
        $include = array('modules', 'lessons', 'objectives');
    }

    try {
        $course = $asg_smart_query->course_by_identifier($identifier, $include);
    } catch (Exception $e) {
        error_log('ASG Optimized Course Error: ' . $e->getMessage());
        return new WP_Error('course_error', 'Error loading course: ' . $e->getMessage(), ['status' => 500]);
    }

    if (!$course) {
        return new WP_Error('course_not_found', 'Course not found', ['status' => 404]);
    }

    return new WP_REST_Response(array(
        'success' => true,
        'data' => $course
    ), 200);
}

/**
 * Create course with intelligent validation and image handling
 */
function asg_optimized_create_course($request) {
    global $wpdb;

    try {
        error_log('ASG: Starting optimized course creation');
        
        // Get data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }
        
        error_log('ASG: Received data: ' . print_r($data, true));
        error_log('ASG: cover_img value: ' . ($data['cover_img'] ?? 'NOT SET'));
        error_log('ASG: image_record_id value: ' . ($data['image_record_id'] ?? 'NOT SET'));
        error_log('ASG: image_record_id isset: ' . (isset($data['image_record_id']) ? 'YES' : 'NO'));
        error_log('ASG: image_record_id empty: ' . (empty($data['image_record_id']) ? 'YES' : 'NO'));

        // Validate required fields
        $required_fields = ['name_course', 'description_course', 'category_course'];
        foreach ($required_fields as $field) {
            if (empty($data[$field])) {
                return new WP_REST_Response([
                    'success' => false,
                    'error' => "Field '$field' is required"
                ], 400);
            }
        }

        // Generate unique course code
        $course_code = 'course_' . strtolower(
            str_replace(' ', '_', sanitize_text_field($data['name_course']))
        ) . '_' . time();

        // Prepare course data
        $course_data = [
            'code_course' => $course_code,
            'name_course' => sanitize_text_field($data['name_course']),
            'description_course' => sanitize_textarea_field($data['description_course']),
            'category_course' => sanitize_text_field($data['category_course']),
            'language_course' => sanitize_text_field($data['language_course'] ?? 'es'),
            'price_course' => floatval($data['price_course'] ?? 0),
            'duration_course' => floatval($data['duration_course'] ?? 0),
            'status_course' => sanitize_text_field($data['status_course'] ?? 'draft'),
            'cover_img' => esc_url_raw($data['cover_img'] ?? ''), // ← AGREGADO
            'objectives' => sanitize_textarea_field($data['objectives'] ?? ''), // ← AGREGADO
            'benefits' => sanitize_textarea_field($data['benefits'] ?? ''), // ← AGREGADO
            'id_user' => get_current_user_id() ?: 1, // ← AGREGADO
            'is_deleted' => 0, // ← AGREGADO
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        // Start transaction
        $wpdb->query('START TRANSACTION');

        // Insert course with proper formats
        $result = $wpdb->insert(
            'wpic_courses',
            $course_data,
            ['%s', '%s', '%s', '%s', '%s', '%f', '%f', '%s', '%s', '%s', '%s', '%d', '%d', '%s', '%s']
        );

        if ($result === false) {
            $wpdb->query('ROLLBACK');
            error_log('ASG: Database error: ' . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        $course_id = $wpdb->insert_id;
        $course_data['id_course'] = $course_id;

        error_log("ASG: Course created with ID: $course_id");
        error_log("ASG: Course data saved: " . json_encode($course_data));

        // Handle image if provided
        error_log("ASG: === IMAGE ASSOCIATION DEBUG ===");
        error_log("ASG: Checking image_record_id: " . (isset($data['image_record_id']) ? $data['image_record_id'] : 'NOT SET'));
        error_log("ASG: image_record_id empty check: " . (empty($data['image_record_id']) ? 'EMPTY' : 'NOT EMPTY'));
        error_log("ASG: About to check if (!empty(\$data['image_record_id']))");

        if (!empty($data['image_record_id'])) {
            error_log("ASG: ✅ INSIDE image_record_id condition");
            $image_id = intval($data['image_record_id']);
            error_log("ASG: Associating image $image_id with course $course_id");

            // Check if image exists first
            $image_exists = $wpdb->get_var($wpdb->prepare(
                "SELECT COUNT(*) FROM wpic_course_images WHERE id = %d",
                $image_id
            ));

            error_log("ASG: Image $image_id exists: " . ($image_exists ? 'YES' : 'NO'));

            if ($image_exists) {
                $image_result = asg_optimized_associate_image_with_course($image_id, $course_id);
                error_log("ASG: Image association result: " . ($image_result ? 'SUCCESS' : 'FAILED'));

                if (!$image_result) {
                    $wpdb->query('ROLLBACK');
                    return new WP_REST_Response([
                        'success' => false,
                        'error' => 'Failed to associate image with course'
                    ], 500);
                }
            } else {
                error_log("ASG: Image $image_id does not exist in database");
            }
        } else {
            error_log("ASG: ❌ NO image_record_id provided, skipping image association");
            error_log("ASG: image_record_id raw value: " . var_export($data['image_record_id'] ?? 'UNDEFINED', true));
        }

        // Commit transaction
        $wpdb->query('COMMIT');

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Course created successfully',
            'data' => $course_data
        ], 201);

    } catch (Exception $e) {
        $wpdb->query('ROLLBACK');
        error_log('ASG: Exception in course creation: ' . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Server error: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Update course with intelligent operations
 */
function asg_optimized_update_course($request, $identifier) {
    global $wpdb;

    error_log("ASG Optimized Update: Starting update for identifier: $identifier");

    // Detect ID vs code
    $is_id = is_numeric($identifier);
    $field = $is_id ? 'id_course' : 'code_course';
    $value = $is_id ? intval($identifier) : sanitize_text_field($identifier);

    error_log("ASG Optimized Update: is_id=$is_id, field=$field, value=$value");

    // Get course ID for update function
    if (!$is_id) {
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id_course FROM wpic_courses WHERE code_course = %s",
            $value
        ));

        error_log("ASG Optimized Update: Found course_id=$course_id for code=$value");

        if (!$course_id) {
            error_log("ASG Optimized Update: Course not found for code=$value");
            return new WP_Error('course_not_found', 'Course not found', ['status' => 404]);
        }
    } else {
        $course_id = $value;
    }

    error_log("ASG Optimized Update: Using course_id=$course_id");

    // Check for special actions
    $action = $request->get_param('action');

    switch ($action) {
        case 'archive':
            return asg_optimized_archive_course($course_id);
        case 'publish':
            return asg_optimized_publish_course($course_id);
        case 'duplicate':
            return asg_optimized_duplicate_course($course_id);
        default:
            // Direct update implementation
            return asg_optimized_direct_update_course($course_id, $request);
    }
}

/**
 * Direct course update implementation
 */
function asg_optimized_direct_update_course($course_id, $request) {
    global $wpdb;

    try {
        error_log("ASG Direct Update: Starting for course_id=$course_id");

        // Get JSON data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }


        // Validate course exists (same as test)
        $existing_course = $wpdb->get_row($wpdb->prepare(
            "SELECT id_course, code_course FROM wpic_courses WHERE id_course = %d",
            $course_id
        ));

        if (!$existing_course) {
            error_log("ASG Direct Update: Course not found with ID $course_id");
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Course not found'
            ], 404);
        }

        error_log("ASG Direct Update: Course exists, proceeding with update");

        // Start with simple update like test endpoint
        $update_data = ['updated_at' => current_time('mysql')];
        $update_format = ['%s'];

        // Add only safe fields one by one (only existing columns)
        $safe_fields = [
            'name_course' => '%s',
            'description_course' => '%s',
            'category_course' => '%s',
            'language_course' => '%s',
            'price_course' => '%f',
            'duration_course' => '%f',
            'objectives' => '%s',
            'benefits' => '%s',
            'cover_img' => '%s',
            'status_course' => '%s',
            'is_deleted' => '%d'
        ];

        foreach ($safe_fields as $field => $format) {
            // Special handling for objectives and benefits - allow empty values
            $should_process = false;
            if ($field === 'objectives' || $field === 'benefits') {
                $should_process = isset($data[$field]); // Process even if empty
            } else {
                $should_process = isset($data[$field]) && $data[$field] !== '';
            }

            if ($should_process) {
                if ($format === '%f') {
                    $update_data[$field] = floatval($data[$field]);
                } elseif ($format === '%d') {
                    $update_data[$field] = intval($data[$field]);
                } else {
                    $update_data[$field] = sanitize_text_field($data[$field]);
                }
                $update_format[] = $format;
                error_log("ASG Direct Update: Adding $field = " . $update_data[$field]);
            } else {
                error_log("ASG Direct Update: Field $field is missing or empty in data");
            }
        }

        error_log("ASG Direct Update: Final update data: " . json_encode($update_data));

        // Perform update (same as test)
        $result = $wpdb->update(
            'wpic_courses',
            $update_data,
            ['id_course' => $course_id],
            $update_format,
            ['%d']
        );

        if ($result === false) {
            error_log("ASG Direct Update: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        error_log("ASG Direct Update: Successfully updated $result rows");

        // Check if this is a course restoration (is_deleted changed from 1 to 0)
        if (isset($data['is_deleted']) && intval($data['is_deleted']) === 0) {
            error_log("ASG Direct Update: Detected course restoration, checking if modules/lessons need restoration");

            // Get course code for related content restoration
            $course_code = $existing_course->code_course;

            // Restore related modules
            $modules_restored = $wpdb->update(
                'wpic_modules',
                ['is_deleted' => 0, 'updated_at' => current_time('mysql')],
                ['code_course' => $course_code, 'is_deleted' => 1],
                ['%d', '%s'],
                ['%s', '%d']
            );

            // Restore related lessons
            $lessons_restored = $wpdb->update(
                'wpic_lessons',
                ['is_deleted' => 0, 'updated_at' => current_time('mysql')],
                ['code_course' => $course_code, 'is_deleted' => 1],
                ['%d', '%s'],
                ['%s', '%d']
            );

            error_log("ASG Direct Update: Restoration cascada completed - Modules restored: $modules_restored, Lessons restored: $lessons_restored");
        }

        // Handle image association if cover_img was updated
        if (isset($data['cover_img']) && isset($data['image_record_id'])) {
            $image_id = intval($data['image_record_id']);
            if ($image_id > 0) {
                asg_optimized_associate_image_with_course($image_id, $course_id);
            }
        }

        // Prepare response data
        $response_data = [
            'course_id' => $course_id,
            'updated_fields' => array_keys($update_data),
            'rows_affected' => $result
        ];

        // Add restoration info if applicable
        if (isset($modules_restored) && isset($lessons_restored)) {
            $response_data['restoration'] = [
                'modules_restored' => $modules_restored,
                'lessons_restored' => $lessons_restored
            ];
        }

        // Return success response
        return new WP_REST_Response([
            'success' => true,
            'message' => isset($modules_restored) ? 'Course and related content restored successfully' : 'Course updated successfully',
            'data' => $response_data
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Direct Update: Exception: " . $e->getMessage());
        error_log("ASG Direct Update: Exception trace: " . $e->getTraceAsString());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Delete course (intelligent archive)
 */
function asg_optimized_delete_course($request, $identifier) {
    global $wpdb;

    try {
        error_log("ASG Delete Course: Starting for identifier=$identifier");

        // Convert identifier to ID
        $is_id = is_numeric($identifier);
        if (!$is_id) {
            $course_id = $wpdb->get_var($wpdb->prepare(
                "SELECT id_course FROM wpic_courses WHERE code_course = %s",
                $identifier
            ));
        } else {
            $course_id = intval($identifier);
        }

        if (!$course_id) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Course not found'
            ], 404);
        }

        // Verify course exists and is not already deleted
        $course = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_courses WHERE id_course = %d AND is_deleted = 0",
            $course_id
        ));

        if (!$course) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Course not found or already deleted'
            ], 404);
        }

        // Soft delete: Change status to draft and mark as deleted
        $result = $wpdb->update(
            'wpic_courses',
            [
                'is_deleted' => 1,
                'status_course' => 'draft'
            ],
            ['id_course' => $course_id],
            ['%d', '%s'],
            ['%d']
        );

        if ($result === false) {
            error_log("ASG Delete Course: Database error - " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Failed to delete course'
            ], 500);
        }

        // Also soft delete related modules and lessons
        $wpdb->update(
            'wpic_modules',
            ['is_deleted' => 1],
            ['code_course' => $course->code_course],
            ['%d'],
            ['%s']
        );

        $wpdb->update(
            'wpic_lessons',
            ['is_deleted' => 1],
            ['code_course' => $course->code_course],
            ['%d'],
            ['%s']
        );

        error_log("ASG Delete Course: Successfully changed course ID $course_id to draft status");

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Course moved to draft successfully',
            'course_id' => $course_id,
            'new_status' => 'draft'
        ]);

    } catch (Exception $e) {
        error_log("ASG Delete Course: Exception - " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Internal server error'
        ], 500);
    }
}

/**
 * Draft courses handler - Get only draft/deleted courses for admin interface
 */
function asg_draft_courses_handler($request) {
    try {
        global $wpdb;

        error_log("ASG Draft Courses: Getting draft courses for admin interface");

        // First, let's check what courses exist with draft status
        $debug_courses = $wpdb->get_results(
            "SELECT id_course, name_course, status_course, is_deleted
             FROM wpic_courses
             WHERE status_course = 'draft' OR is_deleted = 1",
            ARRAY_A
        );

        error_log("ASG Draft Courses DEBUG: Found " . count($debug_courses) . " courses with draft status or deleted");
        foreach ($debug_courses as $course) {
            error_log("ASG Draft Courses DEBUG: Course '{$course['name_course']}' - status: {$course['status_course']}, is_deleted: {$course['is_deleted']}");
        }

        // Get all courses that are marked as deleted or have draft status
        $courses = $wpdb->get_results(
            "SELECT c.*,
                    img.thumbnail_url,
                    img.medium_url,
                    img.large_url
             FROM wpic_courses c
             LEFT JOIN wpic_course_images img ON c.id_course = img.course_id
                 AND img.is_active = 1
                 AND img.file_type = 'course_image'
                 AND img.id = (
                     SELECT MAX(id)
                     FROM wpic_course_images
                     WHERE course_id = c.id_course
                     AND is_active = 1
                     AND file_type = 'course_image'
                 )
             WHERE (c.status_course = 'draft' OR c.is_deleted = 1)
             AND c.status_course != 'archived'
             ORDER BY c.updated_at DESC",
            ARRAY_A
        );

        error_log("ASG Draft Courses: Found " . count($courses) . " draft courses with images");

        // Check for SQL errors
        if ($wpdb->last_error) {
            error_log("ASG Draft Courses SQL Error: " . $wpdb->last_error);
        }

        // Add stats if requested
        $include = $request->get_param('include');
        if ($include && strpos($include, 'stats') !== false) {
            $asg_smart_query = asg_get_smart_query();
            foreach ($courses as &$course) {
                $course['stats'] = $asg_smart_query->course_stats($course['id_course']);
            }
        }

        // Get categories if requested
        $categories = array();
        if ($request->get_param('include_categories')) {
            $categories = asg_get_optimized_categories();
        }

        return new WP_REST_Response(array(
            'success' => true,
            'data' => $courses,
            'categories' => $categories,
            'pagination' => array(
                'page' => 1,
                'per_page' => count($courses),
                'total' => count($courses)
            )
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Draft Courses Error: ' . $e->getMessage());
        return new WP_Error('draft_courses_error', 'Error loading draft courses: ' . $e->getMessage(), ['status' => 500]);
    }
}

/**
 * Get optimized categories from existing courses
 */
function asg_get_optimized_categories() {
    global $wpdb;

    $categories = $wpdb->get_results(
        "SELECT
            category_course as category,
            COUNT(*) as course_count
         FROM wpic_courses
         WHERE status_course != 'archived'
           AND category_course IS NOT NULL
           AND category_course != ''
         GROUP BY category_course
         ORDER BY course_count DESC, category_course ASC",
        ARRAY_A
    );

    return $categories;
}

/**
 * Optimized modules handler
 */
function asg_optimized_modules_handler($request) {
    $method = $request->get_method();
    $course_id = $request['course_id'];

    try {
        switch ($method) {
            case 'GET':
                return asg_optimized_get_modules($request, $course_id);
            case 'POST':
                return asg_create_module($request, $course_id);
            case 'PUT':
                return asg_optimized_bulk_update_modules($request, $course_id);
            default:
                return new WP_Error('method_not_allowed', 'Method not allowed', ['status' => 405]);
        }
    } catch (Exception $e) {
        error_log('ASG Optimized Modules Error: ' . $e->getMessage());
        return new WP_Error('server_error', 'Internal server error', ['status' => 500]);
    }
}

/**
 * Optimized individual module handler
 */
function asg_optimized_module_handler($request) {
    $method = $request->get_method();
    $course_id = $request['course_id'];
    $module_id = $request['module_id'];

    try {
        switch ($method) {
            case 'GET':
                return asg_optimized_get_module($request, $course_id, $module_id);
            case 'PUT':
                return asg_optimized_update_module($request, $course_id, $module_id);
            case 'DELETE':
                return asg_optimized_delete_module($request, $course_id, $module_id);
            default:
                return new WP_Error('method_not_allowed', 'Method not allowed', ['status' => 405]);
        }
    } catch (Exception $e) {
        error_log('ASG Optimized Module Error: ' . $e->getMessage());
        return new WP_Error('server_error', 'Internal server error', ['status' => 500]);
    }
}

/**
 * Get modules with intelligent loading
 */
function asg_optimized_get_modules($request, $course_id) {
    global $asg_smart_query;

    // Intelligent include parameter
    $include = $request->get_param('include');
    if ($include) {
        $include = explode(',', $include);
    } else {
        $include = array();
    }

    // Convert course identifier to ID if needed
    $course_id_num = is_numeric($course_id) ? intval($course_id) : null;
    if (!$course_id_num) {
        global $wpdb;
        $course_id_num = $wpdb->get_var($wpdb->prepare(
            "SELECT id_course FROM wpic_courses WHERE code_course = %s",
            $course_id
        ));
    }

    if (!$course_id_num) {
        return new WP_Error('course_not_found', 'Course not found', ['status' => 404]);
    }

    $modules = $asg_smart_query->course_modules($course_id_num, $include);

    return new WP_REST_Response(array(
        'success' => true,
        'data' => $modules
    ), 200);
}

/**
 * Create module with optimized implementation
 */
function asg_create_module($request, $course_id) {
    global $wpdb;

    try {
        error_log("ASG Create Module: Starting for course_id=$course_id");

        // Get data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }

        error_log("ASG Create Module: Received data: " . json_encode($data));

        // Validate required fields
        if (empty($data['title_module'])) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Module title is required'
            ], 400);
        }

        // Get course info
        $course = $wpdb->get_row($wpdb->prepare(
            "SELECT id_course, code_course FROM wpic_courses WHERE code_course = %s OR id_course = %s",
            $course_id, $course_id
        ));

        if (!$course) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Course not found'
            ], 404);
        }

        // Generate module code
        $module_code = 'MOD_' . strtoupper(uniqid());

        // Prepare module data
        $module_data = [
            'code_module' => $module_code,
            'title_module' => sanitize_text_field($data['title_module']),
            'description_module' => sanitize_textarea_field($data['description_module'] ?? ''),
            'cover_img' => esc_url_raw($data['cover_img'] ?? ''),
            'duration_module' => intval($data['duration_module'] ?? 0),
            'order_module' => intval($data['order_module'] ?? 1),
            'code_course' => $course->code_course,
            'id_user' => get_current_user_id() ?: 1,
            'is_deleted' => 0,
            'date_module' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        // Add course_id if column exists (hybrid approach)
        $has_course_id_column = !empty($wpdb->get_results("SHOW COLUMNS FROM wpic_modules LIKE 'course_id'"));
        if ($has_course_id_column) {
            $module_data['course_id'] = $course->id_course;
            error_log("ASG Create Module: Adding course_id = " . $course->id_course);
        }

        // Insert module
        $result = $wpdb->insert('wpic_modules', $module_data);

        if ($result === false) {
            error_log("ASG Create Module: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        $module_id = $wpdb->insert_id;
        error_log("ASG Create Module: Successfully created module with ID: $module_id");

        // Get the created module
        $created_module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_modules WHERE id_modules = %d",
            $module_id
        ), ARRAY_A);

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Module created successfully',
            'data' => $created_module
        ], 201);

    } catch (Exception $e) {
        error_log("ASG Create Module: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get single module
 */
function asg_optimized_get_module($request, $course_id, $module_id) {
    global $wpdb;

    try {
        // Convert course identifier to ID if needed
        $course_id_num = is_numeric($course_id) ? intval($course_id) : null;
        if (!$course_id_num) {
            $course_id_num = $wpdb->get_var($wpdb->prepare(
                "SELECT id_course FROM wpic_courses WHERE code_course = %s",
                $course_id
            ));
        }

        if (!$course_id_num) {
            return new WP_Error('course_not_found', 'Course not found', ['status' => 404]);
        }

        // Get module
        $module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_modules WHERE id_modules = %d AND is_deleted = 0",
            $module_id
        ), ARRAY_A);

        if (!$module) {
            return new WP_Error('module_not_found', 'Module not found', ['status' => 404]);
        }

        return new WP_REST_Response([
            'success' => true,
            'data' => $module
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Get Module: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Update module
 */
function asg_optimized_update_module($request, $course_id, $module_id) {
    global $wpdb;

    try {
        error_log("ASG Update Module: Starting for course_id=$course_id, module_id=$module_id");

        // Get request data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }

        error_log("ASG Update Module: Data received: " . json_encode($data));

        // Validate module exists
        $module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_modules WHERE id_modules = %d AND is_deleted = 0",
            $module_id
        ));

        if (!$module) {
            return new WP_Error('module_not_found', 'Module not found', ['status' => 404]);
        }

        // Prepare update data
        $update_data = [];
        $allowed_fields = ['title_module', 'description_module', 'cover_img', 'duration_module', 'order_module', 'status_module'];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                if ($field === 'cover_img') {
                    $update_data[$field] = esc_url_raw($data[$field]);
                } elseif ($field === 'description_module') {
                    $update_data[$field] = sanitize_textarea_field($data[$field]);
                } else {
                    $update_data[$field] = sanitize_text_field($data[$field]);
                }
            }
        }

        if (empty($update_data)) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No valid fields to update'
            ], 400);
        }

        // Add updated timestamp
        $update_data['updated_at'] = current_time('mysql');

        // Update module
        $result = $wpdb->update(
            'wpic_modules',
            $update_data,
            ['id_modules' => $module_id],
            null,
            ['%d']
        );

        if ($result === false) {
            error_log("ASG Update Module: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        error_log("ASG Update Module: Successfully updated module $module_id");

        // Get updated module
        $updated_module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_modules WHERE id_modules = %d",
            $module_id
        ), ARRAY_A);

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Module updated successfully',
            'data' => $updated_module
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Update Module: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Delete module
 */
function asg_optimized_delete_module($request, $course_id, $module_id) {
    global $wpdb;

    try {
        error_log("ASG Delete Module: Starting for course_id=$course_id, module_id=$module_id");

        // Validate module exists
        $module = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_modules WHERE id_modules = %d AND is_deleted = 0",
            $module_id
        ));

        if (!$module) {
            return new WP_Error('module_not_found', 'Module not found', ['status' => 404]);
        }

        // Soft delete module
        $result = $wpdb->update(
            'wpic_modules',
            ['is_deleted' => 1, 'updated_at' => current_time('mysql')],
            ['id_modules' => $module_id],
            ['%d', '%s'],
            ['%d']
        );

        if ($result === false) {
            error_log("ASG Delete Module: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        // Also soft delete associated lessons
        $wpdb->update(
            'wpic_lessons',
            ['is_deleted' => 1, 'updated_at' => current_time('mysql')],
            ['id_modules' => $module_id],
            ['%d', '%s'],
            ['%d']
        );

        error_log("ASG Delete Module: Successfully deleted module $module_id and its lessons");

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Module deleted successfully'
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Delete Module: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Optimized dashboard handler - consolidated stats
 */
function asg_optimized_dashboard_handler($request) {
    global $wpdb;

    try {
        // Get comprehensive dashboard data in one call
        $stats = array();

        // Course statistics
        $stats['courses'] = array(
            'total' => intval($wpdb->get_var("SELECT COUNT(*) FROM wpic_courses WHERE status_course != 'archived'")),
            'published' => intval($wpdb->get_var("SELECT COUNT(*) FROM wpic_courses WHERE status_course = 'published'")),
            'draft' => intval($wpdb->get_var("SELECT COUNT(*) FROM wpic_courses WHERE status_course = 'draft'")),
        );

        // Module statistics
        $stats['modules'] = array(
            'total' => intval($wpdb->get_var("SELECT COUNT(*) FROM wpic_modules WHERE is_deleted = 0")),
        );

        // Lesson statistics
        $stats['lessons'] = array(
            'total' => intval($wpdb->get_var("SELECT COUNT(*) FROM wpic_lessons WHERE is_deleted = 0")),
        );

        // Categories
        $stats['categories'] = asg_get_optimized_categories();

        // Recent activity (if requested)
        if ($request->get_param('include_recent')) {
            $stats['recent_courses'] = $wpdb->get_results(
                "SELECT c.*, img.thumbnail_url, img.medium_url, img.large_url
                 FROM wpic_courses c
                 LEFT JOIN wpic_course_images img ON c.id_course = img.course_id AND img.is_active = 1 AND img.file_type = 'course_image' AND img.id = (SELECT MAX(id) FROM wpic_course_images WHERE course_id = c.id_course AND is_active = 1 AND file_type = 'course_image')
                 WHERE c.status_course != 'archived' AND c.is_deleted = 0
                 ORDER BY c.created_at DESC
                 LIMIT 5",
                ARRAY_A
            );
        }

        // System info (if requested)
        if ($request->get_param('include_system')) {
            $stats['system'] = array(
                'php_version' => PHP_VERSION,
                'wp_version' => get_bloginfo('version'),
                'memory_limit' => ini_get('memory_limit'),
                'max_execution_time' => ini_get('max_execution_time'),
            );
        }

        return new WP_REST_Response(array(
            'success' => true,
            'data' => $stats,
            'timestamp' => current_time('mysql')
        ), 200);

    } catch (Exception $e) {
        error_log('ASG Optimized Dashboard Error: ' . $e->getMessage());
        return new WP_Error('server_error', 'Internal server error', ['status' => 500]);
    }
}

/**
 * Optimized media handler - universal upload
 */
function asg_optimized_media_handler($request) {
    $type = $request->get_param('type');
    $entity_id = $request->get_param('entity_id');

    try {
        switch ($type) {
            case 'course_image':
                return asg_optimized_upload_course_image($request);

            case 'module_image':
            case 'module_cover':
                return asg_optimized_upload_course_image($request); // Reutilizar la función de course image

            case 'lesson_media':
            case 'lesson_cover':
                return asg_optimized_upload_course_image($request); // Reutilizar la función de course image

            default:
                return new WP_Error('invalid_type', 'Invalid media type', ['status' => 400]);
        }
    } catch (Exception $e) {
        error_log('ASG Optimized Media Error: ' . $e->getMessage());
        return new WP_Error('server_error', 'Internal server error', ['status' => 500]);
    }
}

/**
 * Optimized course image upload
 */
function asg_optimized_upload_course_image($request) {
    try {
        error_log('ASG Optimized: Starting image upload');

        // Check if file was uploaded
        $files = $request->get_file_params();
        if (empty($files['image'])) {
            return new WP_REST_Response([
                'success' => false,
                'error' => [
                    'code' => 'no_file',
                    'message' => 'No image file provided'
                ]
            ], 400);
        }

        $file = $files['image'];
        error_log('ASG Optimized: File received: ' . print_r($file, true));

        // Validate file type
        $allowed_types = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        if (!in_array($file['type'], $allowed_types)) {
            return new WP_REST_Response([
                'success' => false,
                'error' => [
                    'code' => 'invalid_file_type',
                    'message' => 'Only JPG, PNG and WebP images are allowed'
                ]
            ], 400);
        }

        // Validate size (3MB maximum)
        if ($file['size'] > 3 * 1024 * 1024) {
            return new WP_REST_Response([
                'success' => false,
                'error' => [
                    'code' => 'file_too_large',
                    'message' => 'File size must be less than 3MB'
                ]
            ], 400);
        }

        // Setup WordPress upload handling
        if (!function_exists('wp_handle_upload')) {
            require_once(ABSPATH . 'wp-admin/includes/file.php');
        }

        // Generate unique filename
        $file_extension = pathinfo($file['name'], PATHINFO_EXTENSION);
        $unique_filename = 'asg_course_' . time() . '_' . wp_generate_password(8, false) . '.' . $file_extension;

        // Configure upload with custom filename
        $upload_overrides = [
            'test_form' => false,
            'unique_filename_callback' => function($dir, $name, $ext) use ($unique_filename) {
                return $unique_filename;
            }
        ];

        // Upload file
        $uploaded_file = wp_handle_upload($file, $upload_overrides);

        if (isset($uploaded_file['error'])) {
            error_log('ASG Optimized: Upload error: ' . $uploaded_file['error']);
            return new WP_REST_Response([
                'success' => false,
                'error' => [
                    'code' => 'upload_error',
                    'message' => $uploaded_file['error']
                ]
            ], 500);
        }

        error_log('ASG Optimized: File uploaded successfully: ' . $uploaded_file['url']);

        // Store in custom table with correct column names
        global $wpdb;
        $table_name = 'wpic_course_images';

        // Check if table exists and has correct structure
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$table_name'");
        if (!$table_exists) {
            error_log('ASG Optimized: Table wpic_course_images does not exist');
            return new WP_REST_Response([
                'success' => false,
                'error' => [
                    'code' => 'table_not_found',
                    'message' => 'Image storage table not found'
                ]
            ], 500);
        }

        // Check if mime_type column exists, if not add it
        $columns = $wpdb->get_results("SHOW COLUMNS FROM $table_name LIKE 'mime_type'");
        if (empty($columns)) {
            $wpdb->query("ALTER TABLE $table_name ADD COLUMN mime_type VARCHAR(100) AFTER file_size");
            error_log('ASG Optimized: Added mime_type column to wpic_course_images');
        }

        // Check if course_id column exists and has foreign key
        $foreign_keys = $wpdb->get_results("
            SELECT CONSTRAINT_NAME
            FROM information_schema.KEY_COLUMN_USAGE
            WHERE TABLE_NAME = '$table_name'
            AND COLUMN_NAME = 'course_id'
            AND REFERENCED_TABLE_NAME IS NOT NULL
        ");

        if (empty($foreign_keys)) {
            // Add foreign key constraint for automatic cleanup
            $wpdb->query("
                ALTER TABLE $table_name
                ADD CONSTRAINT fk_course_images_course
                FOREIGN KEY (course_id) REFERENCES wpic_courses(id_course)
                ON DELETE CASCADE ON UPDATE CASCADE
            ");
            error_log('ASG Optimized: Added foreign key constraint to wpic_course_images');
        }

        // Obtener el tipo del request
        $upload_type = $request->get_param('type') ?: 'course_image';

        // Intentar obtener course_id si es posible
        $course_id = null;
        if ($request->get_param('course_id')) {
            $course_id = intval($request->get_param('course_id'));
        }

        $result = $wpdb->insert(
            $table_name,
            [
                'attachment_id' => 0, // We'll update this if we create WP attachment
                'original_url' => $uploaded_file['url'],
                'thumbnail_url' => $uploaded_file['url'], // Same for now
                'medium_url' => $uploaded_file['url'],   // Same for now
                'large_url' => $uploaded_file['url'],    // Same for now
                'file_name' => basename($uploaded_file['file']),
                'file_size' => $file['size'],
                'mime_type' => $file['type'],
                'file_type' => $upload_type, // Agregar el tipo
                'course_id' => $course_id, // Usar course_id si está disponible
                'upload_date' => current_time('mysql'),
                'is_active' => 1
            ],
            ['%d', '%s', '%s', '%s', '%s', '%s', '%d', '%s', '%s', '%d', '%s', '%d']
        );

        if ($result === false) {
            error_log('ASG Optimized: Database insert failed: ' . $wpdb->last_error);
            error_log('ASG Optimized: Last query: ' . $wpdb->last_query);
            return new WP_REST_Response([
                'success' => false,
                'error' => [
                    'code' => 'database_error',
                    'message' => 'Failed to save image information: ' . $wpdb->last_error
                ]
            ], 500);
        }

        $image_id = $wpdb->insert_id;

        error_log('ASG Optimized: Image saved successfully with ID: ' . $image_id);

        return new WP_REST_Response([
            'success' => true,
            'data' => [
                'id' => $image_id, // Agregar 'id' para compatibilidad con frontend
                'image_id' => $image_id,
                'filename' => basename($uploaded_file['file']),
                'url' => $uploaded_file['url'],
                'size' => $file['size']
            ]
        ], 200);

    } catch (Exception $e) {
        error_log('ASG Optimized: Exception in image upload: ' . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => [
                'code' => 'server_error',
                'message' => 'Internal server error'
            ]
        ], 500);
    }
}

/**
 * ========================================
 * QUIZ ENHANCEMENT UTILITIES
 * ========================================
 */

/**
 * Validate and normalize quiz data structure
 * Supports both old and new quiz formats
 */
function asg_validate_and_normalize_quiz_data($quiz_data) {
    if (!is_array($quiz_data) || !isset($quiz_data['questions'])) {
        return false;
    }

    // Set default quiz type if not specified
    $quiz_data['quiz_type'] = $quiz_data['quiz_type'] ?? 'multiple_choice';

    // Validate quiz type
    $valid_types = ['multiple_choice', 'true_false', 'subjective'];
    if (!in_array($quiz_data['quiz_type'], $valid_types)) {
        $quiz_data['quiz_type'] = 'multiple_choice';
    }

    // Normalize each question and enforce pure quiz type
    foreach ($quiz_data['questions'] as &$question) {
        // Ensure required fields exist
        if (!isset($question['id']) || !isset($question['question'])) {
            continue;
        }

        // Enforce pure quiz type - all questions must match quiz type
        $question['type'] = $quiz_data['quiz_type'];

        // Set properties based on quiz type
        switch ($quiz_data['quiz_type']) {
            case 'subjective':
                $question['is_subjective'] = true;
                $question['allow_multiple'] = false;
                $question['points'] = 0;
                $question['correct_answer'] = [];
                // Ensure subjective questions have options for selection
                if (!isset($question['options']) || !is_array($question['options']) || count($question['options']) < 2) {
                    $question['options'] = ['Option A', 'Option B'];
                }
                break;

            case 'true_false':
                $question['is_subjective'] = false;
                $question['allow_multiple'] = false;
                $question['points'] = $question['points'] ?? 1;
                $question['options'] = []; // True/false doesn't use options array
                // Normalize correct_answer for true/false
                if (isset($question['correct_answer']) && is_array($question['correct_answer']) && count($question['correct_answer']) > 0) {
                    // Ensure the value is boolean
                    $question['correct_answer'] = [($question['correct_answer'][0] === true || $question['correct_answer'][0] === 'true')];
                } elseif (isset($question['correct_answer']) && !is_array($question['correct_answer'])) {
                    // Handle non-array correct_answer
                    $question['correct_answer'] = [($question['correct_answer'] === true || $question['correct_answer'] === 'true')];
                } else {
                    $question['correct_answer'] = [true]; // Default to true only if not set or empty array
                }
                break;

            case 'multiple_choice':
            default:
                $question['is_subjective'] = false;
                $question['allow_multiple'] = $question['allow_multiple'] ?? false;
                $question['points'] = $question['points'] ?? 1;
                // Normalize correct_answer to array format
                if (isset($question['correct_answer'])) {
                    if (!is_array($question['correct_answer'])) {
                        $question['correct_answer'] = [$question['correct_answer']];
                    }
                } else {
                    $question['correct_answer'] = [0]; // Default to first option
                }
                break;
        }

        // Validate options array
        if (!isset($question['options']) || !is_array($question['options'])) {
            $question['options'] = [];
        }

        // Ensure minimum 2 options for multiple choice
        if ($question['type'] === 'multiple_choice' && count($question['options']) < 2) {
            // Add default options if missing
            while (count($question['options']) < 2) {
                $question['options'][] = '';
            }
        }

        // Validate correct_answer indices
        if (!$question['is_subjective']) {
            $question['correct_answer'] = array_filter($question['correct_answer'], function($index) use ($question) {
                return is_numeric($index) && $index >= 0 && $index < count($question['options']);
            });
        }
    }

    return $quiz_data;
}

/**
 * Calculate quiz score with enhanced logic
 * Supports subjective questions and multiple selection
 */
function asg_calculate_quiz_score($quiz_data, $user_answers) {
    global $asg_debug_logs;
    if (!isset($asg_debug_logs)) $asg_debug_logs = [];

    $asg_debug_logs[] = "ASG Score Calculation Debug: Starting calculation";
    $asg_debug_logs[] = "ASG Score Calculation Debug: Quiz data: " . json_encode($quiz_data);
    $asg_debug_logs[] = "ASG Score Calculation Debug: User answers: " . json_encode($user_answers);

    if (!$quiz_data || !isset($quiz_data['questions'])) {
        $asg_debug_logs[] = "ASG Score Calculation Debug: No quiz data or questions found";
        return 0;
    }

    // Handle pure quiz types
    $quiz_type = $quiz_data['quiz_type'] ?? 'multiple_choice';
    $asg_debug_logs[] = "ASG Score Calculation Debug: Quiz type: $quiz_type";

    // Subjective quizzes always get 100%
    if ($quiz_type === 'subjective') {
        return 100;
    }

    $total_points = 0;
    $earned_points = 0;

    foreach ($quiz_data['questions'] as $index => $question) {
        $question_points = $question['points'] ?? 1;
        $total_points += $question_points;

        $asg_debug_logs[] = "ASG Score Debug: Question $index - Points: $question_points, Total so far: $total_points";

        $user_answer = $user_answers[$index] ?? null;
        $correct_answers = $question['correct_answer'] ?? [];

        $asg_debug_logs[] = "ASG Score Debug: Question $index - User answer: " . json_encode($user_answer);
        $asg_debug_logs[] = "ASG Score Debug: Question $index - Correct answers: " . json_encode($correct_answers);

        if (empty($correct_answers)) {
            $asg_debug_logs[] = "ASG Score Debug: Question $index - No correct answers defined, skipping";
            continue; // No correct answer defined
        }

        // Handle different answer formats
        if ($question['allow_multiple'] ?? false) {
            // Multiple selection question
            if (!is_array($user_answer)) {
                $user_answer = $user_answer !== null ? [$user_answer] : [];
            }

            // Calculate partial credit for multiple selection
            $correct_count = count($correct_answers);
            $user_correct = count(array_intersect($user_answer, $correct_answers));
            $user_incorrect = count(array_diff($user_answer, $correct_answers));

            // All-or-nothing scoring (can be changed to partial credit)
            if ($user_correct === $correct_count && $user_incorrect === 0) {
                $earned_points += $question_points;
            }
        } else {
            // Single selection question (including true/false)
            if (!is_array($user_answer)) {
                $user_answer = [$user_answer];
            }

            // Handle based on quiz type
            if ($quiz_type === 'true_false') {
                // Convert user answer to boolean
                $user_bool = ($user_answer[0] === 'true' || $user_answer[0] === true);

                // Convert correct answer to boolean - check for both true and false values
                $correct_bool = false; // default
                if (in_array(true, $correct_answers) || in_array('true', $correct_answers)) {
                    $correct_bool = true;
                } elseif (in_array(false, $correct_answers) || in_array('false', $correct_answers)) {
                    $correct_bool = false;
                }

                $asg_debug_logs[] = "ASG Scoring Debug: User answer: " . $user_answer[0] . ", User bool: " . ($user_bool ? 'true' : 'false');
                $asg_debug_logs[] = "ASG Scoring Debug: Correct answers: " . json_encode($correct_answers) . ", Correct bool: " . ($correct_bool ? 'true' : 'false');
                $asg_debug_logs[] = "ASG Scoring Debug: Match: " . ($user_bool === $correct_bool ? 'YES' : 'NO');

                if ($user_bool === $correct_bool) {
                    $earned_points += $question_points;
                    $asg_debug_logs[] = "ASG Score Debug: Question $index - CORRECT! Earned $question_points points. Total earned: $earned_points";
                } else {
                    $asg_debug_logs[] = "ASG Score Debug: Question $index - INCORRECT! No points earned. Total earned: $earned_points";
                }
            } else {
                // Handle multiple choice questions
                if (count($user_answer) === 1 && in_array($user_answer[0], $correct_answers)) {
                    $earned_points += $question_points;
                }
            }
        }
    }

    // Calculate percentage score
    $asg_debug_logs[] = "ASG Final Score Debug: Total points: $total_points, Earned points: $earned_points";
    $percentage = $total_points > 0 ? round(($earned_points / $total_points) * 100) : 0;
    $asg_debug_logs[] = "ASG Final Score Debug: Final percentage: $percentage%";
    return $percentage;
}

/**
 * Quiz validation endpoint handler
 */
function asg_quiz_validation_handler($request) {
    try {
        $data = $request->get_json_params();
        if (empty($data)) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No data provided'
            ], 400);
        }

        // Validate quiz data structure
        $validated_data = asg_validate_and_normalize_quiz_data($data);

        if (!$validated_data) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Invalid quiz data structure',
                'details' => 'Quiz must have questions array with valid question objects'
            ], 400);
        }

        // Return validated and normalized data
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Quiz data is valid',
            'data' => $validated_data,
            'stats' => [
                'total_questions' => count($validated_data['questions']),
                'subjective_questions' => count(array_filter($validated_data['questions'], function($q) {
                    return $q['is_subjective'] ?? false;
                })),
                'multiple_selection_questions' => count(array_filter($validated_data['questions'], function($q) {
                    return $q['allow_multiple'] ?? false;
                })),
                'total_points' => array_sum(array_column($validated_data['questions'], 'points'))
            ]
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Quiz Validation: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Validation error: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Quiz scoring endpoint handler
 */
function asg_quiz_scoring_handler($request) {
    global $asg_debug_logs;
    $asg_debug_logs = []; // Initialize debug logs

    try {
        $data = $request->get_json_params();
        if (empty($data) || !isset($data['quiz_data']) || !isset($data['user_answers'])) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Missing quiz_data or user_answers'
            ], 400);
        }

        $quiz_data = $data['quiz_data'];
        $user_answers = $data['user_answers'];

        // Validate quiz data first
        $validated_quiz = asg_validate_and_normalize_quiz_data($quiz_data);
        if (!$validated_quiz) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Invalid quiz data'
            ], 400);
        }

        // Calculate score
        $score = asg_calculate_quiz_score($validated_quiz, $user_answers);
        $quiz_type = $validated_quiz['quiz_type'] ?? 'multiple_choice';

        // For subjective quizzes, always pass
        if ($quiz_type === 'subjective') {
            $passing_score = 100;
            $passed = true;
        } else {
            $passing_score = $validated_quiz['passing_score'] ?? 70;
            $passed = $score >= $passing_score;
        }

        // Calculate detailed results
        $total_questions = count($validated_quiz['questions']);
        $correct_answers = 0;
        $subjective_answers = ($quiz_type === 'subjective') ? $total_questions : 0;
        $question_results = [];

        foreach ($validated_quiz['questions'] as $index => $question) {
            $user_answer = $user_answers[$index] ?? null;
            $is_subjective = $question['is_subjective'] ?? false;
            $correct_answers_array = $question['correct_answer'] ?? [];

            $question_result = [
                'question_id' => $question['id'],
                'is_subjective' => $is_subjective,
                'user_answer' => $user_answer,
                'correct_answers' => $correct_answers_array,
                'is_correct' => false,
                'points_earned' => 0,
                'points_possible' => $question['points'] ?? 1
            ];

            if ($is_subjective) {
                $subjective_answers++;
                $question_result['is_correct'] = true; // All subjective answers are "correct"
            } else {
                // Check if answer is correct
                if ($question['allow_multiple'] ?? false) {
                    $user_array = is_array($user_answer) ? $user_answer : [$user_answer];
                    $correct_count = count($correct_answers_array);
                    $user_correct = count(array_intersect($user_array, $correct_answers_array));
                    $user_incorrect = count(array_diff($user_array, $correct_answers_array));

                    if ($user_correct === $correct_count && $user_incorrect === 0) {
                        $question_result['is_correct'] = true;
                        $question_result['points_earned'] = $question['points'] ?? 1;
                        $correct_answers++;
                    }
                } else {
                    // Handle based on quiz type
                    if ($quiz_type === 'true_false') {
                        // Convert user answer to boolean
                        $user_bool = ($user_answer === 'true' || $user_answer === true);

                        // Convert correct answer to boolean - check for both true and false values
                        $correct_bool = false; // default
                        if (in_array(true, $correct_answers_array) || in_array('true', $correct_answers_array)) {
                            $correct_bool = true;
                        } elseif (in_array(false, $correct_answers_array) || in_array('false', $correct_answers_array)) {
                            $correct_bool = false;
                        }

                        $asg_debug_logs[] = "ASG Scoring Handler Debug: User answer: " . $user_answer . ", User bool: " . ($user_bool ? 'true' : 'false');
                        $asg_debug_logs[] = "ASG Scoring Handler Debug: Correct answers: " . json_encode($correct_answers_array) . ", Correct bool: " . ($correct_bool ? 'true' : 'false');
                        $asg_debug_logs[] = "ASG Scoring Handler Debug: Match: " . ($user_bool === $correct_bool ? 'YES' : 'NO');

                        if ($user_bool === $correct_bool) {
                            $question_result['is_correct'] = true;
                            $question_result['points_earned'] = $question['points'] ?? 1;
                            $correct_answers++;
                        }
                    } else {
                        // Handle multiple choice questions
                        if (in_array($user_answer, $correct_answers_array)) {
                            $question_result['is_correct'] = true;
                            $question_result['points_earned'] = $question['points'] ?? 1;
                            $correct_answers++;
                        }
                    }
                }
            }

            $question_results[] = $question_result;
        }

        // Recalculate score based on correct answers count (more reliable)
        $non_subjective_questions = $total_questions - $subjective_answers;
        if ($non_subjective_questions > 0) {
            $recalculated_score = round(($correct_answers / $non_subjective_questions) * 100);
            $asg_debug_logs[] = "ASG Score Recalculation: $correct_answers correct out of $non_subjective_questions non-subjective questions = $recalculated_score%";

            // Use recalculated score if it's different from original
            if ($recalculated_score !== $score) {
                $asg_debug_logs[] = "ASG Score Fix: Original score $score% replaced with recalculated score $recalculated_score%";
                $score = $recalculated_score;
                $passed = $score >= $passing_score;
            }
        }

        return new WP_REST_Response([
            'success' => true,
            'data' => [
                'score' => $score,
                'passed' => $passed,
                'passing_score' => $passing_score,
                'correct_answers' => $correct_answers,
                'total_questions' => $total_questions,
                'subjective_answers' => $subjective_answers,
                'question_results' => $question_results
            ],
            'debug_logs' => $asg_debug_logs // Add debug logs to response
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Quiz Scoring: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Scoring error: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Associate image with course
 */
function asg_optimized_associate_image_with_course($image_id, $course_id) {
    global $wpdb;

    // Update both course_id and file_type
    $result = $wpdb->update(
        'wpic_course_images',
        [
            'course_id' => $course_id,
            'file_type' => 'course_cover' // ← AGREGADO
        ],
        ['id' => $image_id],
        ['%d', '%s'], // ← FORMATO ACTUALIZADO
        ['%d']
    );

    if ($result !== false) {
        error_log("ASG Optimized: Image $image_id associated with course $course_id with file_type 'course_cover'");
        return true;
    } else {
        error_log("ASG Optimized: Failed to associate image $image_id with course $course_id: " . $wpdb->last_error);
        return false;
    }
}

/**
 * Test endpoint for debugging update issues
 */
function asg_test_update_endpoint($request) {
    global $wpdb;

    $identifier = $request['identifier'];
    error_log("TEST UPDATE: Starting test for identifier: $identifier");

    try {
        // Get data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }

        error_log("TEST UPDATE: Received data: " . json_encode($data));

        // Find course
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id_course FROM wpic_courses WHERE code_course = %s",
            $identifier
        ));

        if (!$course_id) {
            error_log("TEST UPDATE: Course not found for code: $identifier");
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Course not found'
            ], 404);
        }

        error_log("TEST UPDATE: Found course_id: $course_id");

        // Try simple update
        $result = $wpdb->update(
            'wpic_courses',
            ['updated_at' => current_time('mysql')],
            ['id_course' => $course_id],
            ['%s'],
            ['%d']
        );

        if ($result === false) {
            error_log("TEST UPDATE: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        error_log("TEST UPDATE: Success! Updated $result rows");

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Test update successful',
            'data' => [
                'course_id' => $course_id,
                'identifier' => $identifier,
                'rows_updated' => $result
            ]
        ], 200);

    } catch (Exception $e) {
        error_log("TEST UPDATE: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Simple working update endpoint - based on successful test
 */
function asg_simple_update_endpoint($request) {
    global $wpdb;

    $identifier = $request['identifier'];
    error_log("SIMPLE UPDATE: Starting for identifier: $identifier");

    try {
        // Get data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }

        error_log("SIMPLE UPDATE: Received data: " . json_encode($data));

        // Find course
        $course_id = $wpdb->get_var($wpdb->prepare(
            "SELECT id_course FROM wpic_courses WHERE code_course = %s",
            $identifier
        ));

        if (!$course_id) {
            error_log("SIMPLE UPDATE: Course not found for code: $identifier");
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Course not found'
            ], 404);
        }

        error_log("SIMPLE UPDATE: Found course_id: $course_id");

        // Prepare update data
        $update_data = ['updated_at' => current_time('mysql')];
        $update_format = ['%s'];

        // Add safe fields (only existing columns)
        $safe_fields = [
            'name_course' => '%s',
            'description_course' => '%s',
            'category_course' => '%s',
            'language_course' => '%s',
            'price_course' => '%f',
            'duration_course' => '%f',
            'objectives' => '%s',
            'benefits' => '%s'
        ];

        foreach ($safe_fields as $field => $format) {
            // Special handling for objectives and benefits - allow empty values
            $should_process = false;
            if ($field === 'objectives' || $field === 'benefits') {
                $should_process = isset($data[$field]); // Process even if empty
            } else {
                $should_process = isset($data[$field]) && $data[$field] !== '';
            }

            if ($should_process) {
                if ($format === '%f') {
                    $update_data[$field] = floatval($data[$field]);
                } else {
                    $update_data[$field] = sanitize_text_field($data[$field]);
                }
                $update_format[] = $format;
                error_log("SIMPLE UPDATE: Adding $field = " . $update_data[$field]);
            } else {
                error_log("SIMPLE UPDATE: Field $field is missing or empty in data");
            }
        }

        error_log("SIMPLE UPDATE: Final update data: " . json_encode($update_data));

        // Perform update
        $result = $wpdb->update(
            'wpic_courses',
            $update_data,
            ['id_course' => $course_id],
            $update_format,
            ['%d']
        );

        if ($result === false) {
            error_log("SIMPLE UPDATE: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        error_log("SIMPLE UPDATE: Success! Updated $result rows");

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Course updated successfully',
            'data' => [
                'course_id' => $course_id,
                'identifier' => $identifier,
                'rows_updated' => $result,
                'updated_fields' => array_keys($update_data)
            ]
        ], 200);

    } catch (Exception $e) {
        error_log("SIMPLE UPDATE: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Optimized lessons handler - GET/POST for lessons collection
 */
function asg_optimized_lessons_handler($request) {
    $course_id = $request->get_param('course_id');
    $module_id = $request->get_param('module_id');
    $method = $request->get_method();

    error_log("ASG Lessons Handler: $method for course_id=$course_id, module_id=$module_id");

    switch ($method) {
        case 'GET':
            return asg_optimized_get_lessons($request, $course_id, $module_id);
        case 'POST':
            return asg_optimized_create_lesson($request, $course_id, $module_id);
        default:
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Method not allowed'
            ], 405);
    }
}

/**
 * Optimized lesson handler - GET/PUT/DELETE for individual lesson
 */
function asg_optimized_lesson_handler($request) {
    $course_id = $request->get_param('course_id');
    $module_id = $request->get_param('module_id');
    $lesson_id = $request->get_param('lesson_id');
    $method = $request->get_method();

    error_log("ASG Lesson Handler: $method for course_id=$course_id, module_id=$module_id, lesson_id=$lesson_id");

    switch ($method) {
        case 'GET':
            return asg_optimized_get_lesson($request, $course_id, $module_id, $lesson_id);
        case 'PUT':
            return asg_optimized_update_lesson($request, $course_id, $module_id, $lesson_id);
        case 'DELETE':
            return asg_optimized_delete_lesson($request, $course_id, $module_id, $lesson_id);
        default:
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Method not allowed'
            ], 405);
    }
}

/**
 * Create lesson with optimized implementation
 */
function asg_optimized_create_lesson($request, $course_id, $module_id) {
    global $wpdb;

    try {
        error_log("ASG Create Lesson: Starting for course_id=$course_id, module_id=$module_id");

        // Get data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }

        error_log("ASG Create Lesson: Received data: " . json_encode($data));

        // Validate required fields
        if (empty($data['title_lesson'])) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Lesson title is required'
            ], 400);
        }

        // Get module info
        $module = $wpdb->get_row($wpdb->prepare(
            "SELECT id_modules, code_module, code_course FROM wpic_modules WHERE id_modules = %d",
            $module_id
        ));

        if (!$module) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Module not found'
            ], 404);
        }

        // Generate lesson code
        $lesson_code = 'LES_' . strtoupper(uniqid());

        // Prepare lesson data
        $lesson_data = [
            'code_lesson' => $lesson_code,
            'title_lesson' => sanitize_text_field($data['title_lesson']),
            'description_lesson' => sanitize_textarea_field($data['description_lesson'] ?? ''),
            'content_lesson' => wp_kses_post($data['content_lesson'] ?? ''),
            'video_url' => esc_url_raw($data['video_url'] ?? ''),
            'cover_img' => esc_url_raw($data['cover_img'] ?? ''),
            'lesson_type' => sanitize_text_field($data['lesson_type'] ?? 'text'),
            'duration_lesson' => intval($data['duration_lesson'] ?? 0),
            'order_lesson' => intval($data['order_lesson'] ?? 1),
            'is_preview' => intval($data['is_preview'] ?? 0),
            'code_module' => $module->code_module,
            'code_course' => $module->code_course,
            'id_user' => get_current_user_id() ?: 1,
            'is_deleted' => 0,
            'date_lesson' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ];

        // Add module_id if column exists (hybrid approach)
        $has_module_id_column = !empty($wpdb->get_results("SHOW COLUMNS FROM wpic_lessons LIKE 'module_id'"));
        if ($has_module_id_column) {
            $lesson_data['module_id'] = $module_id;
            error_log("ASG Create Lesson: Adding module_id = $module_id");
        }

        // Insert lesson
        $result = $wpdb->insert('wpic_lessons', $lesson_data);

        if ($result === false) {
            error_log("ASG Create Lesson: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        $lesson_id = $wpdb->insert_id;
        error_log("ASG Create Lesson: Successfully created lesson with ID: $lesson_id");

        // Get the created lesson
        $created_lesson = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_lessons WHERE id_lesson = %d",
            $lesson_id
        ), ARRAY_A);

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Lesson created successfully',
            'data' => $created_lesson
        ], 201);

    } catch (Exception $e) {
        error_log("ASG Create Lesson: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get lessons for a module
 */
function asg_optimized_get_lessons($request, $course_id, $module_id) {
    global $wpdb;

    try {
        // Get lessons using hybrid approach
        $has_module_id_column = !empty($wpdb->get_results("SHOW COLUMNS FROM wpic_lessons LIKE 'module_id'"));

        if ($has_module_id_column) {
            $lessons = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM wpic_lessons WHERE module_id = %d AND is_deleted = 0 ORDER BY order_lesson ASC",
                $module_id
            ), ARRAY_A);
        } else {
            // Fallback to code_module
            $module = $wpdb->get_row($wpdb->prepare(
                "SELECT code_module FROM wpic_modules WHERE id_modules = %d",
                $module_id
            ));

            if (!$module) {
                return new WP_REST_Response(['success' => false, 'error' => 'Module not found'], 404);
            }

            $lessons = $wpdb->get_results($wpdb->prepare(
                "SELECT * FROM wpic_lessons WHERE code_module = %s AND is_deleted = 0 ORDER BY order_lesson ASC",
                $module->code_module
            ), ARRAY_A);
        }

        return new WP_REST_Response([
            'success' => true,
            'data' => $lessons
        ], 200);

    } catch (Exception $e) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Get single lesson
 */
function asg_optimized_get_lesson($request, $course_id, $module_id, $lesson_id) {
    global $wpdb;

    $lesson = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM wpic_lessons WHERE id_lesson = %d AND is_deleted = 0",
        $lesson_id
    ), ARRAY_A);

    if (!$lesson) {
        return new WP_REST_Response(['success' => false, 'error' => 'Lesson not found'], 404);
    }

    return new WP_REST_Response(['success' => true, 'data' => $lesson], 200);
}

/**
 * Update lesson
 */
function asg_optimized_update_lesson($request, $course_id, $module_id, $lesson_id) {
    global $wpdb, $asg_debug_logs;
    if (!isset($asg_debug_logs)) $asg_debug_logs = [];

    try {
        $asg_debug_logs[] = "ASG Update Lesson: Starting for course_id=$course_id, module_id=$module_id, lesson_id=$lesson_id";

        // Get data
        $data = $request->get_json_params();
        if (empty($data)) {
            $data = $request->get_params();
        }

        error_log("ASG Update Lesson: Received data: " . json_encode($data));

        // Validate lesson exists
        $lesson = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_lessons WHERE id_lesson = %d AND is_deleted = 0",
            $lesson_id
        ));

        if (!$lesson) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Lesson not found'
            ], 404);
        }

        // Prepare update data
        $update_data = [];
        $allowed_fields = [
            'title_lesson', 'description_lesson', 'content_lesson',
            'video_url', 'cover_img', 'lesson_type', 'duration_lesson',
            'order_lesson', 'is_preview'
        ];

        foreach ($allowed_fields as $field) {
            if (isset($data[$field])) {
                if (in_array($field, ['video_url', 'cover_img'])) {
                    $update_data[$field] = esc_url_raw($data[$field]);
                } elseif ($field === 'description_lesson') {
                    $update_data[$field] = sanitize_textarea_field($data[$field]);
                } elseif ($field === 'content_lesson') {
                    // Special handling for quiz content
                    if (isset($data['lesson_type']) && $data['lesson_type'] === 'quiz') {
                        // Validate and normalize quiz data
                        $quiz_data = null;
                        if (!empty($data[$field])) {
                            $quiz_data = json_decode($data[$field], true);
                            if (json_last_error() === JSON_ERROR_NONE) {
                                $asg_debug_logs[] = "ASG Update Lesson: Original quiz data: " . json_encode($quiz_data);
                                $quiz_data = asg_validate_and_normalize_quiz_data($quiz_data);
                                if ($quiz_data) {
                                    $asg_debug_logs[] = "ASG Update Lesson: Normalized quiz data: " . json_encode($quiz_data);
                                    $update_data[$field] = json_encode($quiz_data, JSON_UNESCAPED_UNICODE);
                                    $asg_debug_logs[] = "ASG Update Lesson: Quiz data validated and normalized";
                                } else {
                                    $asg_debug_logs[] = "ASG Update Lesson: Invalid quiz data structure";
                                    return new WP_REST_Response([
                                        'success' => false,
                                        'error' => 'Invalid quiz data structure'
                                    ], 400);
                                }
                            } else {
                                error_log("ASG Update Lesson: Invalid JSON in quiz content");
                                return new WP_REST_Response([
                                    'success' => false,
                                    'error' => 'Invalid JSON format in quiz content'
                                ], 400);
                            }
                        }
                    } else {
                        $update_data[$field] = wp_kses_post($data[$field]);
                    }
                } elseif (in_array($field, ['duration_lesson', 'order_lesson', 'is_preview'])) {
                    $update_data[$field] = intval($data[$field]);
                } else {
                    $update_data[$field] = sanitize_text_field($data[$field]);
                }
            }
        }

        if (empty($update_data)) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No valid fields to update'
            ], 400);
        }

        // Add updated timestamp
        $update_data['updated_at'] = current_time('mysql');

        // Update lesson
        $result = $wpdb->update(
            'wpic_lessons',
            $update_data,
            ['id_lesson' => $lesson_id],
            null,
            ['%d']
        );

        if ($result === false) {
            error_log("ASG Update Lesson: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        error_log("ASG Update Lesson: Successfully updated lesson $lesson_id");

        // Get updated lesson
        $updated_lesson = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_lessons WHERE id_lesson = %d",
            $lesson_id
        ), ARRAY_A);

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Lesson updated successfully',
            'data' => $updated_lesson,
            'debug_logs' => $asg_debug_logs ?? []
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Update Lesson: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Delete lesson
 */
function asg_optimized_delete_lesson($request, $course_id, $module_id, $lesson_id) {
    global $wpdb;

    try {
        error_log("ASG Delete Lesson: Starting for course_id=$course_id, module_id=$module_id, lesson_id=$lesson_id");

        // Validate lesson exists
        $lesson = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_lessons WHERE id_lesson = %d AND is_deleted = 0",
            $lesson_id
        ));

        if (!$lesson) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Lesson not found'
            ], 404);
        }

        // Soft delete lesson
        $result = $wpdb->update(
            'wpic_lessons',
            ['is_deleted' => 1, 'updated_at' => current_time('mysql')],
            ['id_lesson' => $lesson_id],
            ['%d', '%s'],
            ['%d']
        );

        if ($result === false) {
            error_log("ASG Delete Lesson: Database error: " . $wpdb->last_error);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Database error: ' . $wpdb->last_error
            ], 500);
        }

        error_log("ASG Delete Lesson: Successfully deleted lesson $lesson_id");

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Lesson deleted successfully'
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Delete Lesson: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Exception: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Lesson viewer handler - Get lesson with enhanced data for lesson viewer page
 */
function asg_lesson_viewer_handler($request) {
    global $wpdb;

    $lesson_id = $request->get_param('lesson_id');
    $course_code = $request->get_param('course');

    try {
        error_log("ASG Lesson Viewer: Getting lesson $lesson_id for course $course_code");

        // Get lesson with enhanced data
        $lesson = $wpdb->get_row($wpdb->prepare(
            "SELECT l.*,
                    m.title_module, m.id_modules,
                    c.name_course, c.id_course
             FROM wpic_lessons l
             LEFT JOIN wpic_modules m ON l.code_module = m.code_module
             LEFT JOIN wpic_courses c ON l.code_course = c.code_course
             WHERE l.id_lesson = %d AND l.is_deleted = 0",
            $lesson_id
        ), ARRAY_A);

        if (!$lesson) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Lesson not found'
            ], 404);
        }

        // Validate course if provided
        if ($course_code && $lesson['code_course'] !== $course_code) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Lesson does not belong to specified course'
            ], 400);
        }

        // Add lesson images
        $course_id = $lesson['id_course'];
        if ($course_id) {
            $image = $wpdb->get_row($wpdb->prepare(
                "SELECT thumbnail_url, medium_url, large_url
                 FROM wpic_course_images
                 WHERE course_id = %d
                 AND file_type = 'lesson_cover'
                 AND is_active = 1
                 ORDER BY id DESC LIMIT 1",
                $course_id
            ), ARRAY_A);

            $lesson['lesson_thumbnail_url'] = $image['thumbnail_url'] ?? null;
            $lesson['lesson_medium_url'] = $image['medium_url'] ?? null;
            $lesson['lesson_large_url'] = $image['large_url'] ?? null;
        }

        // Add lesson progress/completion status (placeholder for future implementation)
        $lesson['is_completed'] = false;
        $lesson['progress_percentage'] = 0;

        error_log("ASG Lesson Viewer: Successfully retrieved lesson data");

        return new WP_REST_Response([
            'success' => true,
            'data' => $lesson
        ], 200);

    } catch (Exception $e) {
        error_log("ASG Lesson Viewer: Exception: " . $e->getMessage());
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Error retrieving lesson: ' . $e->getMessage()
        ], 500);
    }
}
// Funcion de los comentarios
function asg_lesson_comments_handler($request) {
	global $wpdb;

	$lesson_id = intval($request['lesson_id']);
	$method = $request->get_method();

	if ($method === 'GET') {
		$results = $wpdb->get_results($wpdb->prepare(
			"SELECT author, comment, created_at AS date FROM wpic_lesson_comments WHERE lesson_id = %d ORDER BY created_at DESC",
			$lesson_id
		), ARRAY_A);

		return new WP_REST_Response([
			'success' => true,
			'comments' => $results
		], 200);
	}

	if ($method === 'POST') {
		// Solo usuarios autenticados pueden publicar
		if (!is_user_logged_in()) {
			return new WP_REST_Response([
				'success' => false,
				'error' => 'Authentication required.'
			], 401);
		}

		$data = $request->get_json_params();
		$comment = trim(sanitize_text_field($data['comment'] ?? ''));

		if (empty($comment)) {
			return new WP_REST_Response([
				'success' => false,
				'error' => 'Comment is required.'
			], 400);
		}

		$current_user = wp_get_current_user();
		$author = $current_user->display_name;

		$result = $wpdb->insert('wpic_lesson_comments', [
			'lesson_id' => $lesson_id,
			'author' => $author,
			'comment' => $comment,
			'created_at' => current_time('mysql')
		], ['%d', '%s', '%s', '%s']);

		if ($result === false) {
			return new WP_REST_Response([
				'success' => false,
				'error' => 'Database error: ' . $wpdb->last_error
			], 500);
		}

		return new WP_REST_Response([
			'success' => true,
			'message' => 'Comment added successfully',
			'comment' => [
				'author' => $author,
				'comment' => $comment,
				'date' => current_time('mysql')
			]
		], 201);
	}

	return new WP_REST_Response([
		'success' => false,
		'error' => 'Method not allowed'
	], 405);
}


// Log initialization
error_log('ASG: Optimized endpoints loaded - Version 1.0.0 - COMPLETE');
