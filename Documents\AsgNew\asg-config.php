<?php
/**
 * ========================================
 * ASG STUDENT FLOW - CONFIGURATION
 * ========================================
 * 
 * Archivo de configuración centralizada para el sistema de estudiantes
 * Incluye configuraciones de PayPal, URLs, y constantes del sistema
 * 
 * Version: 1.0.0
 * Author: ASG Development Team
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * PAYPAL CONFIGURATION
 * ========================================
 */

// PayPal Environment (sandbox/production)
define('ASG_PAYPAL_ENVIRONMENT', 'sandbox'); // Cambiar a 'production' para producción

// PayPal Client IDs
define('ASG_PAYPAL_CLIENT_ID_SANDBOX', 'AZDxjDScFpQtjWTOUtWKbyN_bDt4OgqaF4eYXlewfBP4-8aqX3PiV8e1GWU6liB2CU21rbWUVscg9RqE');
define('ASG_PAYPAL_CLIENT_ID_PRODUCTION', 'TU_CLIENT_ID_DE_PRODUCCION_AQUI');

// PayPal Client Secrets (para webhooks si se implementan)
define('ASG_PAYPAL_CLIENT_SECRET_SANDBOX', 'TU_CLIENT_SECRET_SANDBOX_AQUI');
define('ASG_PAYPAL_CLIENT_SECRET_PRODUCTION', 'TU_CLIENT_SECRET_PRODUCCION_AQUI');

/**
 * ========================================
 * SYSTEM CONFIGURATION
 * ========================================
 */

// URLs del sistema
define('ASG_PAYMENT_PAGE_URL', '/payment/');
define('ASG_LESSONS_PAGE_URL', '/lessons/');
define('ASG_REGISTER_PAGE_URL', '/register/');
define('ASG_LOGIN_PAGE_URL', '/wp-login.php');

// API Configuration
define('ASG_API_NAMESPACE', 'asg/v1');
define('ASG_API_VERSION', '1.0.0');

// Database Configuration
define('ASG_DB_VERSION', '1.0.0');
define('ASG_STUDENTS_TABLE', 'wpic_asg_students');
define('ASG_ENROLLMENTS_TABLE', 'wpic_asg_enrollments');
define('ASG_PROGRESS_TABLE', 'wpic_asg_progress');

// Course Configuration
define('ASG_DEFAULT_CURRENCY', 'USD');
define('ASG_DEFAULT_COURSE_ACCESS_DURATION', 'lifetime'); // 'lifetime' or number of days

/**
 * ========================================
 * FEATURE FLAGS
 * ========================================
 */

// Características habilitadas/deshabilitadas
define('ASG_ENABLE_COURSE_CERTIFICATES', true);
define('ASG_ENABLE_PROGRESS_TRACKING', true);
define('ASG_ENABLE_EMAIL_NOTIFICATIONS', true);
define('ASG_ENABLE_WEBHOOKS', false);
define('ASG_ENABLE_ANALYTICS', true);

/**
 * ========================================
 * EMAIL CONFIGURATION
 * ========================================
 */

// Email settings
define('ASG_FROM_EMAIL', get_option('admin_email'));
define('ASG_FROM_NAME', get_option('blogname'));
define('ASG_SUPPORT_EMAIL', '<EMAIL>');

/**
 * ========================================
 * HELPER FUNCTIONS
 * ========================================
 */

/**
 * Obtener Client ID de PayPal según el entorno
 */
function asg_get_paypal_client_id() {
    if (ASG_PAYPAL_ENVIRONMENT === 'production') {
        return ASG_PAYPAL_CLIENT_ID_PRODUCTION;
    }
    return ASG_PAYPAL_CLIENT_ID_SANDBOX;
}

/**
 * Obtener Client Secret de PayPal según el entorno
 */
function asg_get_paypal_client_secret() {
    if (ASG_PAYPAL_ENVIRONMENT === 'production') {
        return ASG_PAYPAL_CLIENT_SECRET_PRODUCTION;
    }
    return ASG_PAYPAL_CLIENT_SECRET_SANDBOX;
}

/**
 * Verificar si estamos en modo sandbox
 */
function asg_is_paypal_sandbox() {
    return ASG_PAYPAL_ENVIRONMENT === 'sandbox';
}

/**
 * Obtener URL base de PayPal API
 */
function asg_get_paypal_api_base() {
    if (ASG_PAYPAL_ENVIRONMENT === 'production') {
        return 'https://api.paypal.com';
    }
    return 'https://api.sandbox.paypal.com';
}

/**
 * Obtener configuración completa de PayPal para JavaScript
 */
function asg_get_paypal_config() {
    return [
        'client_id' => asg_get_paypal_client_id(),
        'environment' => ASG_PAYPAL_ENVIRONMENT,
        'currency' => ASG_DEFAULT_CURRENCY,
        'sandbox' => asg_is_paypal_sandbox()
    ];
}

/**
 * ========================================
 * COURSE CONFIGURATION
 * ========================================
 */

/**
 * Mapeo de códigos de curso a información
 */
function asg_get_course_config() {
    return [
        'course_1' => [
            'name' => 'Curso Básico de Habilidades',
            'price' => 99.00,
            'duration' => '4 semanas',
            'level' => 'Principiante'
        ],
        'course_2' => [
            'name' => 'Curso Intermedio de Habilidades',
            'price' => 149.00,
            'duration' => '6 semanas',
            'level' => 'Intermedio'
        ],
        'course_3' => [
            'name' => 'Curso Avanzado de Habilidades',
            'price' => 199.00,
            'duration' => '8 semanas',
            'level' => 'Avanzado'
        ],
        'course_4' => [
            'name' => 'Curso Especializado',
            'price' => 299.00,
            'duration' => '12 semanas',
            'level' => 'Experto'
        ]
    ];
}

/**
 * Obtener información de un curso específico
 */
function asg_get_course_info($course_code) {
    $courses = asg_get_course_config();
    return isset($courses[$course_code]) ? $courses[$course_code] : null;
}

/**
 * ========================================
 * LOGGING CONFIGURATION
 * ========================================
 */

/**
 * Log personalizado para ASG
 */
function asg_log($message, $level = 'info') {
    if (defined('WP_DEBUG') && WP_DEBUG) {
        $timestamp = current_time('Y-m-d H:i:s');
        $log_message = "[{$timestamp}] ASG-{$level}: {$message}";
        error_log($log_message);
    }
}

/**
 * Log de errores específico
 */
function asg_log_error($message, $context = []) {
    $context_str = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    asg_log("ERROR: {$message}{$context_str}", 'error');
}

/**
 * Log de éxito específico
 */
function asg_log_success($message, $context = []) {
    $context_str = !empty($context) ? ' | Context: ' . json_encode($context) : '';
    asg_log("SUCCESS: {$message}{$context_str}", 'success');
}

/**
 * ========================================
 * SECURITY CONFIGURATION
 * ========================================
 */

/**
 * Verificar nonce para requests AJAX
 */
function asg_verify_nonce($nonce, $action = 'asg_action') {
    return wp_verify_nonce($nonce, $action);
}

/**
 * Sanitizar datos de entrada
 */
function asg_sanitize_input($data) {
    if (is_array($data)) {
        return array_map('asg_sanitize_input', $data);
    }
    return sanitize_text_field($data);
}

/**
 * ========================================
 * INITIALIZATION
 * ========================================
 */

/**
 * Inicializar configuración ASG
 */
function asg_init_config() {
    // Log de inicialización
    asg_log('ASG Student Flow System initialized');
    
    // Verificar configuración de PayPal
    if (empty(asg_get_paypal_client_id())) {
        asg_log_error('PayPal Client ID not configured');
    }
    
    // Verificar tablas de base de datos
    global $wpdb;
    $tables = [
        ASG_STUDENTS_TABLE,
        ASG_ENROLLMENTS_TABLE,
        ASG_PROGRESS_TABLE
    ];
    
    foreach ($tables as $table) {
        $table_name = $wpdb->prefix . $table;
        if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
            asg_log_error("Database table missing: $table_name");
        }
    }
}

// Inicializar al cargar WordPress
add_action('init', 'asg_init_config');

/**
 * ========================================
 * CONSTANTS FOR JAVASCRIPT
 * ========================================
 */

/**
 * Generar configuración para JavaScript
 */
function asg_get_js_config() {
    return [
        'api_base' => rest_url(ASG_API_NAMESPACE . '/'),
        'nonce' => wp_create_nonce('wp_rest'),
        'paypal' => asg_get_paypal_config(),
        'urls' => [
            'payment' => ASG_PAYMENT_PAGE_URL,
            'lessons' => ASG_LESSONS_PAGE_URL,
            'register' => ASG_REGISTER_PAGE_URL,
            'login' => ASG_LOGIN_PAGE_URL
        ],
        'features' => [
            'certificates' => ASG_ENABLE_COURSE_CERTIFICATES,
            'progress_tracking' => ASG_ENABLE_PROGRESS_TRACKING,
            'analytics' => ASG_ENABLE_ANALYTICS
        ]
    ];
}

/**
 * Imprimir configuración JavaScript
 */
function asg_print_js_config() {
    $config = asg_get_js_config();
    echo '<script>window.ASG_CONFIG = ' . json_encode($config) . ';</script>';
}

// Hook para imprimir configuración en el footer
add_action('wp_footer', 'asg_print_js_config');

/**
 * ========================================
 * VERSION CONTROL
 * ========================================
 */

// Versión actual del sistema
define('ASG_SYSTEM_VERSION', '1.0.0');

/**
 * Verificar si necesita actualización
 */
function asg_needs_update() {
    $current_version = get_option('asg_system_version', '0.0.0');
    return version_compare($current_version, ASG_SYSTEM_VERSION, '<');
}

/**
 * Actualizar versión del sistema
 */
function asg_update_version() {
    update_option('asg_system_version', ASG_SYSTEM_VERSION);
    asg_log_success('System updated to version ' . ASG_SYSTEM_VERSION);
}

// Verificar actualizaciones al cargar admin
add_action('admin_init', function() {
    if (asg_needs_update()) {
        asg_update_version();
    }
});

?>
