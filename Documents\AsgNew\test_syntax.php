<?php
// Test simplificado de la estructura
function asg_render_lessons_page() {
    global $site_url, $course_code, $lesson_id, $auto_load_first, $access_denied, $access_message;
?>
<!DOCTYPE html>
<html>
<head>
    <title>Test</title>
</head>
<body>
    <?php if ($access_denied): ?>
        <div>Access Denied</div>
    <?php else: ?>
        <div>Content Here</div>
        <script>
            (function() {
                console.log('test');
            })();
        </script>
    <?php endif; ?>
</body>
</html>
<?php
} // Cierre de function asg_render_lessons_page()

function asg_lessons_shortcode($atts) {
    ob_start();
    asg_render_lessons_page();
    return ob_get_clean();
}

function asg_display_lessons() {
    echo do_shortcode('[asg_lessons]');
}

if (basename($_SERVER['PHP_SELF']) === 'test_syntax.php') {
    asg_render_lessons_page();
}
?>
