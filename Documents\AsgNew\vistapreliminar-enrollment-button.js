/**
 * ========================================
 * ASG ENROLLMENT BUTTON - vistapreliminar.php
 * ========================================
 * 
 * JavaScript para manejar el botón de inscripción en vistapreliminar.php
 * Incluye modal de login/registro y verificación de enrollment
 * 
 * Uso: Incluir al final de vistapreliminar.php
 * Requiere: COURSE_CODE y COURSE_PRICE definidos globalmente
 */

// Configuración del curso (se debe definir en vistapreliminar.php)
const COURSE_CODE = window.COURSE_CODE || 'course_1';
const COURSE_PRICE = window.COURSE_PRICE || 99;

// Función principal de inscripción
function startEnrollment(courseCode = COURSE_CODE, price = COURSE_PRICE) {
    console.log('ASG: Iniciando proceso de inscripción', { courseCode, price });
    
    // Verificar si el usuario está logueado
    if (!isUserLoggedIn()) {
        console.log('ASG: Usuario no logueado, mostrando modal');
        showLoginModal(courseCode, price);
        return;
    }
    
    // Usuario logueado - verificar si ya está inscrito
    checkExistingEnrollment(courseCode, price);
}

// Verificar si el usuario está logueado
function isUserLoggedIn() {
    // Múltiples métodos de verificación para máxima compatibilidad
    return document.body.classList.contains('logged-in') || 
           document.querySelector('.admin-bar') !== null ||
           window.wp_user_logged_in === true ||
           document.querySelector('body.logged-in') !== null;
}

// Verificar enrollment existente
async function checkExistingEnrollment(courseCode, price) {
    try {
        const response = await fetch(`/wp-json/asg/v1/check-enrollment?course=${courseCode}`);
        const result = await response.json();
        
        if (result.success && result.enrolled) {
            // Ya está inscrito
            showAlreadyEnrolledMessage(courseCode);
        } else {
            // No está inscrito - proceder al pago
            redirectToPayment(courseCode, price);
        }
    } catch (error) {
        console.error('ASG: Error verificando enrollment:', error);
        // En caso de error, proceder al pago (fail-safe)
        redirectToPayment(courseCode, price);
    }
}

// Mostrar modal de login/registro
function showLoginModal(courseCode, price) {
    // Crear modal si no existe
    let modal = document.getElementById('asg-login-modal');
    if (!modal) {
        modal = createLoginModal();
        document.body.appendChild(modal);
    }
    
    // Actualizar contenido del modal
    updateModalContent(modal, courseCode, price);
    
    // Mostrar modal
    modal.style.display = 'flex';
    document.body.style.overflow = 'hidden';
}

// Crear estructura del modal
function createLoginModal() {
    const modal = document.createElement('div');
    modal.id = 'asg-login-modal';
    modal.className = 'asg-modal-overlay';
    
    modal.innerHTML = `
        <div class="asg-modal-content">
            <button class="asg-modal-close" onclick="closeLoginModal()">&times;</button>
            <div id="asg-modal-body">
                <!-- Contenido dinámico -->
            </div>
        </div>
    `;
    
    // Event listener para cerrar al hacer clic fuera
    modal.addEventListener('click', function(e) {
        if (e.target === modal) {
            closeLoginModal();
        }
    });
    
    return modal;
}

// Actualizar contenido del modal
function updateModalContent(modal, courseCode, price) {
    const modalBody = modal.querySelector('#asg-modal-body');
    const currentUrl = encodeURIComponent(window.location.href);
    const registerUrl = `/register/?redirect_course=${courseCode}&price=${price}`;
    const loginUrl = `/wp-login.php?redirect_to=${currentUrl}`;
    
    modalBody.innerHTML = `
        <div class="login-modal-header">
            <h3>🎓 Para inscribirte necesitas una cuenta</h3>
            <p>Únete a miles de estudiantes que ya están aprendiendo</p>
        </div>
        
        <div class="login-options">
            <a href="${registerUrl}" class="btn btn-primary btn-large">
                <span class="btn-icon">✨</span>
                Crear Cuenta Nueva
                <small>Rápido y gratuito</small>
            </a>
            
            <div class="divider">
                <span>o</span>
            </div>
            
            <a href="${loginUrl}" class="btn btn-outline-primary btn-large">
                <span class="btn-icon">🔐</span>
                Iniciar Sesión
                <small>Ya tengo cuenta</small>
            </a>
        </div>
        
        <div class="benefits-section">
            <h5>¿Por qué crear una cuenta?</h5>
            <ul class="benefits-list">
                <li><span class="benefit-icon">✅</span> Acceso a todos tus cursos</li>
                <li><span class="benefit-icon">📊</span> Seguimiento de progreso</li>
                <li><span class="benefit-icon">🏆</span> Certificados descargables</li>
                <li><span class="benefit-icon">💬</span> Soporte técnico incluido</li>
            </ul>
        </div>
        
        <div class="course-reminder">
            <div class="reminder-content">
                <h6>📚 Curso seleccionado:</h6>
                <p><strong>${courseCode}</strong> - $${price} USD</p>
            </div>
        </div>
    `;
}

// Cerrar modal
function closeLoginModal() {
    const modal = document.getElementById('asg-login-modal');
    if (modal) {
        modal.style.display = 'none';
        document.body.style.overflow = '';
    }
}

// Mostrar mensaje de ya inscrito
function showAlreadyEnrolledMessage(courseCode) {
    // Crear notificación temporal
    const notification = document.createElement('div');
    notification.className = 'asg-notification asg-success';
    notification.innerHTML = `
        <div class="notification-content">
            <h4>✅ Ya estás inscrito</h4>
            <p>Ya tienes acceso a este curso.</p>
            <div class="notification-actions">
                <a href="/lessons/?course=${courseCode}" class="btn btn-success">Ir a las Lecciones</a>
                <button onclick="this.parentElement.parentElement.parentElement.remove()" class="btn btn-secondary">Cerrar</button>
            </div>
        </div>
    `;
    
    // Insertar al inicio del body
    document.body.insertBefore(notification, document.body.firstChild);
    
    // Auto-remover después de 10 segundos
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 10000);
}

// Redirect al pago
function redirectToPayment(courseCode, price) {
    const paymentUrl = `/payment/?course=${courseCode}&price=${price}`;
    console.log('ASG: Redirigiendo a pago:', paymentUrl);
    window.location.href = paymentUrl;
}

// Event listeners para inicialización
document.addEventListener('DOMContentLoaded', function() {
    console.log('ASG Enrollment Button initialized for course:', COURSE_CODE);
    
    // Buscar botones de inscripción existentes y agregar event listeners
    const enrollButtons = document.querySelectorAll('[onclick*="startEnrollment"], .enrollment-button, .inscribirse-btn');
    enrollButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            startEnrollment();
        });
    });
});

// Función para ser llamada desde HTML
window.startEnrollment = startEnrollment;

// Inyectar estilos CSS
function injectModalStyles() {
    if (document.getElementById('asg-modal-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'asg-modal-styles';
    styles.textContent = `
        /* ASG Modal Styles */
        .asg-modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: none;
            justify-content: center;
            align-items: center;
            z-index: 10000;
            backdrop-filter: blur(5px);
        }

        .asg-modal-content {
            background: white;
            border-radius: 15px;
            max-width: 500px;
            width: 90%;
            max-height: 90vh;
            overflow-y: auto;
            position: relative;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease-out;
        }

        @keyframes modalSlideIn {
            from {
                opacity: 0;
                transform: translateY(-50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .asg-modal-close {
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 24px;
            cursor: pointer;
            color: #666;
            z-index: 1;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .asg-modal-close:hover {
            background: #f0f0f0;
            color: #333;
        }

        .login-modal-header {
            text-align: center;
            padding: 30px 30px 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }

        .login-modal-header h3 {
            margin: 0 0 10px 0;
            font-size: 24px;
        }

        .login-modal-header p {
            margin: 0;
            opacity: 0.9;
            font-size: 16px;
        }

        .login-options {
            padding: 30px;
        }

        .btn {
            display: block;
            width: 100%;
            padding: 15px 20px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            border: 2px solid transparent;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .btn-large {
            font-size: 16px;
            margin-bottom: 15px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-color: #667eea;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-outline-primary {
            background: white;
            color: #667eea;
            border-color: #667eea;
        }

        .btn-outline-primary:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
        }

        .btn-icon {
            font-size: 18px;
            margin-right: 8px;
        }

        .btn small {
            display: block;
            font-size: 12px;
            opacity: 0.8;
            font-weight: normal;
            margin-top: 2px;
        }

        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
        }

        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }

        .divider span {
            background: white;
            padding: 0 15px;
            color: #666;
            font-size: 14px;
        }

        .benefits-section {
            padding: 0 30px 20px;
        }

        .benefits-section h5 {
            margin: 0 0 15px 0;
            color: #333;
            font-size: 16px;
        }

        .benefits-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .benefits-list li {
            padding: 8px 0;
            display: flex;
            align-items: center;
            color: #555;
            font-size: 14px;
        }

        .benefit-icon {
            margin-right: 10px;
            font-size: 16px;
        }

        .course-reminder {
            background: #f8f9fa;
            margin: 0 30px 30px;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }

        .course-reminder h6 {
            margin: 0 0 8px 0;
            color: #333;
            font-size: 14px;
        }

        .course-reminder p {
            margin: 0;
            color: #667eea;
            font-weight: 600;
        }

        /* Notification Styles */
        .asg-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            max-width: 400px;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 9999;
            animation: slideInRight 0.3s ease-out;
        }

        .asg-notification.asg-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(100%);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .notification-content h4 {
            margin: 0 0 10px 0;
            font-size: 18px;
        }

        .notification-content p {
            margin: 0 0 15px 0;
        }

        .notification-actions {
            display: flex;
            gap: 10px;
        }

        .btn-success {
            background: #28a745;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            text-decoration: none;
            font-size: 14px;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            padding: 8px 16px;
            border-radius: 4px;
            border: none;
            cursor: pointer;
            font-size: 14px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .asg-modal-content {
                width: 95%;
                margin: 20px;
            }

            .login-modal-header {
                padding: 20px 20px 15px;
            }

            .login-options {
                padding: 20px;
            }

            .benefits-section {
                padding: 0 20px 15px;
            }

            .course-reminder {
                margin: 0 20px 20px;
            }

            .asg-notification {
                top: 10px;
                right: 10px;
                left: 10px;
                max-width: none;
            }
        }
    `;

    document.head.appendChild(styles);
}

// Inyectar estilos al cargar
document.addEventListener('DOMContentLoaded', function() {
    injectModalStyles();
});
