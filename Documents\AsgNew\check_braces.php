<?php
// Script para encontrar llaves desbalanceadas
$file = 'Documents/AsgNew/snippets/lessons-clean.php';
$content = file_get_contents($file);
$lines = explode("\n", $content);

$stack = [];
$braceCount = 0;
$errors = [];

foreach ($lines as $lineNum => $line) {
    $lineNumber = $lineNum + 1;
    
    // Contar llaves de apertura
    $openBraces = substr_count($line, '{');
    $closeBraces = substr_count($line, '}');
    
    $braceCount += $openBraces - $closeBraces;
    
    // Si el balance se vuelve negativo, hay llaves de cierre extra
    if ($braceCount < 0) {
        $errors[] = "Línea $lineNumber: Llave de cierre extra - Balance: $braceCount";
        $errors[] = "Contenido: " . trim($line);
        $errors[] = "---";
    }
}

echo "Balance final de llaves: $braceCount\n";
echo "Errores encontrados:\n";
foreach ($errors as $error) {
    echo $error . "\n";
}

// Mostrar las últimas 20 líneas con llaves
echo "\nÚltimas líneas con llaves:\n";
foreach ($lines as $lineNum => $line) {
    if (strpos($line, '{') !== false || strpos($line, '}') !== false) {
        $lineNumber = $lineNum + 1;
        if ($lineNumber > count($lines) - 30) {
            echo "Línea $lineNumber: " . trim($line) . "\n";
        }
    }
}
?>
