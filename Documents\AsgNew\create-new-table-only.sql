-- =====================================================
-- CREAR SOLO LA NUEVA TABLA OPTIMIZADA
-- Para resolver el error 403 inmediatamente
-- =====================================================

-- CREAR NUEVA TABLA wpic_asg_user_progress
CREATE TABLE IF NOT EXISTS wpic_asg_user_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_code VARCHAR(100) NOT NULL,
    completed_lessons JSON NOT NULL,           -- [51, 52, 57, 58, 59, 60]
    total_lessons INT NOT NULL,
    completed_count INT NOT NULL,
    progress_percentage DECIMAL(5,2) NOT NULL,
    last_lesson_id INT NULL,
    last_completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_course (user_id, course_code),
    INDEX idx_user_id (user_id),
    INDEX idx_course_code (course_code),
    INDEX idx_progress (progress_percentage)
);

-- VERIFICAR QUE SE CREÓ CORRECTAMENTE
SELECT 'TABLA CREADA EXITOSAMENTE' as resultado;

-- MOSTRAR ESTRUCTURA
DESCRIBE wpic_asg_user_progress;

-- CREAR UN REGISTRO DE PRUEBA (OPCIONAL)
-- Reemplazar 1 y 'course_test' con datos reales
/*
INSERT INTO wpic_asg_user_progress (
    user_id, 
    course_code, 
    completed_lessons, 
    total_lessons, 
    completed_count, 
    progress_percentage
) VALUES (
    1, 
    'course_curso_de_finanzas_1751906893', 
    '[]', 
    10, 
    0, 
    0.00
);
*/

SELECT 'EJECUTAR EL DIAGNÓSTICO: /wp-json/asg/v1/system-diagnostic' as siguiente_paso;
