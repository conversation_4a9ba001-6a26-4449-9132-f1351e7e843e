-- ========================================
-- ASG STUDENT FLOW DATABASE STRUCTURE
-- ========================================
-- Estructura optimizada para el flujo del estudiante
-- Compatible con WordPress y escalable

-- 1. ESTUDIANTES (Solo lo esencial)
CREATE TABLE wpic_asg_students (
    id_student INT AUTO_INCREMENT PRIMARY KEY,
    id_user INT NOT NULL,
    username VARCHAR(100) NOT NULL,
    email VARCHAR(100) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    -- ✅ Consistente con wpic_courses.id_user
    UNIQUE KEY unique_user (id_user),
    INDEX idx_user (id_user),
    INDEX idx_email (email)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;



-- 2. INSCRIPCIONES (Solo lo necesario para el negocio)
CREATE TABLE wpic_asg_enrollments (
    id_enrollment INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    course_id INT NOT NULL,
    paypal_payment_id VARCHAR(100) NOT NULL,
    amount_paid DECIMAL(10,2) NOT NULL,
    payment_date DATETIME NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES wpic_asg_students(id_student) ON DELETE CASCADE,
    FOREIGN KEY (course_id) REFERENCES wpic_courses(id_course) ON DELETE CASCADE,
    UNIQUE KEY unique_student_course (student_id, course_id),
    INDEX idx_student (student_id),
    INDEX idx_course (course_id),
    INDEX idx_payment_date (payment_date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 3. PROGRESO (Tracking simple y efectivo)
CREATE TABLE wpic_asg_progress (
    id_progress INT AUTO_INCREMENT PRIMARY KEY,
    student_id INT NOT NULL,
    lesson_id INT NOT NULL,
    completed TINYINT(1) DEFAULT 0,
    completion_date DATETIME NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

    FOREIGN KEY (student_id) REFERENCES wpic_asg_students(id_student) ON DELETE CASCADE,
    FOREIGN KEY (lesson_id) REFERENCES wpic_lessons(id_lesson) ON DELETE CASCADE,
    UNIQUE KEY unique_student_lesson (student_id, lesson_id),
    INDEX idx_student (student_id),
    INDEX idx_lesson (lesson_id),
    INDEX idx_completed (completed)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- ========================================
-- COMENTARIOS Y NOTAS
-- ========================================

/*
NOTAS IMPORTANTES:

1. NO se usa FOREIGN KEY a wpic_users para evitar problemas de permisos
2. La integridad con usuarios de WordPress se maneja en PHP
3. Campo id_user es consistente con wpic_courses.id_user
4. Cada enrollment representa una compra independiente
5. No se necesita tabla separada de "cursos_comprados"
6. El progreso se inicializa automáticamente al crear enrollment

CONSULTAS ÚTILES:

-- Obtener cursos comprados por estudiante:
SELECT c.name_course, e.amount_paid, e.payment_date
FROM wpic_asg_enrollments e
JOIN wpic_courses c ON e.course_id = c.id_course
WHERE e.student_id = ?;

-- Verificar si compró un curso:
SELECT COUNT(*) FROM wpic_asg_enrollments
WHERE student_id = ? AND course_id = ?;

-- Obtener progreso de un curso:
SELECT COUNT(*) as total, SUM(completed) as completed
FROM wpic_asg_progress sp
JOIN wpic_lessons l ON sp.lesson_id = l.id_lesson
JOIN wpic_modules m ON l.code_module = m.code_module
JOIN wpic_courses c ON m.code_course = c.code_course
WHERE sp.student_id = ? AND c.id_course = ?;

-- Obtener estudiante por usuario WordPress:
SELECT * FROM wpic_asg_students WHERE id_user = ?;

-- JOIN con usuarios WordPress:
SELECT s.*, u.user_login, u.user_email
FROM wpic_asg_students s
JOIN wpic_users u ON s.id_user = u.ID;
*/