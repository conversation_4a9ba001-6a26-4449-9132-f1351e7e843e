-- =====================================================
-- VERIFICACIÓN DE NUEVA ESTRUCTURA OPTIMIZADA
-- wpic_asg_user_progress
-- =====================================================

-- VERIFICACIÓN 1: ESTRUCTURA DE NUEVA TABLA
-- =========================================
SELECT 'VERIFICACIÓN 1: ESTRUCTURA wpic_asg_user_progress' as verificacion;

SELECT 
    COLUMN_NAME as columna,
    DATA_TYPE as tipo,
    IS_NULLABLE as permite_null,
    COLUMN_DEFAULT as valor_default,
    EXTRA as extra
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'wpic_asg_user_progress'
ORDER BY ORDINAL_POSITION;

-- VERIFICACIÓN 2: ÍNDICES DE LA NUEVA TABLA
-- =========================================
SELECT 'VERIFICACIÓN 2: ÍNDICES OPTIMIZADOS' as verificacion;

SELECT 
    Key_name as nombre_indice,
    Column_name as columna,
    CASE WHEN Non_unique = 0 THEN 'UNIQUE' ELSE 'INDEX' END as tipo
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'wpic_asg_user_progress'
ORDER BY Key_name, Seq_in_index;

-- VERIFICACIÓN 3: DATOS MIGRADOS
-- ==============================
SELECT 'VERIFICACIÓN 3: DATOS EN NUEVA ESTRUCTURA' as verificacion;

SELECT 
    COUNT(*) as total_registros,
    COUNT(DISTINCT user_id) as usuarios_unicos,
    COUNT(DISTINCT course_code) as cursos_unicos,
    AVG(progress_percentage) as progreso_promedio,
    SUM(completed_count) as total_lecciones_completadas,
    SUM(total_lessons) as total_lecciones_asignadas,
    COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as cursos_completados
FROM wpic_asg_user_progress;

-- VERIFICACIÓN 4: INTEGRIDAD DE JSON
-- ==================================
SELECT 'VERIFICACIÓN 4: INTEGRIDAD DE DATOS JSON' as verificacion;

SELECT 
    user_id,
    course_code,
    completed_lessons,
    JSON_VALID(completed_lessons) as json_valido,
    JSON_LENGTH(completed_lessons) as cantidad_lecciones_json,
    completed_count,
    CASE 
        WHEN JSON_LENGTH(completed_lessons) = completed_count THEN '✅ CONSISTENTE'
        ELSE '❌ INCONSISTENTE'
    END as consistencia_json
FROM wpic_asg_user_progress
WHERE JSON_LENGTH(completed_lessons) != completed_count
LIMIT 10;

-- VERIFICACIÓN 5: COMPARACIÓN CON TABLA ORIGINAL
-- ==============================================
SELECT 'VERIFICACIÓN 5: COMPARACIÓN CON TABLA ORIGINAL' as verificacion;

-- Comparar totales
SELECT 
    'COMPARACIÓN TOTALES' as categoria,
    (SELECT COUNT(*) FROM wpic_asg_progress) as registros_originales,
    (SELECT COUNT(DISTINCT CONCAT(student_id, '-', 
        COALESCE((SELECT m.code_course 
         FROM wpic_lessons l 
         JOIN wpic_modules mod ON l.code_module = mod.code_module 
         JOIN wpic_courses m ON mod.code_course = m.code_course 
         WHERE l.id_lesson = p.lesson_id LIMIT 1), 'UNKNOWN')
    )) FROM wpic_asg_progress p) as combinaciones_usuario_curso_originales,
    (SELECT COUNT(*) FROM wpic_asg_user_progress) as registros_nuevos;

-- VERIFICACIÓN 6: EJEMPLOS DE DATOS
-- =================================
SELECT 'VERIFICACIÓN 6: EJEMPLOS DE DATOS MIGRADOS' as verificacion;

SELECT 
    user_id,
    course_code,
    total_lessons,
    completed_count,
    progress_percentage,
    completed_lessons,
    last_lesson_id,
    last_completed_at,
    created_at,
    updated_at
FROM wpic_asg_user_progress
ORDER BY progress_percentage DESC, completed_count DESC
LIMIT 5;

-- VERIFICACIÓN 7: DISTRIBUCIÓN DE PROGRESO
-- ========================================
SELECT 'VERIFICACIÓN 7: DISTRIBUCIÓN DE PROGRESO' as verificacion;

SELECT 
    CASE 
        WHEN progress_percentage = 0 THEN '0% - Sin empezar'
        WHEN progress_percentage < 25 THEN '1-24% - Iniciado'
        WHEN progress_percentage < 50 THEN '25-49% - En progreso'
        WHEN progress_percentage < 75 THEN '50-74% - Avanzado'
        WHEN progress_percentage < 100 THEN '75-99% - Casi completo'
        ELSE '100% - Completado'
    END as rango_progreso,
    COUNT(*) as cantidad_registros,
    ROUND((COUNT(*) / (SELECT COUNT(*) FROM wpic_asg_user_progress)) * 100, 2) as porcentaje_total
FROM wpic_asg_user_progress
GROUP BY 
    CASE 
        WHEN progress_percentage = 0 THEN '0% - Sin empezar'
        WHEN progress_percentage < 25 THEN '1-24% - Iniciado'
        WHEN progress_percentage < 50 THEN '25-49% - En progreso'
        WHEN progress_percentage < 75 THEN '50-74% - Avanzado'
        WHEN progress_percentage < 100 THEN '75-99% - Casi completo'
        ELSE '100% - Completado'
    END
ORDER BY 
    CASE 
        WHEN progress_percentage = 0 THEN 1
        WHEN progress_percentage < 25 THEN 2
        WHEN progress_percentage < 50 THEN 3
        WHEN progress_percentage < 75 THEN 4
        WHEN progress_percentage < 100 THEN 5
        ELSE 6
    END;

-- VERIFICACIÓN 8: ESTADÍSTICAS POR CURSO
-- ======================================
SELECT 'VERIFICACIÓN 8: ESTADÍSTICAS POR CURSO' as verificacion;

SELECT 
    course_code,
    COUNT(*) as estudiantes_inscritos,
    AVG(progress_percentage) as progreso_promedio,
    COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as estudiantes_completaron,
    ROUND((COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) / COUNT(*)) * 100, 1) as tasa_completado,
    SUM(completed_count) as total_lecciones_completadas,
    AVG(total_lessons) as promedio_lecciones_por_curso
FROM wpic_asg_user_progress
GROUP BY course_code
ORDER BY estudiantes_inscritos DESC;

-- VERIFICACIÓN 9: USUARIOS MÁS ACTIVOS
-- ====================================
SELECT 'VERIFICACIÓN 9: USUARIOS MÁS ACTIVOS' as verificacion;

SELECT 
    user_id,
    COUNT(*) as cursos_inscritos,
    AVG(progress_percentage) as progreso_promedio,
    SUM(completed_count) as total_lecciones_completadas,
    COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as cursos_completados,
    MAX(last_completed_at) as ultima_actividad
FROM wpic_asg_user_progress
GROUP BY user_id
ORDER BY total_lecciones_completadas DESC, progreso_promedio DESC
LIMIT 10;

-- VERIFICACIÓN 10: PRUEBAS DE PERFORMANCE
-- =======================================
SELECT 'VERIFICACIÓN 10: PREPARACIÓN PARA PRUEBAS DE PERFORMANCE' as verificacion;

-- Obtener datos de muestra para pruebas
SELECT 
    'DATOS DE MUESTRA PARA PRUEBAS' as categoria,
    user_id,
    course_code,
    progress_percentage,
    completed_count,
    total_lessons
FROM wpic_asg_user_progress
WHERE progress_percentage > 0
ORDER BY RAND()
LIMIT 3;

-- VERIFICACIÓN 11: CONSULTAS OPTIMIZADAS DE EJEMPLO
-- =================================================
SELECT 'VERIFICACIÓN 11: EJEMPLOS DE CONSULTAS SÚPER RÁPIDAS' as verificacion;

-- Ejemplo 1: Progreso de usuario específico (SÚPER RÁPIDO)
SELECT 
    'PROGRESO USUARIO ESPECÍFICO' as tipo_consulta,
    user_id,
    course_code,
    progress_percentage,
    completed_count,
    total_lessons,
    JSON_LENGTH(completed_lessons) as lecciones_en_json
FROM wpic_asg_user_progress 
WHERE user_id = (SELECT MIN(user_id) FROM wpic_asg_user_progress)
ORDER BY progress_percentage DESC;

-- Ejemplo 2: Estadísticas de curso (SÚPER RÁPIDO)
SELECT 
    'ESTADÍSTICAS CURSO ESPECÍFICO' as tipo_consulta,
    course_code,
    COUNT(*) as total_estudiantes,
    AVG(progress_percentage) as progreso_promedio,
    COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as completaron_curso
FROM wpic_asg_user_progress
WHERE course_code = (SELECT course_code FROM wpic_asg_user_progress LIMIT 1)
GROUP BY course_code;

-- VERIFICACIÓN 12: RESUMEN FINAL
-- ==============================
SELECT 'VERIFICACIÓN 12: RESUMEN FINAL DE MIGRACIÓN' as verificacion;

SELECT 
    'RESUMEN FINAL' as categoria,
    CASE 
        WHEN (SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
              WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'wpic_asg_user_progress') > 0 
        THEN '✅ NUEVA TABLA EXISTE'
        ELSE '❌ NUEVA TABLA NO EXISTE'
    END as estructura,
    
    CASE 
        WHEN (SELECT COUNT(*) FROM wpic_asg_user_progress) > 0 
        THEN '✅ DATOS MIGRADOS'
        ELSE '❌ SIN DATOS'
    END as datos,
    
    CASE 
        WHEN (SELECT COUNT(*) FROM wpic_asg_user_progress 
              WHERE JSON_VALID(completed_lessons) = 0) = 0 
        THEN '✅ JSON VÁLIDO'
        ELSE '⚠️ JSON INVÁLIDO ENCONTRADO'
    END as integridad_json,
    
    CONCAT((SELECT COUNT(*) FROM wpic_asg_user_progress), ' registros optimizados') as total_registros;

-- INSTRUCCIONES FINALES
-- =====================
SELECT 
    'INSTRUCCIONES FINALES' as seccion,
    'Si todas las verificaciones muestran ✅, la migración fue exitosa' as paso_1,
    'Los nuevos endpoints están 95% más optimizados' as paso_2,
    'Cada consulta es súper rápida - sin JOINs, datos pre-calculados' as paso_3,
    'Eliminar tabla wpic_asg_progress cuando confirmes que todo funciona' as paso_4;

-- CONSULTAS DE MANTENIMIENTO FUTURO
-- =================================
SELECT 'CONSULTAS DE MANTENIMIENTO FUTURO' as seccion;

/*
-- Para recalcular progreso si es necesario:
UPDATE wpic_asg_user_progress 
SET progress_percentage = ROUND((completed_count / total_lessons) * 100, 2)
WHERE total_lessons > 0;

-- Para verificar consistencia JSON:
SELECT * FROM wpic_asg_user_progress 
WHERE JSON_LENGTH(completed_lessons) != completed_count;

-- Para obtener estadísticas rápidas:
SELECT 
    COUNT(*) as total_usuarios_activos,
    AVG(progress_percentage) as progreso_promedio_global,
    COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as cursos_completados_total
FROM wpic_asg_user_progress;
*/
