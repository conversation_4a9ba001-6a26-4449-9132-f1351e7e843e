/**
 * ASG All Courses Optimized - WordPress Code Snippet
 *
 * Descripción: Página optimizada para mostrar todos los cursos usando endpoints reales
 * Versión: 3.0.0 - FULLY OPTIMIZED WITH REAL DATA
 * Autor: ING. Bryan Marc S.M
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Crear página de todos los cursos optimizada
 */
function asg_create_all_courses_optimized_page() {
    // Verificar permisos
    if (!current_user_can('manage_options')) {
        wp_die(__('No tienes permisos para acceder a esta página.'));
    }

    // Obtener URL base
    $site_url = get_site_url();
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>All Courses - ASG Management</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <style>
            .wg-li weglot-lang weglot-language weglot-flags flag-3 wg-es{
                display: none;
            }
            
            /* Navbar Styles */
            .navbar {
                background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
                height: 70px;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .navbar-brand img {
                width: 130px;
                height: auto;
            }

            .navbar-nav .nav-link {
                color: rgba(255,255,255,0.9) !important;
                font-weight: 500;
                padding: 0.5rem 1rem !important;
                border-radius: 6px;
                transition: all 0.2s;
            }

            .navbar-nav .nav-link:hover {
                color: white !important;
                background-color: rgba(255,255,255,0.1);
            }

            .navbar-nav .nav-link.active {
                color: white !important;
                background-color: rgba(255,255,255,0.2);
            }

            /* Main Content */
            body {
                font-family: 'Inter', sans-serif;
                background-color: #f8fafc;
                padding-top: 70px;
            }

            .main-content {
                padding: 2rem;
                max-width: 1400px;
                margin: 0 auto;
            }

            /* Page Header */
            .page-header {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                display: flex;
                justify-content: space-between;
                align-items: center;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .page-title {
                font-size: 2rem;
                font-weight: 700;
                color: #1f2937;
                margin: 0;
            }

            .page-subtitle {
                color: #6c757d;
                font-size: 1.1rem;
                margin: 0;
            }

            /* Filters */
            .filters-section {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                margin-bottom: 2rem;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .filter-group {
                display: flex;
                gap: 1rem;
                align-items: center;
                flex-wrap: wrap;
            }

            .filter-label {
                font-weight: 600;
                color: #374151;
                margin-right: 0.5rem;
            }

            /* View Toggle */
            .view-toggle {
                display: flex;
                background: #f3f4f6;
                border-radius: 8px;
                padding: 0.25rem;
            }

            .view-btn {
                padding: 0.5rem 1rem;
                border: none;
                background: transparent;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s;
                color: #6b7280;
                font-weight: 500;
            }

            .view-btn.active {
                background: white;
                color: #2563eb;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            /* Courses Container */
            .courses-container {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                box-shadow: 0 2px 10px rgba(0,0,0,0.05);
                border: 1px solid #e9ecef;
            }

            .courses-grid {
                display: grid;
                grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
                gap: 1.5rem;
            }

            .courses-list {
                display: flex;
                flex-direction: column;
                gap: 1rem;
            }

            /* Course Card */
            .course-card {
                border: 1px solid #e5e7eb;
                border-radius: 12px;
                overflow: hidden;
                transition: all 0.3s ease;
                background: white;
                cursor: pointer;
            }

            .course-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 8px 25px rgba(0,0,0,0.1);
                border-color: #2563eb;
            }

            .course-image {
                height: 180px;
                background-size: cover;
                background-position: center;
                background-color: #f3f4f6;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: center;
                color: #9ca3af;
                font-size: 3rem;
            }

            .course-status-badge {
                position: absolute;
                top: 0.75rem;
                right: 0.75rem;
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 600;
                text-transform: uppercase;
            }

            .status-published {
                background-color: #d1fae5;
                color: #065f46;
            }

            .status-draft {
                background-color: #fef3c7;
                color: #92400e;
            }

            .course-content {
                padding: 1.5rem;
            }

            .course-category {
                font-size: 0.75rem;
                font-weight: 600;
                color: #2563eb;
                text-transform: uppercase;
                margin-bottom: 0.5rem;
            }

            .course-title {
                font-size: 1.25rem;
                font-weight: 700;
                color: #1f2937;
                margin-bottom: 0.5rem;
                line-height: 1.3;
            }

            .course-description {
                color: #6b7280;
                font-size: 0.875rem;
                line-height: 1.5;
                margin-bottom: 1rem;
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            .course-meta {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
            }

            .course-stats {
                display: flex;
                gap: 1rem;
                font-size: 0.75rem;
                color: #6b7280;
            }

            .course-stats span {
                display: flex;
                align-items: center;
                gap: 0.25rem;
            }

            .course-price {
                font-size: 1.125rem;
                font-weight: 700;
                color: #059669;
            }

            .course-actions {
                display: flex;
                gap: 0.5rem;
            }

            .btn-action {
                flex: 1;
                padding: 0.5rem 1rem;
                border: 1px solid #d1d5db;
                border-radius: 6px;
                background: white;
                color: #374151;
                font-size: 0.875rem;
                font-weight: 500;
                cursor: pointer;
                transition: all 0.2s;
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.25rem;
            }

            .btn-action:hover {
                background-color: #f9fafb;
                border-color: #9ca3af;
            }

            .btn-action.primary {
                background-color: #2563eb;
                border-color: #2563eb;
                color: white;
            }

            .btn-action.primary:hover {
                background-color: #1d4ed8;
            }

            .btn-action.danger {
                color: #dc2626;
                border-color: #fecaca;
            }

            .btn-action.danger:hover {
                background-color: #fef2f2;
                border-color: #fca5a5;
            }

            .btn-action.success {
                color: #059669;
                border-color: #a7f3d0;
            }

            .btn-action.success:hover {
                background-color: #ecfdf5;
                border-color: #6ee7b7;
            }

            /* Loading States */
            .loading-skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
                border-radius: 4px;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            /* Responsive */
            @media (max-width: 768px) {
                .main-content {
                    padding: 1rem;
                }

                .page-header {
                    flex-direction: column;
                    align-items: flex-start;
                }

                .courses-grid {
                    grid-template-columns: 1fr;
                }

                .filter-group {
                    flex-direction: column;
                    align-items: flex-start;
                }
            }

            /* Notifications */
            .notification {
                position: fixed;
                top: 90px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1050;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification.success { background-color: #10b981; }
            .notification.error { background-color: #ef4444; }
            .notification.warning { background-color: #f59e0b; }
            .notification.info { background-color: #3b82f6; }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo $site_url; ?>/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo" style="width:130px;height:auto;">
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/admin-dashboard">
                                <i class="bi bi-speedometer2 me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo $site_url; ?>/all-courses">
                                <i class="bi bi-collection me-1"></i>All Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/new-course">
                                <i class="bi bi-plus-circle me-1"></i>New Course
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Page Header -->
            <div class="page-header">
                <div>
                    <h1 class="page-title">📚 All Courses</h1>
                    <p class="page-subtitle">Manage and organize all your courses from one place</p>
                </div>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="refreshCourses()">
                        <i class="bi bi-arrow-clockwise"></i> Refresh
                    </button>
                    <button class="btn btn-success" onclick="window.location.href='<?php echo $site_url; ?>/new-course'">
                        <i class="bi bi-plus-circle"></i> New Course
                    </button>
                </div>
            </div>

            <!-- Filters Section -->
            <div class="filters-section">
                <div class="filter-group">
                    <span class="filter-label">Status:</span>
                    <select class="form-select" style="width: auto;" id="statusFilter" onchange="filterCourses()">
                        <option value="all">All Status</option>
                        <option value="published">Published</option>
                        <option value="draft">Draft</option>
                    </select>

                    <span class="filter-label ms-3">Category:</span>
                    <select class="form-select" style="width: auto;" id="categoryFilter" onchange="filterCourses()">
                        <option value="all">All Categories</option>
                        <option value="finance">Finance</option>
                        <option value="marketing">Marketing</option>
                        <option value="technology">Technology</option>
                        <option value="business">Business</option>
                        <option value="personal-development">Personal Development</option>
                        <option value="health">Health & Wellness</option>
                        <option value="creative">Creative</option>
                        <option value="language">Language</option>
                    </select>

                    <div class="ms-auto">
                        <div class="view-toggle">
                            <button class="view-btn active" id="gridViewBtn" onclick="toggleView('grid')">
                                <i class="bi bi-grid-3x3-gap"></i> Grid
                            </button>
                            <button class="view-btn" id="listViewBtn" onclick="toggleView('list')">
                                <i class="bi bi-list"></i> List
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Courses Container -->
            <div class="courses-container">
                <div id="coursesGrid" class="courses-grid">
                    <!-- Loading skeleton -->
                    <div class="course-card">
                        <div class="loading-skeleton" style="height: 180px;"></div>
                        <div class="course-content">
                            <div class="loading-skeleton" style="height: 1rem; margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 1.5rem; margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 3rem; margin-bottom: 1rem;"></div>
                            <div class="loading-skeleton" style="height: 1rem;"></div>
                        </div>
                    </div>
                    <div class="course-card">
                        <div class="loading-skeleton" style="height: 180px;"></div>
                        <div class="course-content">
                            <div class="loading-skeleton" style="height: 1rem; margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 1.5rem; margin-bottom: 0.5rem;"></div>
                            <div class="loading-skeleton" style="height: 3rem; margin-bottom: 1rem;"></div>
                            <div class="loading-skeleton" style="height: 1rem;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <script>
            // ========================================
            // ASG API CLIENT OPTIMIZED
            // ========================================
            class ASGApiClientOptimized {
                constructor() {
                    this.baseUrl = '<?php echo rest_url('asg/v1'); ?>';
                    this.nonce = '<?php echo wp_create_nonce('wp_rest'); ?>';
                }

                async request(endpoint, options = {}) {
                    const url = `${this.baseUrl}${endpoint}`;
                    const config = {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': this.nonce,
                            ...options.headers
                        },
                        ...options
                    };

                    try {
                        const response = await fetch(url, config);
                        const data = await response.json();

                        if (!response.ok) {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        }

                        return data;
                    } catch (error) {
                        console.error('API Error:', error);
                        throw error;
                    }
                }

                async get(endpoint, params = {}) {
                    const queryString = new URLSearchParams(params).toString();
                    const finalEndpoint = queryString ? `${endpoint}?${queryString}` : endpoint;
                    return this.request(finalEndpoint);
                }

                async delete(endpoint) {
                    return this.request(endpoint, { method: 'DELETE' });
                }

                async put(endpoint, data = {}) {
                    return this.request(endpoint, {
                        method: 'PUT',
                        body: JSON.stringify(data)
                    });
                }

                // Get all courses with stats
                async getCourses(params = {}) {
                    const defaultParams = {
                        include: 'stats,images',
                        per_page: 100
                    };
                    const finalParams = { ...defaultParams, ...params };
                    return this.get('/courses/api', finalParams);
                }

                // Delete course
                async deleteCourse(courseId) {
                    return this.delete(`/courses/api/${courseId}`);
                }

                // Update course
                async updateCourse(courseId, data) {
                    return this.put(`/courses/api/${courseId}`, data);
                }
            }

            const ASG_API = new ASGApiClientOptimized();

            // ========================================
            // GLOBAL VARIABLES
            // ========================================
            let allCourses = [];
            let filteredCourses = [];
            let currentView = 'grid';

            // ========================================
            // UTILITY FUNCTIONS
            // ========================================

            // Show notification
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => notification.classList.add('show'), 100);
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }

            // Format currency
            function formatCurrency(amount) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'USD'
                }).format(amount || 0);
            }

            // Get category info
            function getCategoryInfo(category) {
                const categories = {
                    'finance': { icon: '💰', label: 'Finance' },
                    'marketing': { icon: '📈', label: 'Marketing' },
                    'technology': { icon: '💻', label: 'Technology' },
                    'business': { icon: '💼', label: 'Business' },
                    'personal-development': { icon: '🚀', label: 'Personal Development' },
                    'health': { icon: '🏥', label: 'Health & Wellness' },
                    'creative': { icon: '🎨', label: 'Creative' },
                    'language': { icon: '🗣️', label: 'Language' },
                    'general': { icon: '📚', label: 'General' }
                };
                return categories[category] || categories['general'];
            }

            // Get status info
            function getStatusInfo(status) {
                return {
                    'published': { badge: 'status-published', label: 'Published' },
                    'draft': { badge: 'status-draft', label: 'Draft' }
                }[status] || { badge: 'status-draft', label: 'Draft' };
            }

            // ========================================
            // MAIN FUNCTIONS
            // ========================================

            // Load all courses
            async function loadCourses() {
                try {
                    console.log('🚀 Loading courses with optimized API...');

                    // Load all courses including drafts for admin interface
                    const [publishedResponse, draftResponse] = await Promise.all([
                        ASG_API.getCourses({
                            include: 'stats,images',
                            per_page: 100
                        }),
                        fetch('/wp-json/asg/v1/courses/api/draft?include=stats,images').then(r => r.json())
                    ]);

                    console.log('📚 Published courses response:', publishedResponse);
                    console.log('🗂️ Draft courses response:', draftResponse);

                    // Combine published and draft courses
                    let combinedCourses = [];

                    if (publishedResponse.success && publishedResponse.data) {
                        combinedCourses = [...publishedResponse.data];
                    }

                    if (draftResponse.success && draftResponse.data) {
                        combinedCourses = [...combinedCourses, ...draftResponse.data];
                    }

                    if (combinedCourses.length > 0) {
                        allCourses = combinedCourses;
                        filteredCourses = allCourses;

                        // Debug: Log course categories and statuses
                        console.log('📊 All courses breakdown:');
                        const categoryCounts = {};
                        const statusCounts = {};
                        allCourses.forEach(course => {
                            const category = course.category_course || 'undefined';
                            const status = course.status_course || 'undefined';
                            categoryCounts[category] = (categoryCounts[category] || 0) + 1;
                            statusCounts[status] = (statusCounts[status] || 0) + 1;
                            console.log(`📝 "${course.name_course}": category="${course.category_course}", status="${course.status_course}"`);
                        });
                        console.log('📈 Category summary:', categoryCounts);
                        console.log('📊 Status summary:', statusCounts);

                        // Update category filter with real categories
                        updateCategoryFilter(categoryCounts);

                        renderCourses();
                        showNotification(`✅ Loaded ${allCourses.length} courses successfully! (${statusCounts.published || 0} published, ${statusCounts.draft || 0} draft)`, 'success');
                    } else {
                        throw new Error('No data received from either endpoint');
                    }
                } catch (error) {
                    console.error('❌ Error loading courses:', error);
                    showNotification('⚠️ Error loading courses', 'error');
                    renderCourses([]); // Show empty state
                }
            }

            // Refresh courses
            async function refreshCourses() {
                showNotification('🔄 Refreshing courses...', 'info');
                await loadCourses();
            }



            // Update category filter with real categories from courses
            function updateCategoryFilter(categoryCounts) {
                const categoryFilter = document.getElementById('categoryFilter');
                if (!categoryFilter) return;

                // Keep current selection
                const currentValue = categoryFilter.value;

                // Clear existing options except "All Categories"
                categoryFilter.innerHTML = '<option value="all">All Categories</option>';

                // Add options for each category found in courses
                Object.keys(categoryCounts).forEach(category => {
                    if (category && category !== 'undefined') {
                        const categoryInfo = getCategoryInfo(category);
                        const option = document.createElement('option');
                        option.value = category;
                        option.textContent = `${categoryInfo.icon} ${categoryInfo.label} (${categoryCounts[category]})`;
                        categoryFilter.appendChild(option);
                    }
                });

                // Restore previous selection if it still exists
                if (currentValue && [...categoryFilter.options].some(opt => opt.value === currentValue)) {
                    categoryFilter.value = currentValue;
                }

                console.log('🔄 Updated category filter with real categories:', Object.keys(categoryCounts));
            }

            // Filter courses
            function filterCourses() {
                const statusFilter = document.getElementById('statusFilter').value;
                const categoryFilter = document.getElementById('categoryFilter').value;

                console.log('🔍 Filtering courses:', { statusFilter, categoryFilter });
                console.log('📚 Total courses before filter:', allCourses.length);

                filteredCourses = allCourses.filter(course => {
                    const statusMatch = statusFilter === 'all' || course.status_course === statusFilter;
                    const categoryMatch = categoryFilter === 'all' || course.category_course === categoryFilter;

                    // Debug individual course filtering
                    if (!statusMatch) {
                        console.log(`❌ Status mismatch for "${course.name_course}": expected "${statusFilter}", got "${course.status_course}"`);
                    }
                    if (!categoryMatch) {
                        console.log(`❌ Category mismatch for "${course.name_course}": expected "${categoryFilter}", got "${course.category_course}"`);
                    }

                    return statusMatch && categoryMatch;
                });

                console.log('📊 Courses after filter:', filteredCourses.length);
                renderCourses();
                showNotification(`📊 Showing ${filteredCourses.length} courses`, 'info');
            }

            // Toggle view
            function toggleView(view) {
                currentView = view;

                // Update buttons
                document.getElementById('gridViewBtn').classList.toggle('active', view === 'grid');
                document.getElementById('listViewBtn').classList.toggle('active', view === 'list');

                // Update container
                const container = document.getElementById('coursesGrid');
                container.className = view === 'grid' ? 'courses-grid' : 'courses-list';

                renderCourses();
            }

            // Render courses
            function renderCourses() {
                const container = document.getElementById('coursesGrid');

                if (!filteredCourses || filteredCourses.length === 0) {
                    container.innerHTML = `
                        <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                            <i class="bi bi-collection" style="font-size: 4rem; color: #d1d5db;"></i>
                            <h3>No courses found</h3>
                            <p>Create your first course to get started</p>
                            <button class="btn btn-primary" onclick="window.location.href='<?php echo $site_url; ?>/new-course'">
                                <i class="bi bi-plus-circle"></i> Create Course
                            </button>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = filteredCourses.map(course => createCourseCard(course)).join('');
            }

            // Create course card
            function createCourseCard(course) {
                const category = getCategoryInfo(course.category_course);
                const status = getStatusInfo(course.status_course);
                const courseImage = course.cover_img || 'https://via.placeholder.com/320x180?text=No+Image';

                return `
                    <div class="course-card" onclick="editCourse('${course.code_course}')">
                        <div class="course-image" style="background-image: url('${courseImage}')">
                            <div class="course-status-badge ${status.badge}">
                                ${status.label}
                            </div>
                        </div>
                        <div class="course-content">
                            <div class="course-category">
                                ${category.icon} ${category.label}
                            </div>
                            <h3 class="course-title">${course.name_course}</h3>
                            <p class="course-description">${course.description_course || 'No description available'}</p>
                            <div class="course-meta">
                                <div class="course-stats">
                                    <span><i class="bi bi-layers"></i> ${course.stats ? course.stats.modules_count || 0 : 0} modules</span>
                                    <span><i class="bi bi-play-circle"></i> ${course.stats ? course.stats.lessons_count || 0 : 0} lessons</span>
                                    <span><i class="bi bi-clock"></i> ${course.duration_course || 0} min</span>
                                </div>
                                <div class="course-price">${formatCurrency(course.price_course)}</div>
                            </div>
                            <div class="course-actions">
                                <button class="btn-action primary" onclick="event.stopPropagation(); editCourse('${course.code_course}')">
                                    <i class="bi bi-pencil"></i> Edit
                                </button>
                                <button class="btn-action" onclick="event.stopPropagation(); viewCourse('${course.code_course}')">
                                    <i class="bi bi-eye"></i> View
                                </button>
                                ${course.status_course === 'draft' ?
                                    `<button class="btn-action success" onclick="event.stopPropagation(); restoreCourse('${course.id_course}', '${course.name_course}')">
                                        <i class="bi bi-arrow-clockwise"></i> Restore
                                    </button>` :
                                    `<button class="btn-action danger" onclick="event.stopPropagation(); deleteCourse('${course.id_course}', '${course.name_course}')">
                                        <i class="bi bi-trash"></i> Delete
                                    </button>`
                                }
                            </div>
                        </div>
                    </div>
                `;
            }

            // ========================================
            // COURSE ACTIONS
            // ========================================

            // Edit course
            function editCourse(courseCode) {
                window.location.href = `<?php echo $site_url; ?>/edit-course?#${courseCode}`;
            }

            // View course
            function viewCourse(courseCode) {
                window.location.href = `<?php echo $site_url; ?>/course-detail/?course=${courseCode}`;
            }

            // Delete course
            async function deleteCourse(courseId, courseName) {
                if (!confirm(`Are you sure you want to delete "${courseName}"?\n\nThis action cannot be undone.`)) {
                    return;
                }

                try {
                    showNotification('🗑️ Deleting course...', 'info');

                    const response = await ASG_API.deleteCourse(courseId);

                    if (response.success) {
                        showNotification(`✅ Course "${courseName}" deleted successfully!`, 'success');
                        // Remove from local arrays
                        allCourses = allCourses.filter(course => course.id_course != courseId);
                        filteredCourses = filteredCourses.filter(course => course.id_course != courseId);
                        renderCourses();
                    } else {
                        throw new Error(response.message || 'Delete failed');
                    }
                } catch (error) {
                    console.error('❌ Error deleting course:', error);
                    showNotification(`❌ Error deleting course: ${error.message}`, 'error');
                }
            }

            // Restore course (change from draft to published)
            async function restoreCourse(courseId, courseName) {
                if (!confirm(`Are you sure you want to restore "${courseName}" to published status?`)) {
                    return;
                }

                try {
                    console.log('🔄 Restoring course:', courseId);
                    showNotification('🔄 Restoring course...', 'info');

                    // Update course status to published and is_deleted to 0
                    const response = await ASG_API.updateCourse(courseId, {
                        status_course: 'published',
                        is_deleted: 0
                    });

                    console.log('✅ Restore response:', response);

                    if (response.success) {
                        showNotification(`✅ Course "${courseName}" restored successfully!`, 'success');

                        // Refresh the courses list
                        await refreshCourses();
                    } else {
                        throw new Error(response.message || 'Restore failed');
                    }
                } catch (error) {
                    console.error('❌ Error restoring course:', error);
                    showNotification(`❌ Error restoring course: ${error.message}`, 'error');
                }
            }

            // ========================================
            // INITIALIZATION
            // ========================================

            // Initialize page
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 All Courses Optimized v3.0.0 loaded');

                // Load courses after a short delay
                setTimeout(() => {
                    loadCourses();
                }, 500);
            });
        </script>
    </body>
    </html>
    <?php
}

/**
 * Registrar la página de todos los cursos optimizada
 */
function asg_register_all_courses_optimized_page_public() {
    // Crear página si no existe
    $page_slug = 'all-courses';
    $page = get_page_by_path($page_slug);

    if (!$page) {
        $page_data = array(
            'post_title'    => 'All Courses ASG',
            'post_content'  => '[asg_all_courses]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );

        wp_insert_post($page_data);
    }
}

/**
 * Shortcode para mostrar todos los cursos (OPTIMIZADO)
 */
function asg_all_courses_shortcode($atts) {
    ob_start();
    asg_create_all_courses_optimized_page();
    return ob_get_clean();
}

// Registrar hooks
add_action('init', 'asg_register_all_courses_optimized_page_public');
add_shortcode('asg_all_courses', 'asg_all_courses_shortcode');

// Registrar ruta personalizada
add_action('init', function() {
    add_rewrite_rule('^all-courses/?$', 'index.php?asg_page=all_courses', 'top');
});

add_filter('query_vars', function($vars) {
    $vars[] = 'asg_page';
    return $vars;
});

add_action('template_redirect', function() {
    $asg_page = get_query_var('asg_page');
    if ($asg_page === 'all_courses') {
        asg_create_all_courses_optimized_page();
        exit;
    }
});
