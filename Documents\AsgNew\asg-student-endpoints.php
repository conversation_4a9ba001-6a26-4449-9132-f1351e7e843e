/**
 * ========================================
 * ASG STUDENT FLOW ENDPOINTS - V1.0
 * ========================================
 * 
 * Sistema de gestión de estudiantes para AbilitySeminarsGroup
 * Endpoints para inscripción, acceso y progreso de estudiantes
 * 
 * Funcionalidades:
 * - Procesamiento de enrollments con PayPal
 * - Verificación de acceso a cursos
 * - Tracking de progreso de lecciones
 * - Dashboard del estudiante
 * 
 * Version: 1.0.0 - STUDENT FLOW SYSTEM
 * Author: ASG Development Team
 * Compatibility: WordPress REST API
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * STUDENT ENDPOINTS REGISTRATION
 * ========================================
 */
add_action('rest_api_init', function () {
    
    // ===== PROCESS ENROLLMENT ENDPOINT =====
    register_rest_route('asg/v1', '/process-enrollment', array(
        'methods' => 'POST',
        'callback' => 'asg_process_enrollment_handler',
        'permission_callback' => 'is_user_logged_in',
        'args' => array(
            'course_code' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field'
            ),
            'paypal_payment_id' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field'
            ),
            'amount_paid' => array(
                'required' => true,
                'type' => 'number'
            )
        )
    ));

    // ===== CHECK ENROLLMENT ENDPOINT =====
    register_rest_route('asg/v1', '/check-enrollment', array(
        'methods' => 'GET',
        'callback' => 'asg_check_enrollment_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'course' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field'
            )
        )
    ));

    // ===== COMPLETE LESSON ENDPOINT =====
    register_rest_route('asg/v1', '/lesson/(?P<lesson_id>\d+)/complete', array(
        'methods' => 'POST',
        'callback' => 'asg_lesson_complete_handler',
        'permission_callback' => '__return_true', // TEMPORAL: Permitir acceso sin restricciones para testing
        'args' => array(
            'lesson_id' => array(
                'required' => true,
                'type' => 'integer'
            )
        )
    ));

    // ===== COMPLETE LESSON ENDPOINT (SIN NONCE) =====
    register_rest_route('asg/v1', '/lesson/(?P<lesson_id>\d+)/complete-no-nonce', array(
        'methods' => 'POST',
        'callback' => 'asg_lesson_complete_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'lesson_id' => array(
                'required' => true,
                'type' => 'integer'
            )
        )
    ));

    // ===== DIAGNÓSTICO DEL SISTEMA =====
    register_rest_route('asg/v1', '/system-diagnostic', array(
        'methods' => 'GET',
        'callback' => 'asg_system_diagnostic_handler',
        'permission_callback' => '__return_true'
    ));

    // ===== MIGRAR PROGRESO DE USUARIO =====
    register_rest_route('asg/v1', '/migrate-user-progress', array(
        'methods' => 'POST',
        'callback' => 'asg_migrate_user_progress_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'user_id' => array(
                'required' => false,
                'type' => 'integer'
            ),
            'course_code' => array(
                'required' => false,
                'type' => 'string'
            )
        )
    ));

    // ===== INICIALIZACIÓN RÁPIDA =====
    register_rest_route('asg/v1', '/quick-init/(?P<course_code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'POST',
        'callback' => 'asg_quick_init_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'course_code' => array(
                'required' => true,
                'type' => 'string'
            )
        )
    ));

    // ===== PRUEBA DE PROGRESO =====
    register_rest_route('asg/v1', '/test-progress/(?P<course_code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_test_progress_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'course_code' => array(
                'required' => true,
                'type' => 'string'
            )
        )
    ));

    // ===== DEBUG FRONTEND =====
    register_rest_route('asg/v1', '/debug-frontend/(?P<course_code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_debug_frontend_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'course_code' => array(
                'required' => true,
                'type' => 'string'
            )
        )
    ));

    // ===== MIGRAR DATOS DE USUARIO ESPECÍFICO =====
    register_rest_route('asg/v1', '/migrate-user-progress', array(
        'methods' => 'POST',
        'callback' => 'asg_migrate_user_progress_handler',
        'permission_callback' => '__return_true',
        'args' => array(
            'user_id' => array(
                'required' => false,
                'type' => 'integer'
            ),
            'course_code' => array(
                'required' => false,
                'type' => 'string'
            )
        )
    ));

    // ===== STUDENT DASHBOARD ENDPOINT =====
    register_rest_route('asg/v1', '/my-courses', array(
        'methods' => 'GET',
        'callback' => 'asg_student_dashboard_handler',
        'permission_callback' => '__return_true' // TEMPORAL: Permitir acceso sin restricciones para testing
    ));

    // ===== INITIALIZE PROGRESS ENDPOINT =====
    register_rest_route('asg/v1', '/initialize-progress', array(
        'methods' => 'POST',
        'callback' => 'asg_initialize_progress_handler',
        'permission_callback' => 'is_user_logged_in',
        'args' => array(
            'course' => array(
                'required' => true,
                'type' => 'string',
                'sanitize_callback' => 'sanitize_text_field'
            )
        )
    ));
});

/**
 * ========================================
 * STUDENT MANAGER CLASS
 * ========================================
 */
class ASG_Student_Manager {
    
    /**
     * Obtener o crear estudiante con validación WordPress
     */
    public static function get_or_create_student($user_id) {
        global $wpdb;
        
        // Validación primaria: Usuario WordPress existe
        $wp_user = get_user_by('ID', $user_id);
        if (!$wp_user) {
            error_log("ASG: Usuario WordPress inválido: $user_id");
            return null;
        }
        
        // Buscar estudiante existente
        $student = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_asg_students WHERE id_user = %d",
            $user_id
        ), ARRAY_A);
        
        if (!$student) {
            // Crear estudiante (solo ID + datos básicos)
            $result = $wpdb->insert('wpic_asg_students', [
                'id_user' => $user_id,
                'username' => $wp_user->user_login,
                'email' => $wp_user->user_email
            ]);
            
            if (!$result) {
                if (defined('WP_DEBUG') && WP_DEBUG) {
                    error_log("ASG: Error creando estudiante: " . $wpdb->last_error);
                }
                return null;
            }
            
            $student = $wpdb->get_row($wpdb->prepare(
                "SELECT * FROM wpic_asg_students WHERE id_user = %d",
                $user_id
            ), ARRAY_A);
        }
        
        // Enriquecer con datos de WordPress (en tiempo real)
        if ($student) {
            $student['username'] = $wp_user->user_login;
            $student['email'] = $wp_user->user_email;
            $student['display_name'] = $wp_user->display_name;
            $student['wp_user_object'] = $wp_user;
        }
        
        return $student;
    }
    
    /**
     * Obtener student_id del usuario actual
     */
    public static function get_current_student_id() {
        $user_id = get_current_user_id();
        if (!$user_id) return null;
        
        $student = self::get_or_create_student($user_id);
        return $student ? $student['id_student'] : null;
    }
    
    /**
     * Verificar si un estudiante tiene un curso
     */
    public static function student_has_course($student_id, $course_id) {
        global $wpdb;
        
        $count = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM wpic_asg_enrollments WHERE student_id = %d AND course_id = %d",
            $student_id, $course_id
        ));
        
        return $count > 0;
    }
    
    /**
     * Verificar acceso a curso por código
     */
    public static function check_course_access($course_code) {
        global $wpdb;
        
        // Obtener usuario actual
        $user_id = get_current_user_id();
        if (!$user_id) {
            return ['access' => false, 'reason' => 'not_logged_in'];
        }
        
        // Validar usuario WordPress
        $wp_user = get_user_by('ID', $user_id);
        if (!$wp_user) {
            return ['access' => false, 'reason' => 'invalid_user'];
        }
        
        // Verificar enrollment
        $enrollment = $wpdb->get_row($wpdb->prepare("
            SELECT 
                e.*,
                c.name_course,
                s.id_user as student_user_id
            FROM wpic_asg_enrollments e
            JOIN wpic_asg_students s ON e.student_id = s.id_student
            JOIN wpic_courses c ON e.course_id = c.id_course
            WHERE s.id_user = %d AND c.code_course = %s
        ", $user_id, $course_code));
        
        if (!$enrollment) {
            return ['access' => false, 'reason' => 'not_enrolled'];
        }
        
        // Verificación de integridad
        if ($enrollment->student_user_id != $user_id) {
            error_log("ASG: Inconsistencia de datos - User: $user_id, Student: {$enrollment->student_user_id}");
            return ['access' => false, 'reason' => 'data_inconsistency'];
        }
        
        return [
            'access' => true,
            'enrollment' => $enrollment,
            'user' => $wp_user
        ];
    }
    
    /**
     * Obtener estadísticas del estudiante
     */
    public static function get_student_stats($student_id) {
        global $wpdb;
        
        return $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(DISTINCT e.course_id) as total_courses,
                SUM(e.amount_paid) as total_spent,
                COUNT(DISTINCT CASE WHEN sp.completed = 1 THEN sp.lesson_id END) as total_lessons_completed,
                COUNT(DISTINCT sp.lesson_id) as total_lessons_enrolled
            FROM wpic_asg_enrollments e
            LEFT JOIN wpic_asg_progress sp ON sp.student_id = e.student_id
            WHERE e.student_id = %d
        ", $student_id), ARRAY_A);
    }
    
    /**
     * Obtener cursos del estudiante
     */
    public static function get_student_courses($student_id) {
        global $wpdb;

        // Obtener cursos básicos con estadísticas
        $courses = $wpdb->get_results($wpdb->prepare("
            SELECT
                c.*,
                e.amount_paid,
                e.payment_date,
                e.paypal_payment_id,
                COUNT(sp.id_progress) as total_lessons,
                COUNT(CASE WHEN sp.completed = 1 THEN 1 END) as completed_lessons,
                ROUND((COUNT(CASE WHEN sp.completed = 1 THEN 1 END) / COUNT(sp.id_progress)) * 100, 1) as progress_percentage
            FROM wpic_asg_enrollments e
            JOIN wpic_courses c ON e.course_id = c.id_course
            LEFT JOIN wpic_asg_progress sp ON sp.student_id = e.student_id
            LEFT JOIN wpic_lessons l ON sp.lesson_id = l.id_lesson
            LEFT JOIN wpic_modules m ON l.code_module = m.code_module
            LEFT JOIN wpic_courses c2 ON m.code_course = c2.code_course AND c2.id_course = c.id_course
            WHERE e.student_id = %d
            GROUP BY c.id_course, e.id_enrollment
            ORDER BY e.payment_date DESC
        ", $student_id), ARRAY_A);

        // Para cada curso, agregar la lista de lecciones completadas y desbloqueadas
        foreach ($courses as &$course) {
            $completed_lessons = $wpdb->get_col($wpdb->prepare("
                SELECT sp.lesson_id
                FROM wpic_asg_progress sp
                LEFT JOIN wpic_lessons l ON sp.lesson_id = l.id_lesson
                LEFT JOIN wpic_modules m ON l.code_module = m.code_module
                WHERE sp.student_id = %d
                AND sp.completed = 1
                AND m.code_course = %s
                ORDER BY l.id_lesson ASC
            ", $student_id, $course['code_course']));

            $course['completed_lessons_list'] = array_map('intval', $completed_lessons);

            // Obtener todas las lecciones del curso ordenadas
            $all_lessons = $wpdb->get_results($wpdb->prepare("
                SELECT l.id_lesson, l.is_preview
                FROM wpic_lessons l
                LEFT JOIN wpic_modules m ON l.code_module = m.code_module
                WHERE m.code_course = %s
                ORDER BY l.id_lesson ASC
            ", $course['code_course']), ARRAY_A);

            // Calcular lecciones desbloqueadas (sistema secuencial)
            $unlocked_lessons = [];
            $preview_lessons = [];

            foreach ($all_lessons as $lesson) {
                $lesson_id = intval($lesson['id_lesson']);
                $is_preview = intval($lesson['is_preview']);

                if ($is_preview) {
                    // Las lecciones preview siempre están desbloqueadas
                    $preview_lessons[] = $lesson_id;
                    $unlocked_lessons[] = $lesson_id;
                } else {
                    // Para lecciones no-preview, verificar secuencia
                    if (empty($unlocked_lessons)) {
                        // Primera lección no-preview siempre desbloqueada
                        $unlocked_lessons[] = $lesson_id;
                    } else {
                        // Verificar si la lección anterior está completada
                        $previous_lessons = array_slice($all_lessons, 0, array_search($lesson, $all_lessons));
                        $all_previous_completed = true;

                        foreach ($previous_lessons as $prev_lesson) {
                            $prev_id = intval($prev_lesson['id_lesson']);
                            $prev_is_preview = intval($prev_lesson['is_preview']);

                            // Solo verificar lecciones no-preview
                            if (!$prev_is_preview && !in_array($prev_id, $completed_lessons)) {
                                $all_previous_completed = false;
                                break;
                            }
                        }

                        if ($all_previous_completed) {
                            $unlocked_lessons[] = $lesson_id;
                        }
                    }
                }
            }

            $course['unlocked_lessons_list'] = $unlocked_lessons;
            $course['preview_lessons_list'] = $preview_lessons;
            $course['all_lessons_list'] = array_map(function($l) { return intval($l['id_lesson']); }, $all_lessons);
        }

        return $courses;
    }
}

/**
 * ========================================
 * ENDPOINT HANDLERS
 * ========================================
 */

/**
 * 1. PROCESAR ENROLLMENT - Maneja la compra de cursos
 */
function asg_process_enrollment_handler($request) {
    global $wpdb;

    $user_id = get_current_user_id();
    $course_code = sanitize_text_field($request['course_code']);
    $paypal_payment_id = sanitize_text_field($request['paypal_payment_id']);
    $amount_paid = floatval($request['amount_paid']);

    // Validaciones previas
    if (!$user_id) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Usuario no autenticado'
        ], 401);
    }

    $wp_user = get_user_by('ID', $user_id);
    if (!$wp_user) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Usuario inválido'
        ], 401);
    }

    $course = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM wpic_courses WHERE code_course = %s AND status_course = 'published'",
        $course_code
    ));

    if (!$course) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Curso no encontrado'
        ], 404);
    }

    // Transacción segura
    $wpdb->query('START TRANSACTION');

    try {
        // 1. Crear/obtener estudiante
        $student = ASG_Student_Manager::get_or_create_student($user_id);
        if (!$student) {
            throw new Exception('Error creando estudiante');
        }

        // 2. Verificar enrollment duplicado
        $existing = $wpdb->get_row($wpdb->prepare(
            "SELECT * FROM wpic_asg_enrollments WHERE student_id = %d AND course_id = %d",
            $student['id_student'], $course->id_course
        ));

        if ($existing) {
            throw new Exception('Ya estás inscrito en este curso');
        }

        // 3. Crear enrollment
        $enrollment_result = $wpdb->insert('wpic_asg_enrollments', [
            'student_id' => $student['id_student'],
            'course_id' => $course->id_course,
            'paypal_payment_id' => $paypal_payment_id,
            'amount_paid' => $amount_paid,
            'payment_date' => current_time('mysql')
        ]);

        if (!$enrollment_result) {
            throw new Exception('Error creando enrollment');
        }

        // 4. Inicializar progreso (OPTIMIZADO - nueva estructura)
        $total_lessons = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(l.id_lesson)
             FROM wpic_lessons l
             JOIN wpic_modules m ON l.code_module = m.code_module
             WHERE m.code_course = %s AND l.is_deleted = 0",
            $course_code
        ));

        // Crear registro optimizado en wpic_asg_user_progress
        $progress_result = $wpdb->insert('wpic_asg_user_progress', [
            'user_id' => $student['id_student'],
            'course_code' => $course_code,
            'completed_lessons' => json_encode([]), // Array vacío inicialmente
            'total_lessons' => $total_lessons,
            'completed_count' => 0,
            'progress_percentage' => 0.00,
            'last_lesson_id' => null,
            'last_completed_at' => null,
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ]);

        if (!$progress_result) {
            throw new Exception('Error inicializando progreso optimizado');
        }

        $wpdb->query('COMMIT');

        // Log de éxito
        if (defined('WP_DEBUG') && WP_DEBUG) {
            error_log("ASG: Enrollment exitoso - Usuario: {$wp_user->user_login} ({$user_id}), Curso: {$course_code}, Pago: {$paypal_payment_id}");
        }

        return new WP_REST_Response([
            'success' => true,
            'message' => 'Enrollment procesado exitosamente',
            'data' => [
                'course_name' => $course->name_course,
                'student_name' => $wp_user->display_name,
                'total_lessons' => $total_lessons,
                'progress_initialized' => true,
                'redirect_url' => "/lessons/?course={$course_code}"
            ]
        ]);

    } catch (Exception $e) {
        $wpdb->query('ROLLBACK');
        error_log('ASG Enrollment Error: ' . $e->getMessage());

        return new WP_REST_Response([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * 2. VERIFICAR ENROLLMENT - Comprueba si el usuario tiene acceso al curso
 */
function asg_check_enrollment_handler($request) {
    $course_code = sanitize_text_field($request['course']);

    // Inicializar usuario automáticamente
    asg_init_current_user();

    // Verificar acceso usando helper
    $access_result = ASG_Student_Manager::check_course_access($course_code);

    $response_data = [
        'success' => true,
        'enrolled' => $access_result['access'],
        'reason' => $access_result['reason'] ?? null
    ];

    // Agregar información del usuario si está logueado
    if ($access_result['access'] && isset($access_result['user'])) {
        $response_data['user_info'] = [
            'user_id' => $access_result['user']->ID,
            'username' => $access_result['user']->user_login,
            'display_name' => $access_result['user']->display_name
        ];
    }

    return new WP_REST_Response($response_data);
}

/**
 * 3. COMPLETAR LECCIÓN - Marca una lección como completada
 */
function asg_lesson_complete_handler($request) {
    error_log("ASG: asg_lesson_complete_handler INICIADO (OPTIMIZADO)");

    $lesson_id = intval($request['lesson_id']);
    error_log("ASG: lesson_id recibido: " . $lesson_id);

    // Verificar si la nueva tabla existe
    global $wpdb;
    $table_exists = $wpdb->get_var("SHOW TABLES LIKE 'wpic_asg_user_progress'");
    if (!$table_exists) {
        error_log("ASG: ERROR - Tabla wpic_asg_user_progress no existe. Necesita ejecutar migración.");
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Sistema en migración. La tabla wpic_asg_user_progress no existe. Contacta al administrador.',
            'migration_needed' => true
        ], 500);
    }
    error_log("ASG: Tabla wpic_asg_user_progress verificada - existe");

    // Inicializar usuario automáticamente
    asg_init_current_user();
    error_log("ASG: Usuario inicializado, current_user_id: " . get_current_user_id());

    // Obtener student_id del usuario actual
    $student_id = ASG_Student_Manager::get_current_student_id();

    // Para testing: si no hay student_id, usar uno de prueba
    if (!$student_id) {
        global $wpdb;
        $student_id = $wpdb->get_var("SELECT id_student FROM wpic_asg_students LIMIT 1");
        error_log("ASG Testing: Usando student_id de prueba para completar lección: " . $student_id);

        if (!$student_id) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No hay estudiantes en el sistema para testing'
            ], 404);
        }
    }

    // Verificar que la lección existe y obtener course_code
    global $wpdb;
    $lesson = $wpdb->get_row($wpdb->prepare(
        "SELECT l.*, m.code_course
         FROM wpic_lessons l
         JOIN wpic_modules m ON l.code_module = m.code_module
         WHERE l.id_lesson = %d AND l.is_deleted = 0",
        $lesson_id
    ));

    if (!$lesson) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Lección no encontrada'
        ], 404);
    }

    $course_code = $lesson->code_course;
    error_log("ASG: Lección encontrada: " . $lesson->title_lesson . " - Curso: " . $course_code);

    // Verificar que el estudiante tiene progreso inicializado para este curso
    $user_progress = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_user_progress
        WHERE user_id = %d AND course_code = %s
    ", $student_id, $course_code));

    if (!$user_progress) {
        error_log("ASG: Progreso no inicializado para curso: " . $course_code . " - Intentando auto-inicializar");

        // AUTO-INICIALIZAR PROGRESO
        $total_lessons = $wpdb->get_var($wpdb->prepare("
            SELECT COUNT(l.id_lesson)
            FROM wpic_lessons l
            JOIN wpic_modules m ON l.code_module = m.code_module
            WHERE m.code_course = %s AND l.is_deleted = 0
        ", $course_code));

        if ($total_lessons) {
            $init_result = $wpdb->insert('wpic_asg_user_progress', [
                'user_id' => $student_id,
                'course_code' => $course_code,
                'completed_lessons' => json_encode([]),
                'total_lessons' => $total_lessons,
                'completed_count' => 0,
                'progress_percentage' => 0.00,
                'last_lesson_id' => null,
                'last_completed_at' => null,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ]);

            if ($init_result) {
                error_log("ASG: Progreso auto-inicializado exitosamente");
                // Recargar el progreso recién creado
                $user_progress = $wpdb->get_row($wpdb->prepare("
                    SELECT * FROM wpic_asg_user_progress
                    WHERE user_id = %d AND course_code = %s
                ", $student_id, $course_code));
            }
        }

        if (!$user_progress) {
            error_log("ASG: Error - No se pudo auto-inicializar progreso para curso: " . $course_code);
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No se pudo inicializar progreso para este curso. Usa el endpoint /migrate-user-progress primero.',
                'auto_init_failed' => true,
                'course_code' => $course_code,
                'student_id' => $student_id
            ], 403);
        }
    }

    // Obtener lecciones completadas actuales
    $completed_lessons = json_decode($user_progress->completed_lessons, true) ?: [];

    // Verificar si la lección ya está completada
    if (in_array($lesson_id, $completed_lessons)) {
        error_log("ASG: Lección ya completada: " . $lesson_id);
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Lección ya estaba completada',
            'already_completed' => true,
            'data' => [
                'lesson_id' => $lesson_id,
                'lesson_title' => $lesson->title_lesson,
                // Formato compatible con frontend existente
                'course_progress' => [
                    'completed_lessons' => intval($user_progress->completed_count),
                    'total_lessons' => intval($user_progress->total_lessons),
                    'progress_percentage' => floatval($user_progress->progress_percentage)
                ],
                // Datos adicionales optimizados
                'progress' => [
                    'completed_count' => intval($user_progress->completed_count),
                    'total_lessons' => intval($user_progress->total_lessons),
                    'progress_percentage' => floatval($user_progress->progress_percentage),
                    'completed_lessons_list' => json_decode($user_progress->completed_lessons, true) ?: [],
                    'last_lesson_id' => $user_progress->last_lesson_id
                ]
            ]
        ]);
    }

    // Agregar nueva lección completada
    $completed_lessons[] = $lesson_id;
    sort($completed_lessons); // Mantener ordenado

    // Calcular nuevos valores
    $new_completed_count = count($completed_lessons);
    $new_progress_percentage = ($new_completed_count / $user_progress->total_lessons) * 100;

    // Actualizar registro en wpic_asg_user_progress (SÚPER OPTIMIZADO)
    $result = $wpdb->update('wpic_asg_user_progress', [
        'completed_lessons' => json_encode($completed_lessons),
        'completed_count' => $new_completed_count,
        'progress_percentage' => round($new_progress_percentage, 2),
        'last_lesson_id' => $lesson_id,
        'last_completed_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ], [
        'user_id' => $student_id,
        'course_code' => $course_code
    ]);

    if ($result === false) {
        error_log("ASG: Error al actualizar progreso: " . $wpdb->last_error);
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Error actualizando progreso: ' . $wpdb->last_error
        ], 500);
    }

    error_log("ASG: Progreso actualizado exitosamente - Nueva lección completada: " . $lesson_id);
    error_log("ASG: Nuevo progreso - Completadas: {$new_completed_count}/{$user_progress->total_lessons} ({$new_progress_percentage}%)");

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Lección completada exitosamente',
        'data' => [
            'lesson_id' => $lesson_id,
            'lesson_title' => $lesson->title_lesson,
            // Formato compatible con frontend existente
            'course_progress' => [
                'completed_lessons' => $new_completed_count,
                'total_lessons' => intval($user_progress->total_lessons),
                'progress_percentage' => round($new_progress_percentage, 2)
            ],
            // Datos adicionales optimizados
            'progress' => [
                'completed_count' => $new_completed_count,
                'total_lessons' => intval($user_progress->total_lessons),
                'progress_percentage' => round($new_progress_percentage, 2),
                'completed_lessons_list' => $completed_lessons,
                'last_lesson_id' => $lesson_id
            ]
        ]
    ]);
}

/**
 * 4. INICIALIZAR PROGRESO - Crea registro optimizado para el curso (OPTIMIZADO)
 */
function asg_initialize_progress_handler($request) {
    $course_code = sanitize_text_field($request['course']);

    // Obtener student_id del usuario actual
    $student_id = ASG_Student_Manager::get_current_student_id();
    if (!$student_id) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Estudiante no encontrado'
        ], 404);
    }

    global $wpdb;

    // Verificar que el estudiante está inscrito en el curso
    $enrollment = $wpdb->get_row($wpdb->prepare("
        SELECT e.id_enrollment
        FROM wpic_asg_enrollments e
        JOIN wpic_courses c ON e.course_id = c.id_course
        WHERE e.student_id = %d AND c.code_course = %s
    ", $student_id, $course_code));

    if (!$enrollment) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No estás inscrito en este curso'
        ], 403);
    }

    // Verificar si ya existe progreso para este usuario y curso
    $existing_progress = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_user_progress
        WHERE user_id = %d AND course_code = %s
    ", $student_id, $course_code));

    if ($existing_progress) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Progreso ya inicializado para este curso',
            'already_exists' => true,
            'data' => [
                'course_code' => $course_code,
                'total_lessons' => intval($existing_progress->total_lessons),
                'completed_count' => intval($existing_progress->completed_count),
                'progress_percentage' => floatval($existing_progress->progress_percentage)
            ]
        ]);
    }

    // Obtener total de lecciones del curso
    $total_lessons = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(l.id_lesson)
        FROM wpic_lessons l
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE m.code_course = %s AND l.is_deleted = 0
    ", $course_code));

    if (!$total_lessons) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se encontraron lecciones para este curso'
        ], 404);
    }

    // Crear registro inicial optimizado en wpic_asg_user_progress
    $result = $wpdb->insert('wpic_asg_user_progress', [
        'user_id' => $student_id,
        'course_code' => $course_code,
        'completed_lessons' => json_encode([]), // Array vacío inicialmente
        'total_lessons' => $total_lessons,
        'completed_count' => 0,
        'progress_percentage' => 0.00,
        'last_lesson_id' => null,
        'last_completed_at' => null,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ]);

    if ($result === false) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Error al inicializar progreso: ' . $wpdb->last_error
        ], 500);
    }

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Progreso inicializado exitosamente',
        'data' => [
            'course_code' => $course_code,
            'total_lessons' => $total_lessons,
            'completed_count' => 0,
            'progress_percentage' => 0.00,
            'user_id' => $student_id
        ]
    ]);
}

/**
 * 5. DASHBOARD DEL ESTUDIANTE - Obtiene todos los cursos y estadísticas (OPTIMIZADO)
 */
function asg_student_dashboard_handler($request) {
    // Inicializar usuario automáticamente
    asg_init_current_user();

    // Obtener student_id
    $student_id = ASG_Student_Manager::get_current_student_id();

    // Para testing: si no hay student_id, usar uno de prueba
    if (!$student_id) {
        global $wpdb;
        // Buscar cualquier estudiante existente para testing
        $student_id = $wpdb->get_var("SELECT id_student FROM wpic_asg_students LIMIT 1");
        error_log("ASG Testing: Usando student_id de prueba: " . $student_id);

        if (!$student_id) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No hay estudiantes en el sistema para testing'
            ], 404);
        }
    }

    global $wpdb;

    // Obtener cursos del estudiante (SÚPER OPTIMIZADO - consulta directa)
    $user_progress = $wpdb->get_results($wpdb->prepare("
        SELECT
            up.*,
            c.name_course,
            c.cover_img,
            c.description_course
        FROM wpic_asg_user_progress up
        LEFT JOIN wpic_courses c ON up.course_code = c.code_course
        WHERE up.user_id = %d
        ORDER BY up.progress_percentage DESC, up.updated_at DESC
    ", $student_id));

    // Estadísticas generales del usuario (SÚPER OPTIMIZADO)
    $user_stats = $wpdb->get_row($wpdb->prepare("
        SELECT
            COUNT(*) as total_courses,
            SUM(completed_count) as total_lessons_completed,
            SUM(total_lessons) as total_lessons_enrolled,
            AVG(progress_percentage) as average_progress,
            COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as courses_completed,
            MAX(last_completed_at) as last_activity
        FROM wpic_asg_user_progress
        WHERE user_id = %d
    ", $student_id));

    // Formatear datos de cursos
    $courses = [];
    error_log("ASG Dashboard: Formateando " . count($user_progress) . " cursos para respuesta");

    foreach ($user_progress as $course) {
        $completed_lessons = json_decode($course->completed_lessons, true) ?: [];
        error_log("ASG Dashboard: Curso {$course->course_code} - {$course->completed_count}/{$course->total_lessons} ({$course->progress_percentage}%)");

        // Obtener todas las lecciones del curso para unlocked_lessons_list
        $all_lessons = $wpdb->get_results($wpdb->prepare("
            SELECT l.id_lesson
            FROM wpic_lessons l
            JOIN wpic_modules m ON l.code_module = m.code_module
            WHERE m.code_course = %s AND l.is_deleted = 0
            ORDER BY l.id_lesson ASC
        ", $course->course_code));

        $all_lessons_list = array_map(function($lesson) {
            return intval($lesson->id_lesson);
        }, $all_lessons);

        // Calculate unlocked lessons (Sequential Learning)
        $unlocked_lessons_list = [];
        if (!empty($all_lessons_list)) {
            // First lesson is always unlocked
            $unlocked_lessons_list[] = $all_lessons_list[0];

            // Unlock lessons sequentially based on completed ones
            for ($i = 1; $i < count($all_lessons_list); $i++) {
                $previous_lesson_id = $all_lessons_list[$i - 1];
                if (in_array($previous_lesson_id, $completed_lessons)) {
                    $unlocked_lessons_list[] = $all_lessons_list[$i];
                } else {
                    break; // Stop at first locked lesson
                }
            }
        }

        error_log("ASG Sequential: Course {$course->course_code} - Unlocked: " . count($unlocked_lessons_list) . "/" . count($all_lessons_list));

        $courses[] = [
            'course_code' => $course->course_code,
            'code_course' => $course->course_code, // Frontend alias
            'course_name' => $course->name_course,
            'name_course' => $course->name_course, // Frontend alias
            'course_image' => $course->cover_img,
            'cover_img' => $course->cover_img, // Frontend alias
            'course_description' => $course->description_course,
            'total_lessons' => intval($course->total_lessons),
            'completed_lessons' => intval($course->completed_count), // Frontend expects this name
            'completed_count' => intval($course->completed_count), // Compatibility
            'progress_percentage' => floatval($course->progress_percentage),
            'completed_lessons_list' => $completed_lessons, // Completed lesson IDs
            'unlocked_lessons_list' => $unlocked_lessons_list, // Sequential unlocking
            'preview_lessons_list' => [], // No preview lessons
            'all_lessons_list' => $all_lessons_list, // All course lessons
            'last_lesson_id' => $course->last_lesson_id,
            'last_completed_at' => $course->last_completed_at,
            'created_at' => $course->created_at,
            'updated_at' => $course->updated_at
        ];
    }

    // Formatear estadísticas
    $stats = [
        'total_courses' => intval($user_stats->total_courses ?? 0),
        'total_lessons_completed' => intval($user_stats->total_lessons_completed ?? 0),
        'total_lessons_enrolled' => intval($user_stats->total_lessons_enrolled ?? 0),
        'average_progress' => round(floatval($user_stats->average_progress ?? 0), 2),
        'courses_completed' => intval($user_stats->courses_completed ?? 0),
        'last_activity' => $user_stats->last_activity ?? null
    ];

    // Obtener información del usuario actual
    $user_id = get_current_user_id();
    $wp_user = get_user_by('ID', $user_id);

    // Para testing: si no hay usuario logueado, usar datos de prueba
    $user_info = [
        'display_name' => 'Usuario de Prueba',
        'email' => '<EMAIL>',
        'username' => 'test_user'
    ];

    if ($wp_user) {
        $user_info = [
            'display_name' => $wp_user->display_name,
            'email' => $wp_user->user_email,
            'username' => $wp_user->user_login
        ];
    }

    return new WP_REST_Response([
        'success' => true,
        'data' => [
            'user_info' => $user_info,
            'courses' => $courses,
            'stats' => $stats
        ]
    ]);
}

/**
 * ========================================
 * HELPER FUNCTIONS
 * ========================================
 */

/**
 * Inicializar usuario automáticamente para REST API
 * Soluciona el problema donde WordPress no inicializa la sesión automáticamente
 */
function asg_init_current_user() {
    // Si ya hay un usuario establecido, no hacer nada
    if (get_current_user_id() > 0) {
        return get_current_user_id();
    }

    // Intentar validar cookie de autenticación
    $user_id = wp_validate_auth_cookie();
    if (!$user_id) {
        $user_id = wp_validate_auth_cookie('', 'logged_in');
    }

    // Si encontramos un usuario válido, establecerlo
    if ($user_id) {
        wp_set_current_user($user_id);
        error_log("ASG: Usuario inicializado automáticamente: " . $user_id);
        return $user_id;
    }

    return 0;
}

/**
 * Permission callback personalizado que auto-inicializa el usuario
 */
function asg_permission_check_with_init() {
    // Primero intentar auto-inicializar
    $user_id = asg_init_current_user();

    // Verificar si ahora el usuario está logueado
    $is_logged_in = is_user_logged_in();

    error_log("ASG Permission Check: user_id=" . $user_id . ", is_logged_in=" . ($is_logged_in ? 'true' : 'false'));

    return $is_logged_in;
}

/**
 * Limpiar estudiantes huérfanos (sin usuario WordPress)
 */
function asg_cleanup_orphaned_students() {
    global $wpdb;

    $orphaned = $wpdb->get_results("
        SELECT s.id_student, s.id_user
        FROM wpic_asg_students s
        LEFT JOIN wpic_users u ON s.id_user = u.ID
        WHERE u.ID IS NULL
    ");

    foreach ($orphaned as $student) {
        error_log("ASG: Eliminando estudiante huérfano: {$student->id_student}");
        $wpdb->delete('wpic_asg_students', ['id_student' => $student->id_student]);
    }

    return count($orphaned);
}

// Ejecutar limpieza periódicamente
add_action('wp_scheduled_delete', 'asg_cleanup_orphaned_students');

/**
 * Hook para manejar post-registro automático
 */
add_action('user_register', 'asg_handle_post_registration_enrollment');

function asg_handle_post_registration_enrollment($user_id) {
    // Verificar si hay enrollment pendiente en URL o sesión
    if (isset($_GET['redirect_course']) && isset($_GET['price'])) {
        $course_code = sanitize_text_field($_GET['redirect_course']);
        $price = floatval($_GET['price']);

        // Redirect automático a pago
        wp_redirect("/payment/?course={$course_code}&price={$price}&new_user=1");
        exit;
    }
}

/**
 * ENDPOINT DE TESTING - Para verificar que los endpoints funcionan sin autenticación
 */
function asg_test_endpoint_handler($request) {
    global $wpdb;

    // Estado ANTES de inicialización
    $before_init = [
        'user_logged_in' => is_user_logged_in(),
        'current_user_id' => get_current_user_id()
    ];

    // Inicializar usuario automáticamente
    $initialized_user_id = asg_init_current_user();

    // Estado DESPUÉS de inicialización
    $after_init = [
        'user_logged_in' => is_user_logged_in(),
        'current_user_id' => get_current_user_id()
    ];

    // Información detallada de autenticación
    $current_user = wp_get_current_user();
    $cookies_info = [];

    // Verificar cookies de WordPress
    foreach ($_COOKIE as $name => $value) {
        if (strpos($name, 'wordpress') !== false || strpos($name, 'wp-') !== false) {
            $cookies_info[$name] = substr($value, 0, 50) . '...'; // Solo primeros 50 caracteres por seguridad
        }
    }

    // Información básica del sistema
    $info = [
        'timestamp' => current_time('mysql'),
        'initialization_status' => [
            'before_init' => $before_init,
            'after_init' => $after_init,
            'initialized_user_id' => $initialized_user_id,
            'auto_init_worked' => $initialized_user_id > 0
        ],
        'current_user_info' => [
            'user_logged_in' => is_user_logged_in(),
            'current_user_id' => get_current_user_id(),
            'current_user_login' => $current_user->user_login ?? 'No user',
            'current_user_email' => $current_user->user_email ?? 'No email',
            'current_user_roles' => $current_user->roles ?? []
        ],
        'system_info' => [
            'wp_cookies_present' => !empty($cookies_info),
            'cookies_count' => count($cookies_info),
            'session_tokens' => get_user_meta(get_current_user_id(), 'session_tokens', true) ? 'Present' : 'None',
            'total_students' => $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_students"),
            'total_courses' => $wpdb->get_var("SELECT COUNT(*) FROM wpic_courses WHERE status_course = 'published'"),
            'total_enrollments' => $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_enrollments")
        ]
    ];

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Endpoint de testing funcionando correctamente',
        'data' => $info
    ]);
}

/**
 * ENDPOINT DE DIAGNÓSTICO DE AUTENTICACIÓN
 */
function asg_auth_diagnostic_handler($request) {
    // Forzar verificación de autenticación
    wp_set_current_user(0); // Reset
    wp_cookie_constants(); // Asegurar que las constantes estén definidas

    // Verificar autenticación manualmente
    $user_id = wp_validate_auth_cookie();
    if (!$user_id) {
        $user_id = wp_validate_auth_cookie('', 'logged_in');
    }

    $diagnostic = [
        'wp_validate_auth_cookie' => $user_id ? $user_id : 'Failed',
        'is_user_logged_in_before' => is_user_logged_in(),
        'get_current_user_id_before' => get_current_user_id(),
    ];

    // Si encontramos un usuario válido, establecerlo
    if ($user_id) {
        wp_set_current_user($user_id);
    }

    $diagnostic['is_user_logged_in_after'] = is_user_logged_in();
    $diagnostic['get_current_user_id_after'] = get_current_user_id();
    $diagnostic['wp_get_current_user'] = wp_get_current_user()->user_login ?? 'No user';

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Diagnóstico de autenticación',
        'data' => $diagnostic
    ]);
}

/**
 * ENDPOINT DE TESTING PARA COMPLETAR LECCIONES
 */
function asg_test_complete_lesson_handler($request) {
    $lesson_id = $request->get_param('lesson_id') ?: 44;

    return new WP_REST_Response([
        'success' => true,
        'message' => 'Test endpoint para completar lecciones funcionando',
        'data' => [
            'lesson_id' => $lesson_id,
            'timestamp' => current_time('mysql'),
            'user_logged_in' => is_user_logged_in(),
            'current_user_id' => get_current_user_id(),
            'endpoint_status' => 'accessible'
        ]
    ]);
}

/**
 * DIAGNÓSTICO DEL SISTEMA - Verificar estado de migración
 */
function asg_system_diagnostic_handler($request) {
    global $wpdb;

    $diagnostic = [
        'timestamp' => current_time('mysql'),
        'tables' => [],
        'migration_status' => [],
        'sample_data' => [],
        'recommendations' => []
    ];

    // Verificar tablas existentes
    $tables_to_check = [
        'wpic_asg_progress' => 'Tabla original de progreso',
        'wpic_asg_user_progress' => 'Nueva tabla optimizada',
        'wpic_asg_students' => 'Tabla de estudiantes',
        'wpic_courses' => 'Tabla de cursos',
        'wpic_lessons' => 'Tabla de lecciones'
    ];

    foreach ($tables_to_check as $table => $description) {
        $exists = $wpdb->get_var("SHOW TABLES LIKE '{$table}'");
        $count = 0;
        if ($exists) {
            $count = $wpdb->get_var("SELECT COUNT(*) FROM {$table}");
        }

        $diagnostic['tables'][$table] = [
            'exists' => (bool)$exists,
            'description' => $description,
            'record_count' => $count
        ];
    }

    // Estado de migración
    $old_table_exists = $diagnostic['tables']['wpic_asg_progress']['exists'];
    $new_table_exists = $diagnostic['tables']['wpic_asg_user_progress']['exists'];

    if (!$old_table_exists && !$new_table_exists) {
        $diagnostic['migration_status']['status'] = 'no_tables';
        $diagnostic['migration_status']['message'] = 'No existen tablas de progreso';
        $diagnostic['recommendations'][] = 'Crear estructura de base de datos';
    } elseif ($old_table_exists && !$new_table_exists) {
        $diagnostic['migration_status']['status'] = 'migration_needed';
        $diagnostic['migration_status']['message'] = 'Migración pendiente - solo existe tabla antigua';
        $diagnostic['recommendations'][] = 'Ejecutar migrate-to-new-structure.sql';
    } elseif (!$old_table_exists && $new_table_exists) {
        $diagnostic['migration_status']['status'] = 'migrated';
        $diagnostic['migration_status']['message'] = 'Migración completada - solo existe tabla nueva';
        $diagnostic['recommendations'][] = 'Sistema optimizado y funcionando';
    } else {
        $diagnostic['migration_status']['status'] = 'both_exist';
        $diagnostic['migration_status']['message'] = 'Ambas tablas existen - migración en progreso';
        $diagnostic['recommendations'][] = 'Verificar datos y eliminar tabla antigua cuando confirmes que todo funciona';
    }

    // Datos de muestra
    if ($new_table_exists) {
        $sample_progress = $wpdb->get_results("
            SELECT user_id, course_code, total_lessons, completed_count, progress_percentage
            FROM wpic_asg_user_progress
            LIMIT 3
        ");
        $diagnostic['sample_data']['new_structure'] = $sample_progress;
    }

    if ($old_table_exists) {
        $old_count = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress");
        $diagnostic['sample_data']['old_structure_count'] = $old_count;
    }

    // Verificar usuario actual
    $current_user = get_current_user_id();
    $student_id = null;
    if ($current_user) {
        $student_id = $wpdb->get_var($wpdb->prepare("
            SELECT id_student FROM wpic_asg_students WHERE user_id = %d
        ", $current_user));
    }

    $diagnostic['current_user'] = [
        'wp_user_id' => $current_user,
        'student_id' => $student_id,
        'has_student_record' => (bool)$student_id
    ];

    return new WP_REST_Response([
        'success' => true,
        'diagnostic' => $diagnostic
    ]);
}

/**
 * MIGRAR PROGRESO DE USUARIO ESPECÍFICO - Solución rápida para error 403
 */
function asg_migrate_user_progress_handler($request) {
    global $wpdb;

    // Obtener user_id (del parámetro o del usuario actual)
    $user_id = $request['user_id'] ?? null;
    if (!$user_id) {
        // Intentar obtener del usuario actual
        $current_user = get_current_user_id();
        if ($current_user) {
            $user_id = $wpdb->get_var($wpdb->prepare("
                SELECT id_student FROM wpic_asg_students WHERE user_id = %d
            ", $current_user));
        }

        // Si aún no hay user_id, usar el primer estudiante disponible
        if (!$user_id) {
            $user_id = $wpdb->get_var("SELECT id_student FROM wpic_asg_students LIMIT 1");
        }
    }

    if (!$user_id) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se pudo determinar el user_id'
        ], 400);
    }

    // Obtener course_code (del parámetro o detectar automáticamente)
    $course_code = $request['course_code'] ?? null;
    if (!$course_code) {
        // Buscar cursos en los que está inscrito el usuario
        $enrolled_courses = $wpdb->get_results($wpdb->prepare("
            SELECT c.code_course
            FROM wpic_asg_enrollments e
            JOIN wpic_courses c ON e.course_id = c.id_course
            WHERE e.student_id = %d
        ", $user_id));

        if (empty($enrolled_courses)) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'Usuario no está inscrito en ningún curso'
            ], 404);
        }

        // Usar el primer curso encontrado
        $course_code = $enrolled_courses[0]->code_course;
    }

    // Verificar si ya existe progreso en la nueva tabla
    $existing = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_user_progress
        WHERE user_id = %d AND course_code = %s
    ", $user_id, $course_code));

    if ($existing) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Progreso ya existe en nueva estructura',
            'data' => [
                'user_id' => $user_id,
                'course_code' => $course_code,
                'total_lessons' => intval($existing->total_lessons),
                'completed_count' => intval($existing->completed_count),
                'progress_percentage' => floatval($existing->progress_percentage)
            ]
        ]);
    }

    // Verificar si existe la tabla antigua
    $old_table_exists = $wpdb->get_var("SHOW TABLES LIKE 'wpic_asg_progress'");

    if ($old_table_exists) {
        // MIGRAR DESDE TABLA ANTIGUA
        $old_progress = $wpdb->get_results($wpdb->prepare("
            SELECT p.*, l.id_lesson, m.code_course
            FROM wpic_asg_progress p
            JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
            JOIN wpic_modules mod ON l.code_module = mod.code_module
            JOIN wpic_courses m ON mod.code_course = m.code_course
            WHERE p.student_id = %d AND m.code_course = %s
        ", $user_id, $course_code));

        if (!empty($old_progress)) {
            // Procesar datos de la tabla antigua
            $completed_lessons = [];
            $total_lessons = count($old_progress);
            $completed_count = 0;
            $last_lesson_id = null;
            $last_completed_at = null;

            foreach ($old_progress as $progress) {
                if ($progress->completed == 1) {
                    $completed_lessons[] = intval($progress->lesson_id);
                    $completed_count++;
                    $last_lesson_id = intval($progress->lesson_id);
                    if ($progress->completion_date) {
                        $last_completed_at = $progress->completion_date;
                    }
                }
            }

            sort($completed_lessons);
            $progress_percentage = $total_lessons > 0 ? ($completed_count / $total_lessons) * 100 : 0;

            // Insertar en nueva tabla
            $result = $wpdb->insert('wpic_asg_user_progress', [
                'user_id' => $user_id,
                'course_code' => $course_code,
                'completed_lessons' => json_encode($completed_lessons),
                'total_lessons' => $total_lessons,
                'completed_count' => $completed_count,
                'progress_percentage' => round($progress_percentage, 2),
                'last_lesson_id' => $last_lesson_id,
                'last_completed_at' => $last_completed_at,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ]);

            if ($result) {
                return new WP_REST_Response([
                    'success' => true,
                    'message' => 'Progreso migrado exitosamente desde tabla antigua',
                    'data' => [
                        'user_id' => $user_id,
                        'course_code' => $course_code,
                        'total_lessons' => $total_lessons,
                        'completed_count' => $completed_count,
                        'progress_percentage' => round($progress_percentage, 2),
                        'completed_lessons' => $completed_lessons,
                        'migrated_from' => 'wpic_asg_progress'
                    ]
                ]);
            } else {
                return new WP_REST_Response([
                    'success' => false,
                    'error' => 'Error insertando en nueva tabla: ' . $wpdb->last_error
                ], 500);
            }
        }
    }

    // CREAR PROGRESO NUEVO (si no existe en tabla antigua)
    $total_lessons = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(l.id_lesson)
        FROM wpic_lessons l
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE m.code_course = %s AND l.is_deleted = 0
    ", $course_code));

    if (!$total_lessons) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se encontraron lecciones para el curso: ' . $course_code
        ], 404);
    }

    // Crear registro inicial
    $result = $wpdb->insert('wpic_asg_user_progress', [
        'user_id' => $user_id,
        'course_code' => $course_code,
        'completed_lessons' => json_encode([]),
        'total_lessons' => $total_lessons,
        'completed_count' => 0,
        'progress_percentage' => 0.00,
        'last_lesson_id' => null,
        'last_completed_at' => null,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ]);

    if ($result) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Progreso inicializado exitosamente',
            'data' => [
                'user_id' => $user_id,
                'course_code' => $course_code,
                'total_lessons' => $total_lessons,
                'completed_count' => 0,
                'progress_percentage' => 0.00,
                'created_new' => true
            ]
        ]);
    } else {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Error creando progreso inicial: ' . $wpdb->last_error
        ], 500);
    }
}

/**
 * INICIALIZACIÓN RÁPIDA - Para resolver error 403 inmediatamente
 */
function asg_quick_init_handler($request) {
    $course_code = sanitize_text_field($request['course_code']);

    // Obtener student_id del usuario actual
    $student_id = ASG_Student_Manager::get_current_student_id();

    // Para testing: si no hay student_id, usar uno de prueba
    if (!$student_id) {
        global $wpdb;
        $student_id = $wpdb->get_var("SELECT id_student FROM wpic_asg_students LIMIT 1");

        if (!$student_id) {
            return new WP_REST_Response([
                'success' => false,
                'error' => 'No se encontró estudiante para inicializar'
            ], 404);
        }
    }

    global $wpdb;

    // Verificar si ya existe progreso
    $existing = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_user_progress
        WHERE user_id = %d AND course_code = %s
    ", $student_id, $course_code));

    if ($existing) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Progreso ya inicializado',
            'data' => [
                'user_id' => $student_id,
                'course_code' => $course_code,
                'total_lessons' => intval($existing->total_lessons),
                'completed_count' => intval($existing->completed_count),
                'progress_percentage' => floatval($existing->progress_percentage),
                'already_exists' => true
            ]
        ]);
    }

    // Obtener total de lecciones del curso
    $total_lessons = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(l.id_lesson)
        FROM wpic_lessons l
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE m.code_course = %s AND l.is_deleted = 0
    ", $course_code));

    if (!$total_lessons) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se encontraron lecciones para el curso: ' . $course_code
        ], 404);
    }

    // Crear progreso inicial
    $result = $wpdb->insert('wpic_asg_user_progress', [
        'user_id' => $student_id,
        'course_code' => $course_code,
        'completed_lessons' => json_encode([]),
        'total_lessons' => $total_lessons,
        'completed_count' => 0,
        'progress_percentage' => 0.00,
        'last_lesson_id' => null,
        'last_completed_at' => null,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ]);

    if ($result) {
        return new WP_REST_Response([
            'success' => true,
            'message' => 'Progreso inicializado exitosamente',
            'data' => [
                'user_id' => $student_id,
                'course_code' => $course_code,
                'total_lessons' => $total_lessons,
                'completed_count' => 0,
                'progress_percentage' => 0.00,
                'created_new' => true
            ]
        ]);
    } else {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'Error inicializando progreso: ' . $wpdb->last_error
        ], 500);
    }
}

/**
 * PRUEBA DE PROGRESO - Para verificar datos de barra de progreso
 */
function asg_test_progress_handler($request) {
    $course_code = sanitize_text_field($request['course_code']);

    // Obtener student_id del usuario actual
    $student_id = ASG_Student_Manager::get_current_student_id();

    if (!$student_id) {
        global $wpdb;
        $student_id = $wpdb->get_var("SELECT id_student FROM wpic_asg_students LIMIT 1");
    }

    if (!$student_id) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se encontró estudiante'
        ], 404);
    }

    global $wpdb;

    // Obtener progreso actual
    $user_progress = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_user_progress
        WHERE user_id = %d AND course_code = %s
    ", $student_id, $course_code));

    if (!$user_progress) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se encontró progreso para este curso',
            'course_code' => $course_code,
            'student_id' => $student_id,
            'suggestion' => 'Ejecuta /quick-init/' . $course_code . ' primero'
        ], 404);
    }

    $completed_lessons = json_decode($user_progress->completed_lessons, true) ?: [];

    return new WP_REST_Response([
        'success' => true,
        'data' => [
            'student_id' => $student_id,
            'course_code' => $course_code,
            'raw_data' => $user_progress,
            'formatted_for_frontend' => [
                'course_progress' => [
                    'completed_lessons' => intval($user_progress->completed_count),
                    'total_lessons' => intval($user_progress->total_lessons),
                    'progress_percentage' => floatval($user_progress->progress_percentage)
                ]
            ],
            'completed_lessons_list' => $completed_lessons,
            'progress_bar_data' => [
                'percentage' => floatval($user_progress->progress_percentage),
                'text' => $user_progress->completed_count . '/' . $user_progress->total_lessons . ' lecciones completadas'
            ]
        ]
    ]);
}

/**
 * DEBUG FRONTEND - Simular exactamente lo que hace el frontend
 */
function asg_debug_frontend_handler($request) {
    $course_code = sanitize_text_field($request['course_code']);

    // Simular exactamente lo que hace loadUserProgress()
    $student_id = ASG_Student_Manager::get_current_student_id();
    if (!$student_id) {
        global $wpdb;
        $student_id = $wpdb->get_var("SELECT id_student FROM wpic_asg_students LIMIT 1");
    }

    if (!$student_id) {
        return new WP_REST_Response([
            'success' => false,
            'error' => 'No se encontró estudiante'
        ], 404);
    }

    // Llamar al endpoint /my-courses como lo hace el frontend
    $dashboard_request = new WP_REST_Request('GET', '/asg/v1/my-courses');
    $dashboard_response = asg_student_dashboard_handler($dashboard_request);
    $dashboard_data = $dashboard_response->get_data();

    $debug_info = [
        'step_1_dashboard_call' => [
            'success' => $dashboard_data['success'] ?? false,
            'courses_count' => count($dashboard_data['data']['courses'] ?? []),
            'raw_response' => $dashboard_data
        ]
    ];

    if ($dashboard_data['success'] && !empty($dashboard_data['data']['courses'])) {
        // Buscar el curso específico como lo hace el frontend
        $courseProgress = null;
        foreach ($dashboard_data['data']['courses'] as $course) {
            if ($course['course_code'] === $course_code || $course['code_course'] === $course_code) {
                $courseProgress = $course;
                break;
            }
        }

        $debug_info['step_2_course_search'] = [
            'course_found' => $courseProgress !== null,
            'searched_for' => $course_code,
            'available_courses' => array_map(function($c) {
                return [
                    'course_code' => $c['course_code'] ?? 'missing',
                    'code_course' => $c['code_course'] ?? 'missing',
                    'name' => $c['course_name'] ?? $c['name_course'] ?? 'missing'
                ];
            }, $dashboard_data['data']['courses'])
        ];

        if ($courseProgress) {
            $debug_info['step_3_course_data'] = [
                'has_completed_lessons_list' => isset($courseProgress['completed_lessons_list']),
                'completed_lessons_list' => $courseProgress['completed_lessons_list'] ?? 'missing',
                'has_unlocked_lessons_list' => isset($courseProgress['unlocked_lessons_list']),
                'unlocked_lessons_list' => $courseProgress['unlocked_lessons_list'] ?? 'missing',
                'progress_percentage' => $courseProgress['progress_percentage'] ?? 'missing',
                'completed_lessons' => $courseProgress['completed_lessons'] ?? 'missing',
                'total_lessons' => $courseProgress['total_lessons'] ?? 'missing',
                'full_course_data' => $courseProgress
            ];

            // Simular updateProgressBar()
            $debug_info['step_4_progress_bar_data'] = [
                'percentage' => $courseProgress['progress_percentage'] ?? 0,
                'text' => ($courseProgress['completed_lessons'] ?? 0) . '/' . ($courseProgress['total_lessons'] ?? 0) . ' lecciones completadas'
            ];

            // Simular updateLessonsListUI()
            $debug_info['step_5_lessons_ui_data'] = [
                'completed_lessons' => $courseProgress['completed_lessons_list'] ?? [],
                'unlocked_lessons' => $courseProgress['unlocked_lessons_list'] ?? [],
                'preview_lessons' => $courseProgress['preview_lessons_list'] ?? [],
                'all_lessons' => $courseProgress['all_lessons_list'] ?? []
            ];
        }
    }

    return new WP_REST_Response([
        'success' => true,
        'debug_info' => $debug_info,
        'frontend_simulation' => 'complete'
    ]);
}

// Registrar endpoints de testing
add_action('rest_api_init', function() {
    register_rest_route('asg/v1', '/test', array(
        'methods' => 'GET',
        'callback' => 'asg_test_endpoint_handler',
        'permission_callback' => '__return_true'
    ));

    register_rest_route('asg/v1', '/auth-diagnostic', array(
        'methods' => 'GET',
        'callback' => 'asg_auth_diagnostic_handler',
        'permission_callback' => '__return_true'
    ));

    register_rest_route('asg/v1', '/test-permission', array(
        'methods' => 'GET',
        'callback' => 'asg_test_endpoint_handler',
        'permission_callback' => 'asg_permission_check_with_init' // Usar el mismo permission callback que my-courses
    ));

    register_rest_route('asg/v1', '/test-complete', array(
        'methods' => ['GET', 'POST'],
        'callback' => 'asg_test_complete_lesson_handler',
        'permission_callback' => '__return_true'
    ));
});

