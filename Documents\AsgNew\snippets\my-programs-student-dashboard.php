<?php
add_shortcode('my_programs_students', 'render_my_programs_students');
function render_my_programs_students() {
    global $wpdb;
    ob_start();

    // Solo mostrar cursos inscritos si el usuario está logueado
    $results = [];
    if (is_user_logged_in()) {
        $current_user_id = get_current_user_id();
        $results = $wpdb->get_results($wpdb->prepare("
            SELECT c.*, e.payment_date, e.amount_paid
            FROM {$wpdb->prefix}asg_enrollments e
            JOIN {$wpdb->prefix}asg_students s ON e.student_id = s.id_student
            JOIN {$wpdb->prefix}courses c ON e.course_id = c.id_course
            WHERE s.id_user = %d AND c.status_course = 'published'
            ORDER BY e.payment_date DESC
        ", $current_user_id));
    }

    ?>
    <style>
        .course-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 20px;
            justify-content: flex-start;
            padding: 0px;
            font-family: 'Segoe UI', sans-serif;
        }

        .course-card {
            background: white;
            border: 1px solid #eee;
            border-radius: 16px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.08);
            overflow: hidden;
            max-width: 220px;
            text-align: center;
            transition: transform 0.3s ease;
        }

        .course-card:hover {
            transform: translateY(-5px);
        }

        .course-card img {
            width: 100%;
            height: 180px;
            object-fit: cover;
        }

        .course-card-title {
            font-weight: bold;
            font-size: 16px;
            color: #1a1a1a;
            margin: 12px 0 8px;
        }

        .course-card-meta {
            display: inline-block;
            font-size: 12px;
            background: #f5f5f5;
            color: #000;
            border: 1px solid #000;
            border-radius: 20px;
            padding: 5px 12px;
            margin-bottom: 12px;
        }

        .course-card-button {
            display: inline-block;
            background-color: #0AAA3F;
            color: #fff;
            font-size: 14px;
            padding: 8px 30px;
            border-radius: 24px;
            margin-bottom: 16px;
            text-decoration: none !important;
            font-weight: 600;
        }

        .course-card-button:hover {
            color: #fff;
            background-color: #088a34;
        }

        p {
            color: #003B71;
            font-size: 24px;
            font-weight: bold;
        }
		
		div h2, div h4{
			font-weight:300;
			color: #006cad;
		}
    </style>

<section class="body-content">
    <header class="page-header d-flex p-2 align-items-center">

        <h5 class="mb-0 px-3 py-2 text-white">
            My Programs
        </h5>
    </header>
	
	<section class="main-content">
		
		<div>
			<h2>
				My Programs
			</h2>
			<h4 class="mb-5">
				<?php if (is_user_logged_in()): ?>
					Here you can see all the programs you've purchased and your progress.
				<?php else: ?>
					Please log in to see your enrolled programs.
				<?php endif; ?>
			</h4>
		</div>

    <div class="course-grid">
        <?php if ($results) : ?>
            <?php foreach ($results as $course) :
                $title = esc_html($course->name_course);
                $description = esc_html($course->description_course);
                $image_url = !empty($course->cover_img) ? esc_url($course->cover_img) : 'https://via.placeholder.com/220x180';
                $code_course = esc_attr($course->code_course);

                // 1. Obtener módulos para este curso
                $module_ids = $wpdb->get_col(
                    $wpdb->prepare(
                        "SELECT id_modules FROM {$wpdb->prefix}modules WHERE course_id = %d AND is_deleted = 0",
                        $course->id_course
                    )
                );
                $module_count = count($module_ids);

                // 2. Obtener lecciones desde wpic_lessons para esos módulos
                $lesson_ids = [];
                $lesson_count = 0;
                if (!empty($module_ids)) {
                    $placeholders = implode(',', array_fill(0, count($module_ids), '%d'));
                    $sql = "SELECT id_lesson FROM {$wpdb->prefix}lessons WHERE module_id IN ($placeholders) AND is_deleted = 0";
                    $prepared = $wpdb->prepare($sql, ...$module_ids);
                    $lesson_ids = $wpdb->get_col($prepared);
                    $lesson_count = count($lesson_ids);
                }

                // 3. Enlace Start Program (a la primera lección si existe)
                $first_lesson_id = !empty($lesson_ids) ? intval($lesson_ids[0]) : 0;
                $start_link = $first_lesson_id
                    ? "https://abilityseminarsgroup.com/lessons/?course={$code_course}&lesson={$first_lesson_id}"
                    : "#";

                ?>
                <div class="course-card">
                    <img src="<?php echo $image_url; ?>" alt="<?php echo $title; ?>">
                    <div class="course-card-title"><?php echo $title; ?></div>
                    <div class="course-card-meta">
                        <?php echo "{$module_count} modules / {$lesson_count} lessons"; ?>
                    </div>
                    <a class="course-card-button" href="<?php echo esc_url($start_link); ?>">Start Program</a>
                </div>
            <?php endforeach; ?>
        <?php else : ?>
            <?php if (is_user_logged_in()): ?>
                <div class="no-courses-message">
                    <div class="no-courses-content">
                        <h3>📚 No tienes cursos inscritos aún</h3>
                        <p>¡Explora nuestro catálogo y encuentra el curso perfecto para ti!</p>
                        <div class="no-courses-actions">
                            <a href="/courses/" class="btn btn-primary">Explorar Cursos</a>
                            <a href="/" class="btn btn-outline-secondary">Ir al Inicio</a>
                        </div>
                    </div>
                </div>
            <?php else: ?>
                <div class="login-required-message">
                    <div class="login-content">
                        <h3>🔐 Inicia sesión para ver tus cursos</h3>
                        <p>Accede a tu cuenta para ver todos los cursos en los que estás inscrito.</p>
                        <div class="login-actions">
                            <a href="/wp-login.php" class="btn btn-primary">Iniciar Sesión</a>
                            <a href="/register/" class="btn btn-outline-secondary">Crear Cuenta</a>
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        <?php endif; ?>
    </div>
	</section>
</section>

    <?php
    return ob_get_clean();
}?>


<script>
	
	document.addEventListener("DOMContentLoaded",function(){
		const sidebar = document.getElementById('sidebar');
    const navList = document.querySelector('.sidebar-nav');
    const bodyContent = document.querySelector('.body-content');

    // Botón 1: toggle sidebar (menú hamburguesa principal)
    document.getElementById('toggleSidebar').addEventListener('click', () => {
        sidebar.classList.toggle('show');
    });

    // Botón 2: colapsar/expandir sidebar completamente
    document.getElementById("toggleSidebar2").addEventListener('click', () => {
        sidebar.classList.toggle('collapsed');
        bodyContent.classList.toggle('collapsed');
        sidebar.style.width = '';
    });

	// ASG STUDENT FLOW INTEGRATION
	// Mejorar la visualización de cursos inscritos con datos del API
	async function enhanceEnrolledCourses() {
	    try {
	        const response = await fetch('/wp-json/asg/v1/my-courses');
	        const result = await response.json();

	        if (result.success && result.data.courses.length > 0) {
	            updateCourseCardsWithProgress(result.data.courses);
	        } else if (result.success && result.data.courses.length === 0) {
	            // No hay cursos inscritos, mostrar mensaje
	            showNoCourseMessage();
	        }
	    } catch (error) {
	        console.error('Error cargando datos de cursos:', error);
	    }
	}

	// Actualizar cards de cursos con datos de progreso del API
	function updateCourseCardsWithProgress(apiCourses) {
	    const courseCards = document.querySelectorAll('.course-card');

	    courseCards.forEach(card => {
	        const cardTitle = card.querySelector('.course-card-title').textContent;

	        // Buscar curso correspondiente en datos del API
	        const apiCourse = apiCourses.find(course =>
	            course.name_course === cardTitle ||
	            card.querySelector('a[href*="' + course.code_course + '"]')
	        );

	        if (apiCourse) {
	            // Agregar información de progreso
	            const meta = card.querySelector('.course-card-meta');
	            if (meta) {
	                // Actualizar meta con progreso real
	                meta.innerHTML = `
	                    <span style="color: #28a745;">✅ Inscrito</span>
	                    <span>Progreso: ${apiCourse.completed_lessons || 0}/${apiCourse.total_lessons || 0}</span>
	                `;

	                // Agregar barra de progreso
	                const progressBar = document.createElement('div');
	                progressBar.className = 'progress-bar-container';
	                progressBar.style.cssText = 'margin: 10px 0; background: #e9ecef; border-radius: 4px; overflow: hidden; height: 8px;';
	                progressBar.innerHTML = `
	                    <div class="progress-bar" style="width: ${apiCourse.progress_percentage || 0}%; height: 100%; background: #28a745; transition: width 0.3s ease;"></div>
	                `;

	                meta.parentNode.insertBefore(progressBar, meta.nextSibling);
	            }

	            // Actualizar botón
	            const button = card.querySelector('.course-card-button');
	            if (button) {
	                button.textContent = 'Continuar Aprendiendo';
	                button.style.background = '#28a745';
	                button.href = `/lessons/?course=${apiCourse.code_course}`;
	            }

	            // Marcar card como inscrito
	            card.classList.add('enrolled-course');
	        }
	    });
	}

	// Mostrar mensaje cuando no hay cursos
	function showNoCourseMessage() {
	    const courseGrid = document.querySelector('.course-grid');
	    if (courseGrid && courseGrid.children.length === 0) {
	        courseGrid.innerHTML = `
	            <div class="no-courses-message" style="text-align: center; padding: 60px 20px;">
	                <h3 style="color: #666; margin-bottom: 15px;">📚 No tienes cursos inscritos aún</h3>
	                <p style="color: #888; margin-bottom: 25px;">¡Explora nuestro catálogo y encuentra el curso perfecto para ti!</p>
	                <div class="no-courses-actions">
	                    <a href="/courses/" class="btn btn-primary" style="margin-right: 10px;">Explorar Cursos</a>
	                    <a href="/" class="btn btn-outline-secondary">Ir al Inicio</a>
	                </div>
	            </div>
	        `;
	    }
	}

	// Mejorar visualización de cursos al inicializar
	if (document.body.classList.contains('logged-in')) {
	    enhanceEnrolledCourses();
	}
	})
</script>

<!-- ASG Student Dashboard Styles -->
<style>
.enrolled-courses-section {
    margin-bottom: 30px;
}

.enrolled-course {
    border: 2px solid #28a745;
    position: relative;
}

.enrolled-course::before {
    content: '✅';
    position: absolute;
    top: 10px;
    right: 10px;
    background: white;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
}

/* No Courses Message Styles */
.no-courses-message, .login-required-message {
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 400px;
    padding: 40px 20px;
}

.no-courses-content, .login-content {
    text-align: center;
    max-width: 500px;
    background: white;
    padding: 40px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    border-top: 4px solid #667eea;
}

.no-courses-content h3, .login-content h3 {
    margin: 0 0 15px 0;
    color: #333;
    font-size: 24px;
}

.no-courses-content p, .login-content p {
    margin: 0 0 25px 0;
    color: #666;
    font-size: 16px;
    line-height: 1.5;
}

.no-courses-actions, .login-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    flex-wrap: wrap;
}

.btn {
    display: inline-block;
    padding: 12px 24px;
    border-radius: 6px;
    text-decoration: none;
    font-weight: 500;
    text-align: center;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    font-size: 14px;
}

.btn-primary {
    background: #667eea;
    color: white;
    border-color: #667eea;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-1px);
}

.btn-outline-secondary {
    background: transparent;
    color: #6c757d;
    border-color: #6c757d;
}

.btn-outline-secondary:hover {
    background: #6c757d;
    color: white;
}

/* Progress Bar Styles */
.progress-bar-container {
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 10px 0;
    height: 8px;
}

.progress-bar {
    background: #28a745;
    height: 100%;
    transition: width 0.3s ease;
    border-radius: 4px;
}

/* Responsive */
@media (max-width: 768px) {
    .no-courses-content, .login-content {
        margin: 20px;
        padding: 30px 20px;
    }

    .no-courses-actions, .login-actions {
        flex-direction: column;
        align-items: center;
    }

    .btn {
        width: 100%;
        max-width: 250px;
    }
}
</style>
