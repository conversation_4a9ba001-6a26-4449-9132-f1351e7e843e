# 🎓 ASG Student Flow System - README

## 📋 **RESUMEN DEL PROYECTO**

Sistema de gestión de estudiantes para **AbilitySeminarsGroup** que permite:
- ✅ Inscripción de estudiantes con pago PayPal
- ✅ Control de acceso a cursos
- ✅ Tracking de progreso de lecciones
- ✅ Dashboard del estudiante
- ✅ Gestión de múltiples cursos por estudiante

## 🏗️ **ARQUITECTURA DEL SISTEMA**

### **Enfoque Híbrido Optimizado:**
- **Base de datos minimalista** (3 tablas esenciales)
- **Validación con WordPress** (funciones nativas)
- **Sin foreign keys a WordPress** (máxima compatibilidad)
- **Datos enriquecidos en tiempo real**
- **Transacciones seguras**


## 🔄 **FLUJOS COMPLETOS DEL SISTEMA**

### **🎯 FLUJO A: VISITANTE NO REGISTRADO (Usuario Nuevo)**

#### **PASO 1: Descubrimiento del Curso**
- Visitante anónimo navega a `vistapreliminar.php?course=course_1`
- Ve información completa del curso, precio y botón "Inscribirse"
- **Estado**: No logueado, no registrado en WordPress

#### **PASO 2: Intento de Inscripción**
- Clic en "Inscribirse $99" → Verificación de login (JavaScript inline)
- **Resultado**: Modal aparece con dos opciones

#### **PASO 3: Modal de Registro/Login**
```
┌─────────────────────────────────────────┐
│  Para inscribirte necesitas una cuenta  │
│                                         │
│  [Crear Cuenta Nueva] [Iniciar Sesión] │
│                                         │
│  ¿Por qué crear una cuenta?             │
│  ✅ Acceso a todos tus cursos           │
│  ✅ Seguimiento de progreso             │
│  ✅ Certificados descargables           │
└─────────────────────────────────────────┘
```

#### **PASO 4A: Opción "Crear Cuenta Nueva"**
- Redirect a `/register/?redirect_course=course_1&price=99`
- Formulario de registro muestra recordatorio del curso deseado
- Datos del curso se guardan en localStorage
- Usuario completa registro en WordPress

#### **PASO 5A: Post-Registro Automático**
- Hook de WordPress detecta enrollment pendiente
- Redirect automático a `/payment/?course=course_1&price=99&new_user=1`
- Mensaje de bienvenida + countdown visual

#### **PASO 4B: Opción "Iniciar Sesión"**
- Redirect a `/wp-login.php?redirect_to=vistapreliminar.php?course=course_1`
- Usuario ingresa credenciales existentes
- Redirect automático de vuelta a vistapreliminar.php
- Puede hacer clic en "Inscribirse" y ir directo a pago

### **🎯 FLUJO B: USUARIO REGISTRADO (Ya tiene cuenta WordPress)**

#### **PASO 1: Descubrimiento del Curso**
- Usuario logueado navega a `vistapreliminar.php?course=course_1`
- Ve información del curso, precio y botón "Inscribirse"

#### **PASO 2: Proceso de Inscripción Directo**
- Clic en "Inscribirse" → Verificación de login exitosa
- Verificación de enrollment existente (por si ya compró el curso)
- Si no está inscrito → Redirect directo a `/payment/?course=course_1&price=99`

### **🎯 FLUJO C: PROCESO DE PAGO (Común para ambos tipos de usuario)**

#### **PASO 3: Pago con PayPal**
- Redirect a `/payment/?course=course_1&price=99` (payment-page.php)
- Página muestra resumen del curso + integración PayPal inline
- Pago en 2 clics con PayPal SDK
- Confirmación automática y procesamiento

#### **PASO 4: Procesamiento Backend**
- Endpoint `/wp-json/asg/v1/process-enrollment`
- Crear/obtener estudiante en `wpic_asg_students`
- Crear enrollment en `wpic_asg_enrollments`
- Inicializar progreso en `wpic_asg_progress`
- Transacción segura con rollback

#### **PASO 5: Acceso Inmediato**
- Redirect automático a `lessons.php?course=course_1`
- Verificación de acceso via API
- Acceso completo a todas las lecciones

#### **PASO 6: Experiencia de Aprendizaje**
- Navegación por lecciones
- Progreso automático al completar
- Tracking en tiempo real

### **🎯 FLUJO D: ESTUDIANTE EXISTENTE COMPRA SEGUNDO CURSO**

#### **Escenario**: Usuario ya compró `course_1`, ahora quiere `course_2`

#### **PASO 1**: Ve `vistapreliminar.php?course=course_2`
#### **PASO 2**: Clic "Inscribirse" → Ya logueado → Directo a payment
#### **PASO 3**: Pago exitoso → **MISMO student_id**, **NUEVA fila** en enrollments
#### **PASO 4**: Progreso inicializado para lecciones del nuevo curso
#### **PASO 5**: Dashboard muestra ambos cursos con progreso independiente

## 🚀 **ARCHIVOS A IMPLEMENTAR**

### **1. Backend (PHP)**
```
📁 Raíz del proyecto/
├── asg-student-endpoints.php     # 4 endpoints REST API principales
└── payment-page.php              # Página de pago con PayPal integrado

📁 snippets/ (archivos existentes a modificar)
├── vistapreliminar.php           # Agregar botón de enrollment optimizado
├── lessons.php                   # Agregar verificación de acceso
└── endpoint.php                  # Integrar nuevos endpoints
```

### **2. Frontend (JavaScript Inline)**
```
📁 Raíz del proyecto/
├── vistapreliminar-enrollment-button.js  # Código para vistapreliminar.php
└── lessons-enrollment-check.js           # Código para lessons.php

📁 Integración en archivos existentes:
├── vistapreliminar.php → Incluir vistapreliminar-enrollment-button.js
├── lessons.php → Incluir lessons-enrollment-check.js
├── register.html → Agregar detección de parámetros + recordatorio visual
└── payment-page.php → JavaScript PayPal integrado inline
```

### **3. Base de Datos**
```
📁 database/
└── data.sql                      # Estructura de las 3 tablas nuevas
```

## 🔧 **ENDPOINTS API**

### **1. Procesar Enrollment**
```
POST /wp-json/asg/v1/process-enrollment
Body: {
    "course_code": "course_1",
    "paypal_payment_id": "PAYPAL123",
    "amount_paid": 99.00
}
```

### **2. Verificar Acceso**
```
GET /wp-json/asg/v1/check-enrollment?course=course_1
Response: {
    "success": true,
    "enrolled": true
}
```

### **3. Completar Lección**
```
POST /wp-json/asg/v1/lesson/{lesson_id}/complete
Response: {
    "success": true,
    "message": "Lección completada"
}
```

### **4. Dashboard del Estudiante**
```
GET /wp-json/asg/v1/my-courses
Response: {
    "success": true,
    "data": {
        "courses": [...],
        "stats": {...}
    }
}
```

## 💡 **CARACTERÍSTICAS TÉCNICAS**

### **✅ Seguridad**
- Validación con funciones nativas de WordPress
- Transacciones de base de datos
- Verificación de integridad en cada operación
- Logging completo para auditoría

### **✅ Rendimiento**
- Solo 3 tablas minimalistas
- Consultas optimizadas con índices
- Datos enriquecidos en tiempo real
- Caché automático de WordPress

### **✅ Escalabilidad**
- Estructura relacional eficiente
- Sin foreign keys problemáticas
- Fácil migración y backup
- Compatible con clustering

### **✅ Compatibilidad**
- 100% compatible con WordPress
- Sin conflictos con plugins
- Actualizaciones seguras
- Funciona con cualquier hosting

## 🎯 **FLUJO DE DATOS**

### **Usuario Nuevo:**
```
WordPress User (ID: 42)
    ↓ Primera compra
wpic_asg_students (student_id: 1, id_user: 42)
    ↓
wpic_asg_enrollments (student_id: 1, course_id: 5)
    ↓
wpic_asg_progress (student_id: 1, lesson_id: 101-108)
```

### **Usuario Existente (Segunda Compra):**
```
Mismo Student (student_id: 1)
    ↓ Nueva compra
Nueva fila en wpic_asg_enrollments (student_id: 1, course_id: 8)
    ↓
Nuevas filas en wpic_asg_progress (student_id: 1, lesson_id: 201-205)
```

## 📋 **TAREAS DE IMPLEMENTACIÓN**

### **Fase 1: Base de Datos**
- [ ] Ejecutar `database/data.sql` en phpMyAdmin
- [ ] Verificar creación de las 3 tablas nuevas
- [ ] Probar inserción manual de datos de prueba

### **Fase 2: Backend**
- [ ] Subir `asg-student-endpoints.php` a la raíz del proyecto
- [ ] Subir `payment-page.php` a la raíz del proyecto
- [ ] Configurar PayPal Client ID en `payment-page.php`
- [ ] Integrar endpoints en `snippets/endpoint.php` existente
- [ ] Probar endpoints con Postman/navegador

### **Fase 3: Frontend (Modificaciones a archivos existentes)**
- [ ] **vistapreliminar.php**: Agregar código de `vistapreliminar-enrollment-button.js` al final del archivo
- [ ] **lessons.php**: Agregar código de `lessons-enrollment-check.js` al final del archivo
- [ ] **register.html**: Agregar detección de parámetros de curso y recordatorio visual
- [ ] **payment-page.php**: Ya incluye JavaScript PayPal inline (no archivos separados)
- [ ] Crear página WordPress para payment-page.php con shortcode

### **Fase 4: Integración WordPress**
- [ ] Registrar página `/payment/` en WordPress
- [ ] Configurar shortcode `[asg_payment_page]`
- [ ] Agregar hook `user_register` para post-registro automático
- [ ] Verificar que endpoints REST API funcionan
- [ ] Probar autenticación de usuarios
- [ ] Configurar redirects de login/registro

### **Fase 5: Testing Completo**
- [ ] **Flujo A**: Probar visitante no registrado → registro → pago → acceso
- [ ] **Flujo B**: Probar usuario existente → login → pago → acceso
- [ ] **Flujo C**: Probar usuario ya inscrito → mensaje de "ya inscrito"
- [ ] **Flujo D**: Probar múltiples compras del mismo usuario
- [ ] Verificar acceso a lecciones post-pago
- [ ] Testing de errores y rollbacks
- [ ] Verificar dashboard del estudiante con múltiples cursos

### **Fase 6: Producción**
- [ ] Cambiar PayPal Client ID a producción
- [ ] Configurar webhooks PayPal (opcional)
- [ ] Monitoring y logs en servidor
- [ ] Backup automático de base de datos
- [ ] Documentación para soporte técnico

## 🔍 **CONSULTAS ÚTILES**

### **Ver todos los estudiantes:**
```sql
SELECT s.*, u.user_login, u.user_email
FROM wpic_asg_students s
JOIN wpic_users u ON s.id_user = u.ID;
```

### **Ver enrollments con detalles:**
```sql
SELECT e.*, c.name_course, u.user_login, e.amount_paid
FROM wpic_asg_enrollments e
JOIN wpic_asg_students s ON e.student_id = s.id_student
JOIN wpic_users u ON s.id_user = u.ID
JOIN wpic_courses c ON e.course_id = c.id_course
ORDER BY e.payment_date DESC;
```

### **Ver progreso de un estudiante:**
```sql
SELECT c.name_course, 
       COUNT(*) as total_lessons,
       SUM(sp.completed) as completed_lessons,
       ROUND((SUM(sp.completed) / COUNT(*)) * 100, 1) as progress_percentage
FROM wpic_asg_progress sp
JOIN wpic_lessons l ON sp.lesson_id = l.id_lesson
JOIN wpic_modules m ON l.code_module = m.code_module
JOIN wpic_courses c ON m.code_course = c.code_course
WHERE sp.student_id = 1
GROUP BY c.id_course;
```

## 🎯 **VENTAJAS DEL SISTEMA**

### **Para el Negocio:**
- ✅ Conversión optimizada (pago en 2 clics)
- ✅ Acceso inmediato post-pago
- ✅ Tracking completo de estudiantes
- ✅ Escalable para miles de usuarios

### **Para el Desarrollo:**
- ✅ Código limpio y modular
- ✅ Fácil mantenimiento
- ✅ Compatible con WordPress
- ✅ Bien documentado

### **Para el Usuario:**
- ✅ Experiencia fluida
- ✅ Acceso de por vida
- ✅ Progreso visual
- ✅ Dashboard personalizado

---

## 👥 **EQUIPO DE DESARROLLO**

**Desarrollador:** Bryan Marc  
**Desarrollador** Armando Rodriguez 
**Proyecto:** ASG ONLINE FLOW
**Fecha:** 2025

---

*Este README contiene toda la información necesaria para implementar el sistema completo. Cualquier duda, revisar los archivos de código o contactar al equipo de desarrollo.*
