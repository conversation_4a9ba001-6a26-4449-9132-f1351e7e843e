<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ability Seminars - Register</title>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Lato:wght@400;700&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Inter', 'Lato', Arial, sans-serif;
          
        }

        .register-container {
            background: linear-gradient(180deg, #2B7CB8 0%, #1E6BA8 50%, #165388 100%);
            border-radius: 20px;
            box-shadow: 12px 0px 35px 6px rgba(176, 176, 176, 0.5);
            padding: 40px;
            width: 100%;
            max-width: 600px;
            color: white;
            position: relative;
        }

        .logo {
            text-align: center;
            margin-bottom: 30px;
        }

        .logo img {
            max-width: 200px;
            height: auto;
        }

        .register-header {
            text-align: center;
            margin-bottom: 30px;
        }

        .register-header h1 {
            font-family: "Inter", Sans-serif;
            font-size: 28px;
            font-weight: 700;
            margin-bottom: 10px;
            color: white;
        }

        .register-header p {
            font-family: "Lato", Sans-serif;
            font-size: 16px;
            font-weight: 400;
            color: rgba(255, 255, 255, 0.9);
            line-height: 1.4;
        }

        /* Form Styles */
        .form {
            width: 100%;
        }

        .form__group {
            margin-bottom: 20px;
        }

        .form__group label {
            display: block;
            color: white;
            font-family: "Inter", Sans-serif;
            font-weight: 500;
            margin-bottom: 8px;
            font-size: 14px;
        }

        .required {
            color: #ff6b6b;
        }

        .form__group input,
        .form__group select {
            width: 100%;
            padding: 15px 20px;
            border: none;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            font-family: "Lato", Sans-serif;
            font-size: 16px;
            font-weight: bold;
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }

        .form__group input::placeholder {
            color: rgba(255, 255, 255, 0.8);
            font-weight: 400;
        }

        .form__group input:focus,
        .form__group select:focus {
            outline: none;
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.3);
        }

        .form__group select option {
            background: #165388;
            color: white;
        }

        /* Two column layout for name fields */
        .form-row {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        /* Checkbox styles */
        .checkbox-row {
            display: flex;
            flex-wrap: wrap;
            align-items: center;
            gap: 20px;
            margin-top: 10px;
        }

        .checkbox-row > div {
            display: flex;
            align-items: center;
            gap: 10px;
            white-space: nowrap;
        }

        .checkbox-row input[type="checkbox"] {
            width: 18px;
            height: 18px;
            min-width: 18px;
            border: 2px solid white;
            border-radius: 4px;
            background: transparent;
            cursor: pointer;
        }

        .checkbox-row input[type="checkbox"]:checked {
            background: white;
            border-color: white;
        }

        .checkbox-row label {
            font-size: 14px !important;
            margin-bottom: 0 !important;
            cursor: pointer;
        }

        /* Password toggle */
        .password-container {
            position: relative;
        }

        .toggle-password {
            position: absolute;
            top: 50%;
            right: 15px;
            transform: translateY(-50%);
            cursor: pointer;
            color: rgba(255, 255, 255, 0.8);
        }

        /* Terms checkbox */
        .terms-group {
            display: flex;
            align-items: flex-start;
            gap: 10px;
            margin: 20px 0;
        }

        .terms-group input[type="checkbox"] {
            width: 18px;
            height: 18px;
            min-width: 18px;
            margin-top: 2px;
        }

        .terms-group label {
            font-size: 14px;
            line-height: 1.4;
            margin-bottom: 0;
        }

        .terms-group a {
            color: #87CEEB;
            text-decoration: underline;
        }

        /* Custom select styles */
        .custom-select {
            position: relative;
            width: 100%;
        }

        .custom-select-options {
            position: absolute;
            top: 100%;
            left: 0;
            right: 0;
            border: 1px solid rgba(255, 255, 255, 0.3);
            border-top: none;
            background: rgba(22, 83, 136, 0.95);
            backdrop-filter: blur(10px);
            max-height: 200px;
            overflow-y: auto;
            z-index: 1000;
            display: none;
            border-radius: 0 0 10px 10px;
        }

        .custom-select-option {
            padding: 12px 20px;
            cursor: pointer;
            color: white;
            font-size: 14px;
        }

        .custom-select-option:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Hidden fields for conditional display */
        .hidden-field {
            display: none !important;
        }

        .form__group.hidden-field {
            display: none !important;
        }

        /* Password validation styles */
        .input-success {
            border: 2px solid #34c759 !important;
            background-color: rgba(52, 199, 89, 0.1) !important;
        }

        .input-error-match {
            border: 2px solid #ff6b6b !important;
            background-color: rgba(255, 107, 107, 0.1) !important;
        }

        #password-match-message {
            margin-top: 5px;
            font-size: 12px;
        }

        /* Submit button */
        .submit-btn {
            background: white;
            border: 2px solid white;
            border-radius: 10px;
            color: #165388;
            font-family: "Inter", Sans-serif;
            font-size: 16px;
            font-weight: bold;
            height: 50px;
            width: 100%;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-btn:hover {
            background: rgba(255, 255, 255, 0.9);
            transform: translateY(-2px);
        }

        .submit-btn:disabled {
            opacity: 0.7;
            cursor: not-allowed;
            transform: none;
        }

        /* Error messages */
        .form__error {
            color: #ff6b6b;
            font-size: 12px;
            margin-top: 5px;
            display: none;
        }

        .input-error {
            border: 2px solid #ff6b6b !important;
            background-color: rgba(255, 107, 107, 0.1) !important;
        }

        /* Success message */
        .register-message {
            padding: 12px 20px;
            margin: 15px 0;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
        }

        .register-message.success {
            background: rgba(52, 199, 89, 0.1);
            color: #34c759;
            border: 1px solid rgba(52, 199, 89, 0.3);
        }

        .register-message.error {
            background: rgba(255, 59, 48, 0.1);
            color: #ff3b30;
            border: 1px solid rgba(255, 59, 48, 0.3);
        }

        /* Modal Styles */
        .popup {
            background: #fff;
            border-radius: 15px;
            box-shadow: 0px 8px 25px rgba(0, 0, 0, 0.3);
            color: #333;
            left: 50%;
            padding: 30px;
            position: fixed;
            text-align: left;
            top: 50%;
            transform: translate(-50%, -50%) scale(0.1);
            transition: transform 0.4s, top 0.4s;
            visibility: hidden;
            width: 90%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            z-index: 1000;
        }

        .open-popup {
            transform: translate(-50%, -50%) scale(1);
            visibility: visible;
        }

        .popup__background {
            background: rgba(0, 0, 0, 0.5);
            content: '';
            height: 100%;
            left: 0;
            position: fixed;
            top: 0;
            width: 100%;
            z-index: 999;
            display: none;
        }

        .open-popup + .popup__background {
            display: block;
        }

        .popup h4 {
            color: #165388;
            font-family: "Inter", Sans-serif;
            font-weight: 700;
            margin-bottom: 15px;
            font-size: 18px;
        }

        .popup p {
            color: #333;
            font-family: 'Lato', Sans-serif;
            font-weight: 400;
            line-height: 1.6;
            margin-bottom: 15px;
            text-align: justify;
        }

        .popup button {
            background-color: #165388;
            border: 2px solid #165388;
            border-radius: 8px;
            color: #ffffff;
            font-family: "Inter", Sans-serif;
            font-size: 16px;
            font-weight: 700;
            padding: 12px 30px;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            width: 100%;
        }

        .popup button:hover {
            background-color: #ffffff;
            color: #165388;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .register-container {
                padding: 30px 20px;
                margin: 10px;
            }

            .form-row {
                grid-template-columns: 1fr;
            }

            .checkbox-row {
                flex-direction: column;
                align-items: flex-start;
            }

            .popup {
                padding: 20px;
                width: 95%;
                max-height: 85vh;
            }
        }
    </style>
</head>
<body>
    <div class="register-container">
        <!-- Logo -->
        <div class="logo">
            <img src="https://abilityseminarsgroup.com/wp-content/uploads/2025/07/white-ASG-logo.png" alt="Ability Seminars Group">
        </div>

        <!-- Header -->
        <div class="register-header">
            <h1>Create an account to get started</h1>
            <p>Ability Seminars Register Form.</p>
        </div>

        <!-- Registration Form -->
        <form id="registerForm" class="form">
            <!-- Username -->
            <div class="form__group">
                <label for="username">Username <span class="required">*</span></label>
                <input type="text" id="username" name="username" required>
                <div class="form__error"></div>
            </div>

            <!-- Name Fields -->
            <div class="form-row">
                <div class="form__group">
                    <label for="first-name">First Name <span class="required">*</span></label>
                    <input type="text" id="first-name" name="firstName" required>
                    <div class="form__error"></div>
                </div>
                <div class="form__group">
                    <label for="last-name">Last Name <span class="required">*</span></label>
                    <input type="text" id="last-name" name="lastName" required>
                    <div class="form__error"></div>
                </div>
            </div>

            <!-- Gender -->
            <div class="form__group">
                <label for="gender">Gender <span class="required">*</span></label>
                <select id="gender" name="gender" required>
                    <option value="">- Select an option -</option>
                    <option value="male">Male</option>
                    <option value="female">Female</option>
                </select>
                <div class="form__error"></div>
            </div>

            <!-- Institution Type -->
            <div class="form__group">
                <label>Which institution do you belong to? <span class="required">*</span></label>
                <div class="checkbox-row">
                    <div>
                        <input type="checkbox" id="school-individual" name="schoolType" value="Individual">
                        <label for="school-individual">Individual</label>
                    </div>
                    <div>
                        <input type="checkbox" id="school-universidad" name="schoolType" value="Universidad">
                        <label for="school-universidad">Schools and Universities</label>
                    </div>
                    <div>
                        <input type="checkbox" id="school-business" name="schoolType" value="Business">
                        <label for="school-business">Business</label>
                    </div>
                </div>
                <div id="checkbox-error-message" style="color: #ff6b6b; display: none; margin-top: 10px; font-size: 12px;">
                    Please select at least one option.
                </div>
            </div>

            <!-- Business Name (conditional) -->
            <div id="business-input-container" class="form__group hidden-field">
                <label for="business-input">Company Name</label>
                <input type="text" id="business-input" placeholder="Enter your company name">
                <div class="form__error"></div>
            </div>

            <!-- University Selector (conditional) -->
            <div id="universidad-container" class="form__group hidden-field">
                <label for="universidad-input">School Name</label>
                <div class="custom-select" id="custom-universidad">
                    <input type="text" id="universidad-input" placeholder="- Select an option -" autocomplete="off">
                    <input type="hidden" name="universidad" id="universidad-hidden">
                    <div class="custom-select-options"></div>
                </div>
                <div class="form__error"></div>
            </div>

            <!-- Age -->
            <div class="form__group">
                <label for="age">Age <span class="required">*</span></label>
                <select id="age" name="age" required>
                    <option value="" disabled selected>- Select an option -</option>
                    <option value="18-29">18-29 Years</option>
                    <option value="30-44">30-44 Years</option>
                    <option value="45-59">45-59 Years</option>
                    <option value="60+">60+ Years</option>
                </select>
                <div class="form__error"></div>
            </div>

            <!-- Language -->
            <div class="form__group">
                <label for="language">Language <span class="required">*</span></label>
                <select id="language" name="language" required>
                    <option value="" disabled selected>- Select an option -</option>
                    <option value="english">English</option>
                    <option value="spanish">Spanish</option>
                </select>
                <div class="form__error"></div>
            </div>

            <!-- Phone -->
            <div class="form__group">
                <label for="phone">Phone Number <span class="required">*</span></label>
                <input type="tel" id="phone" name="phone" required>
                <div class="form__error"></div>
            </div>

            <!-- Email -->
            <div class="form__group">
                <label for="email">Email Address <span class="required">*</span></label>
                <input type="email" id="email" name="email" required>
                <div class="form__error"></div>
            </div>

            <!-- Password -->
            <div class="form__group">
                <label for="password">Password <span class="required">*</span></label>
                <div class="password-container">
                    <input type="password" id="password" name="password" required>
                    <span class="toggle-password" data-target="password">👁️</span>
                </div>
                <div class="form__error"></div>
            </div>

            <!-- Confirm Password -->
            <div class="form__group">
                <label for="repeat-password">Repeat Password <span class="required">*</span></label>
                <div class="password-container">
                    <input type="password" id="repeat-password" name="confirmPassword" required>
                    <span class="toggle-password" data-target="repeat-password">👁️</span>
                </div>
                <div id="password-match-message" class="form__error"></div>
            </div>

            <!-- Terms -->
            <div class="terms-group">
                <input type="checkbox" id="terms" name="terms" required>
                <label for="terms">
                    By submitting, I agree to the <a href="#" onclick="showTerms()">Terms & Conditions.</a>
                </label>
            </div>

            <!-- Submit Button -->
            <button type="submit" class="submit-btn">Submit</button>
        </form>

        <!-- Login Link -->
        <div style="text-align: center; margin-top: 20px;">
            <p style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
                Already have an account?
                <a href="/login/" style="color: #87CEEB; text-decoration: underline;">Log in here</a>
            </p>
        </div>
    </div>

    <!-- Modal for Terms & Conditions -->
    <div class="popup" id="popup">
        <div class="popup__content"></div>
        <button type="button" onclick="closePopup()">OK</button>
    </div>
    <div class="popup__background"></div>

    <script>
        // Lista de universidades (igual que en register.html)
        const universidades = [
            "ALS",
            "Centro Educativo Alta Vista",
            "Centro Educativo Club 20-30",
            "Colegio Luis Hess",
            "Colegio Santa Rosa",
            "Colegio Utesiano",
            "Escuela Andrés Brugal Montañéz",
            "Escuela Félix Henríquez",
            "Escuela Llano de Pérez",
            "Escuela Sandra Ivelisse Corniel",
            "Instituto Tecnológico de Santo Domingo (INTEC)",
            "Instituto Nacional de Formación Técnico Profesional (INFOTEP)",
            "Liceo Eugenio María de Hostos",
            "Liceo Gregorio Luperón",
            "Liceo Juan N. Ravelo",
            "Liceo Llano de Pérez",
            "Liceo Luisa Argentina Castille",
            "Liceo Madre Teresa de Calcuta",
            "Liceo María Mercedes Meyreles",
            "Politecnico Gregorio Urbano Gilbert",
            "Politécnico Prof. José Morel",
            "Politécnico Prof. Juan Bosch",
            "Politécnico Romel Cruz",
            "Pontificia Universidad Católica Madre y Maestra (PUCMM)",
            "Profesor Andrés Brito Bruno",
            "TV Centro Saballo",
            "Universidad APEC (UNAPEC)",
            "Universidad Adventista Dominicana (UNAD)",
            "Universidad Agroforestal Fernando Arturo de Meriño (UAFAM)",
            "Universidad Autónoma de Santo Domingo (UASD)",
            "Universidad Católica Nordestana (UCNE)",
            "Universidad Católica Santo Domingo (UCSD)",
            "Universidad Católica Tecnológica de Barahona (UCATEBA)",
            "Universidad Católica del Cibao (UCATECI)",
            "Universidad Central del Este (UCE)",
            "Universidad Dominicana O&M",
            "Universidad Dominicana OYM",
            "Universidad Eugenio María de Hostos (UNIREMHOS)",
            "Universidad Federico Henríquez y Carvajal (UFHEC)",
            "Universidad Iberoamericana (UNIBE)",
            "Universidad Nacional Evangélica (UNEV)",
            "Universidad Nacional Pedro Henríquez Ureña (UNPHU)",
            "Universidad Psicología Industrial Dominicana (UPID)",
            "Universidad Tecnológica de Santiago (UTESA)",
            "Universidad del Caribe (UNICARIBE)",
            "Universidad Abierta para Adultos (UAPA)",
            "Universidad ISA (ISA)",
            "Villa Paraíso"
        ].sort();

        // Password toggle functionality
        document.querySelectorAll('.toggle-password').forEach(toggle => {
            toggle.addEventListener('click', () => {
                const targetId = toggle.getAttribute('data-target');
                const input = document.getElementById(targetId);

                if (input.type === 'password') {
                    input.type = 'text';
                    toggle.textContent = '🙈';
                } else {
                    input.type = 'password';
                    toggle.textContent = '👁️';
                }
            });
        });

        // Password match validation
        function validatePasswordMatch() {
            const pass1 = document.getElementById("password");
            const pass2 = document.getElementById("repeat-password");
            const message = document.getElementById("password-match-message");

            if (!pass1.value || !pass2.value) {
                message.textContent = "";
                pass2.classList.remove("input-success", "input-error-match");
                return;
            }

            if (pass1.value === pass2.value) {
                message.textContent = "✅ Passwords match!";
                message.style.color = "#34c759";
                pass2.classList.remove("input-error-match");
                pass2.classList.add("input-success");
            } else {
                message.textContent = "❌ Passwords do not match!";
                message.style.color = "#ff6b6b";
                pass2.classList.remove("input-success");
                pass2.classList.add("input-error-match");
            }
        }

        document.getElementById("repeat-password").addEventListener("input", validatePasswordMatch);
        document.getElementById("password").addEventListener("input", validatePasswordMatch);

        // Checkbox group validation
        function validateCheckboxGroup() {
            const checkboxes = document.querySelectorAll('input[name="schoolType"]');
            const atLeastOneChecked = Array.from(checkboxes).some(cb => cb.checked);
            const errorContainer = document.getElementById("checkbox-error-message");

            if (!atLeastOneChecked) {
                errorContainer.style.display = "block";
                return false;
            } else {
                errorContainer.style.display = "none";
                return true;
            }
        }

        // Initialize form functionality
        document.addEventListener('DOMContentLoaded', function() {
            // School type checkbox logic
            document.querySelectorAll('input[name="schoolType"]').forEach(checkbox => {
                checkbox.addEventListener('change', function() {
                    // Exclusive checkbox behavior
                    document.querySelectorAll('input[name="schoolType"]').forEach(cb => {
                        if(cb !== this) cb.checked = false;
                    });

                    // Show/hide conditional fields
                    const businessContainer = document.getElementById('business-input-container');
                    const universidadContainer = document.getElementById('universidad-container');
                    const universidadHidden = document.getElementById('universidad-hidden');

                    if (this.value === "Individual" && this.checked) {
                        businessContainer.classList.add('hidden-field');
                        universidadContainer.classList.add('hidden-field');
                        universidadHidden.value = "Individual";
                    } else if (this.value === "Business" && this.checked) {
                        businessContainer.classList.remove('hidden-field');
                        universidadContainer.classList.add('hidden-field');
                        universidadHidden.value = "";
                    } else if (this.value === "Universidad" && this.checked) {
                        universidadContainer.classList.remove('hidden-field');
                        businessContainer.classList.add('hidden-field');
                        universidadHidden.value = "";
                    } else {
                        businessContainer.classList.add('hidden-field');
                        universidadContainer.classList.add('hidden-field');
                        universidadHidden.value = "";
                    }
                });
            });

            // Business input updates hidden field
            document.getElementById('business-input').addEventListener('input', function() {
                document.getElementById('universidad-hidden').value = this.value;
            });

            // Custom select for universities
            const customSelect = document.getElementById("custom-universidad");
            const inputField = document.getElementById("universidad-input");
            const hiddenInput = document.getElementById("universidad-hidden");
            const optionsContainer = customSelect.querySelector(".custom-select-options");

            function renderOptions(filter = "") {
                optionsContainer.innerHTML = "";
                const filterLower = filter.toLowerCase();

                let filteredUniversities;
                if (filterLower.length === 1) {
                    filteredUniversities = universidades
                        .filter(univ => univ.toLowerCase().startsWith(filterLower))
                        .sort();
                } else {
                    filteredUniversities = universidades.filter(univ => univ.toLowerCase().includes(filterLower));
                }

                filteredUniversities.forEach(function (univ) {
                    const optionDiv = document.createElement("div");
                    optionDiv.className = "custom-select-option";
                    optionDiv.textContent = univ;
                    optionDiv.dataset.value = univ;
                    optionsContainer.appendChild(optionDiv);
                });

                optionsContainer.style.display = optionsContainer.children.length > 0 ? "block" : "none";
            }

            inputField.addEventListener("input", function () {
                hiddenInput.value = "";
                renderOptions(this.value);
            });

            inputField.addEventListener("focus", function () {
                renderOptions(this.value);
            });

            optionsContainer.addEventListener("click", function (e) {
                if (e.target && e.target.classList.contains("custom-select-option")) {
                    const selectedValue = e.target.dataset.value;
                    inputField.value = selectedValue;
                    hiddenInput.value = selectedValue;
                    optionsContainer.style.display = "none";
                }
            });

            document.addEventListener("click", function (e) {
                if (!customSelect.contains(e.target)) {
                    optionsContainer.style.display = "none";
                }
            });

            inputField.addEventListener("blur", function () {
                setTimeout(function () {
                    if (!universidades.includes(inputField.value)) {
                        inputField.value = "";
                        hiddenInput.value = "";
                    }
                    optionsContainer.style.display = "none";
                }, 200);
            });
        });

        // Form submission
        document.getElementById('registerForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            // Validate checkbox group first
            if (!validateCheckboxGroup()) {
                return;
            }

            // Get form data
            const formData = new FormData(this);
            const data = {
                username: formData.get('username').trim(),
                email: formData.get('email').trim(),
                password: formData.get('password'),
                name: formData.get('firstName').trim(),
                lastName: formData.get('lastName').trim(),
                phone: formData.get('phone').trim(),
                age: formData.get('age'),
                gender: formData.get('gender'),
                language: formData.get('language'),
                university: document.getElementById('universidad-hidden').value || 'Individual'
            };

            // Basic validation
            if (!data.username || !data.email || !data.password || !data.name || !data.lastName) {
                showMessage('Please fill in all required fields', 'error');
                return;
            }

            if (data.password !== formData.get('confirmPassword')) {
                showMessage('Passwords do not match', 'error');
                return;
            }

            if (!document.getElementById('terms').checked) {
                showMessage('Please accept the terms and conditions', 'error');
                return;
            }
            
            const submitBtn = this.querySelector('.submit-btn');
            const originalText = submitBtn.textContent;
            submitBtn.textContent = 'Creating Account...';
            submitBtn.disabled = true;
            
            try {
                // Validate username/email first (igual que register.html original)
                const validateResponse = await fetch(`https://abilityseminarsgroup.com/wp-json/api/v1/validate-student?username=${encodeURIComponent(data.username)}&email=${encodeURIComponent(data.email)}`);
                const validateData = await validateResponse.json();

                if (validateData.error === 'INVALID_EMAIL') {
                    showMessage(validateData.message, 'error');
                    return;
                } else if (validateData.error === 'INVALID_USERNAME') {
                    showMessage(validateData.message, 'error');
                    return;
                }

                // Normalizar datos igual que en register.html original
                const normalizedData = {
                    username: normalize(data.username),
                    university: normalize(data.university),
                    name: normalize(data.name),
                    lastName: normalize(data.lastName),
                    email: normalize(data.email),
                    phone: normalize(data.phone),
                    age: normalize(data.age),
                    password: normalize(data.password),
                    gender: normalize(data.gender),
                    language: normalize(data.language)
                };

                // Redirigir a landing page igual que register.html original
                window.location.href = `https://abilityseminarsgroup.com/landing-page-register/?data=${btoa(JSON.stringify(normalizedData))}`;

            } catch (error) {
                console.error('Validation error:', error);
                showMessage('Connection error. Please try again.', 'error');
            } finally {
                submitBtn.textContent = originalText;
                submitBtn.disabled = false;
            }
        });
        
        // Show message function
        function showMessage(message, type) {
            const existingMessage = document.querySelector('.register-message');
            if (existingMessage) {
                existingMessage.remove();
            }

            const messageDiv = document.createElement('div');
            messageDiv.className = `register-message ${type}`;
            messageDiv.textContent = message;

            const form = document.getElementById('registerForm');
            form.parentNode.insertBefore(messageDiv, form);

            if (type === 'success') {
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, 3000);
            }
        }

        // Show terms and conditions modal
        function showTerms() {
            const popup = document.getElementById('popup');
            const popupContent = popup.querySelector('.popup__content');

            popupContent.innerHTML = `
                <h4>Terms & Conditions</h4>

                <h4>1. General Information</h4>
                <p>These terms and conditions ("Terms") govern your participation in the webinar ("Webinar") organized by Ability Seminars Group ("Organizer"). By registering for and/or attending the Webinar, you agree to comply with these Terms.</p>

                <h4>2. Registration</h4>
                <p>2.1 All participants must register for the Webinar through the designated registration platform.</p>
                <p>2.2 Accurate and complete information must be provided during the registration process. The Organizer reserves the right to deny access if false or misleading information is provided.</p>

                <h4>3. Participation and Access</h4>
                <p>3.1 Participation in the Webinar is subject to the payment of any applicable fees, if required. Payment details will be provided during the registration process.</p>
                <p>3.2 Webinar access is non-transferable. Sharing login credentials with others is prohibited.</p>
                <p>3.3 The Organizer reserves the right to restrict or terminate access to the Webinar for participants who violate these Terms.</p>

                <h4>4. Intellectual Property</h4>
                <p>4.1 All content presented during the Webinar, including but not limited to slides, videos, and handouts, is the intellectual property of the Organizer or its licensors.</p>
                <p>4.2 Participants may not record, reproduce, distribute, or share Webinar content without prior written consent from the Organizer.</p>

                <h4>5. Code of Conduct</h4>
                <p>5.1 Participants are expected to behave professionally and respectfully towards others during the Webinar.</p>
                <p>5.2 Disruptive, abusive, or discriminatory behavior will not be tolerated and may result in removal from the Webinar.</p>

                <h4>6. Technical Requirements</h4>
                <p>6.1 Participants are responsible for ensuring they have the necessary technology and internet connectivity to access the Webinar.</p>
                <p>6.2 The Organizer is not liable for technical issues experienced by participants.</p>

                <h4>7. Privacy and Data Protection</h4>
                <p>7.1 The Organizer will collect and process personal data in accordance with its Privacy Policy.</p>
                <p>7.2 By registering for the Webinar, participants consent to the Organizer using their contact information for communication related to the Webinar and future events.</p>

                <h4>8. Limitation of Liability</h4>
                <p>8.1 The Organizer is not liable for any loss or damage arising from participation in the Webinar, except as required by law.</p>
                <p>8.2 The Webinar is provided "as is," and no guarantees are made regarding its content, quality, or outcomes.</p>

                <p><strong>By registering for and attending the Webinar, you acknowledge that you have read, understood, and agree to these Terms and Conditions.</strong></p>
            `;

            popup.classList.add('open-popup');
        }

        // Close modal
        function closePopup() {
            const popup = document.getElementById('popup');
            popup.classList.remove('open-popup');
        }

        // Close modal when clicking background
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('popup__background')) {
                closePopup();
            }
        });

        // Normalize function for data processing
        const normalize = (value) => {
            if (!value) return '';
            const decomposed = value.normalize('NFD');
            return decomposed.replace(/[\u0300-\u036f]/g, '');
        }
    </script>
</body>
</html>
