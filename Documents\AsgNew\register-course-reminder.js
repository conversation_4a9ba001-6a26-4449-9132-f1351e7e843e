/**
 * ========================================
 * ASG REGISTER COURSE REMINDER - register.html
 * ========================================
 * 
 * JavaScript para manejar el recordatorio de curso en register.html
 * Detecta parámetros de curso y muestra recordatorio visual
 * Guarda información para redirect post-registro
 * 
 * Uso: Incluir al final de register.html
 * Detecta automáticamente parámetros: ?redirect_course=course_1&price=99
 */

// Detectar parámetros de curso al cargar
document.addEventListener('DOMContentLoaded', function() {
    console.log('ASG: Inicializando registro con recordatorio de curso');
    
    // Obtener parámetros de URL
    const urlParams = new URLSearchParams(window.location.search);
    const redirectCourse = urlParams.get('redirect_course');
    const price = urlParams.get('price');
    
    if (redirectCourse && price) {
        console.log('ASG: Curso detectado en registro:', { redirectCourse, price });
        
        // Mostrar recordatorio visual
        showCourseReminder(redirectCourse, price);
        
        // Guardar en localStorage para después del registro
        savePendingEnrollment(redirectCourse, price);
        
        // Modificar formulario para incluir datos del curso
        enhanceRegistrationForm(redirectCourse, price);
    }
});

// Mostrar recordatorio visual del curso
function showCourseReminder(courseCode, price) {
    const reminder = document.createElement('div');
    reminder.className = 'course-reminder-banner';
    reminder.innerHTML = `
        <div class="reminder-content">
            <div class="reminder-header">
                <h3>🎓 ¡Estás a un paso de inscribirte!</h3>
                <p>Completa tu registro para continuar con la compra del curso</p>
            </div>
            
            <div class="course-details">
                <div class="course-info">
                    <h4>📚 Curso Seleccionado:</h4>
                    <div class="course-name">${formatCourseName(courseCode)}</div>
                    <div class="course-price">$${price} USD</div>
                </div>
                
                <div class="benefits">
                    <h5>Lo que obtienes:</h5>
                    <ul>
                        <li>✅ Acceso de por vida</li>
                        <li>✅ Certificado de finalización</li>
                        <li>✅ Soporte técnico incluido</li>
                        <li>✅ Actualizaciones gratuitas</li>
                    </ul>
                </div>
            </div>
            
            <div class="reminder-footer">
                <p><strong>Después del registro serás redirigido automáticamente al pago</strong></p>
                <div class="security-badges">
                    <span class="badge">🔒 Pago Seguro</span>
                    <span class="badge">💳 PayPal</span>
                    <span class="badge">⚡ Acceso Inmediato</span>
                </div>
            </div>
        </div>
    `;
    
    // Insertar al inicio del formulario de registro
    const form = document.querySelector('.registration-form, .register-form, form');
    if (form) {
        form.insertBefore(reminder, form.firstChild);
    } else {
        // Si no encuentra el formulario, insertar al inicio del body
        document.body.insertBefore(reminder, document.body.firstChild);
    }
}

// Formatear nombre del curso
function formatCourseName(courseCode) {
    // Convertir course_1 a "Curso 1", etc.
    const courseMap = {
        'course_1': 'Curso Básico',
        'course_2': 'Curso Intermedio', 
        'course_3': 'Curso Avanzado',
        'course_4': 'Curso Especializado'
    };
    
    return courseMap[courseCode] || courseCode.replace('_', ' ').toUpperCase();
}

// Guardar enrollment pendiente
function savePendingEnrollment(courseCode, price) {
    const enrollmentData = {
        course: courseCode,
        price: parseFloat(price),
        timestamp: Date.now(),
        source: 'register_page'
    };
    
    localStorage.setItem('pending_enrollment', JSON.stringify(enrollmentData));
    console.log('ASG: Enrollment pendiente guardado:', enrollmentData);
}

// Mejorar formulario de registro
function enhanceRegistrationForm(courseCode, price) {
    const form = document.querySelector('.registration-form, .register-form, form');
    if (!form) return;
    
    // Agregar campos ocultos para tracking
    const hiddenFields = `
        <input type="hidden" name="redirect_course" value="${courseCode}">
        <input type="hidden" name="course_price" value="${price}">
        <input type="hidden" name="enrollment_source" value="course_registration">
    `;
    
    form.insertAdjacentHTML('beforeend', hiddenFields);
    
    // Modificar texto del botón de submit
    const submitButton = form.querySelector('input[type="submit"], button[type="submit"], .submit-btn');
    if (submitButton) {
        const originalText = submitButton.textContent || submitButton.value;
        submitButton.textContent = `${originalText} y Continuar al Pago`;
        submitButton.classList.add('enhanced-submit');
    }
    
    // Agregar event listener para el submit
    form.addEventListener('submit', handleEnhancedSubmit);
}

// Manejar submit mejorado
function handleEnhancedSubmit(event) {
    console.log('ASG: Formulario de registro enviado con curso pendiente');
    
    // Mostrar loading en el botón
    const submitButton = event.target.querySelector('input[type="submit"], button[type="submit"], .submit-btn');
    if (submitButton) {
        const originalText = submitButton.textContent || submitButton.value;
        submitButton.textContent = 'Creando cuenta...';
        submitButton.disabled = true;
        
        // Restaurar después de 10 segundos (por si hay error)
        setTimeout(() => {
            submitButton.textContent = originalText;
            submitButton.disabled = false;
        }, 10000);
    }
    
    // Agregar clase visual de procesamiento
    event.target.classList.add('processing');
}

// Manejar post-registro (si la página de confirmación carga)
function handlePostRegistration() {
    const pendingEnrollment = localStorage.getItem('pending_enrollment');
    
    if (pendingEnrollment) {
        const enrollment = JSON.parse(pendingEnrollment);
        console.log('ASG: Procesando post-registro con enrollment:', enrollment);
        
        // Mostrar mensaje de éxito y countdown
        showPostRegistrationMessage(enrollment);
        
        // Limpiar localStorage
        localStorage.removeItem('pending_enrollment');
        
        // Redirect automático después de 3 segundos
        setTimeout(() => {
            const paymentUrl = `/payment/?course=${enrollment.course}&price=${enrollment.price}&new_user=1`;
            console.log('ASG: Redirigiendo a pago:', paymentUrl);
            window.location.href = paymentUrl;
        }, 3000);
    }
}

// Mostrar mensaje post-registro
function showPostRegistrationMessage(enrollment) {
    const message = document.createElement('div');
    message.className = 'post-registration-success';
    message.innerHTML = `
        <div class="success-content">
            <div class="success-header">
                <h2>🎉 ¡Registro Exitoso!</h2>
                <p>Tu cuenta ha sido creada correctamente</p>
            </div>
            
            <div class="next-step">
                <h3>Siguiente paso: Completar tu inscripción</h3>
                <div class="course-summary">
                    <div class="course-name">${formatCourseName(enrollment.course)}</div>
                    <div class="course-price">$${enrollment.price} USD</div>
                </div>
            </div>
            
            <div class="redirect-info">
                <div class="countdown-container">
                    <p>Redirigiendo automáticamente en:</p>
                    <div class="countdown" id="countdown">3</div>
                    <p>segundos</p>
                </div>
                
                <div class="manual-redirect">
                    <a href="/payment/?course=${enrollment.course}&price=${enrollment.price}&new_user=1" 
                       class="btn btn-primary btn-large">
                        Continuar al Pago Ahora
                    </a>
                </div>
            </div>
        </div>
    `;
    
    // Reemplazar contenido de la página
    document.body.innerHTML = '';
    document.body.appendChild(message);
    
    // Iniciar countdown
    startCountdown();
}

// Iniciar countdown visual
function startCountdown() {
    let seconds = 3;
    const countdownEl = document.getElementById('countdown');
    
    const interval = setInterval(() => {
        seconds--;
        if (countdownEl) {
            countdownEl.textContent = seconds;
            
            // Efecto visual
            countdownEl.style.transform = 'scale(1.2)';
            setTimeout(() => {
                countdownEl.style.transform = 'scale(1)';
            }, 200);
        }
        
        if (seconds <= 0) {
            clearInterval(interval);
        }
    }, 1000);
}

// Detectar si estamos en página de confirmación de registro
function detectRegistrationSuccess() {
    // Buscar indicadores de registro exitoso
    const successIndicators = [
        'registration-success',
        'account-created',
        'welcome',
        'confirm',
        'success'
    ];
    
    const pageContent = document.body.textContent.toLowerCase();
    const pageClasses = document.body.className.toLowerCase();
    const pageUrl = window.location.href.toLowerCase();
    
    return successIndicators.some(indicator => 
        pageContent.includes(indicator) || 
        pageClasses.includes(indicator) || 
        pageUrl.includes(indicator)
    );
}

// Inicialización automática
document.addEventListener('DOMContentLoaded', function() {
    // Si detectamos que es una página de éxito de registro
    if (detectRegistrationSuccess()) {
        console.log('ASG: Página de éxito de registro detectada');
        handlePostRegistration();
    }
});

// Función global para forzar redirect (llamada desde HTML si es necesario)
window.continueToPayment = function() {
    const pendingEnrollment = localStorage.getItem('pending_enrollment');
    if (pendingEnrollment) {
        const enrollment = JSON.parse(pendingEnrollment);
        const paymentUrl = `/payment/?course=${enrollment.course}&price=${enrollment.price}&new_user=1`;
        window.location.href = paymentUrl;
    }
};

console.log('ASG Register Course Reminder initialized');

// Inyectar estilos CSS para el registro
function injectRegisterStyles() {
    if (document.getElementById('asg-register-styles')) return;

    const styles = document.createElement('style');
    styles.id = 'asg-register-styles';
    styles.textContent = `
        /* ASG Register Course Reminder Styles */
        .course-reminder-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 30px;
            box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
            animation: slideInDown 0.6s ease-out;
        }

        @keyframes slideInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .reminder-content {
            text-align: center;
        }

        .reminder-header h3 {
            margin: 0 0 10px 0;
            font-size: 26px;
            font-weight: 700;
        }

        .reminder-header p {
            margin: 0 0 25px 0;
            font-size: 16px;
            opacity: 0.9;
        }

        .course-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 25px;
            margin: 25px 0;
            text-align: left;
        }

        .course-info {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .course-info h4 {
            margin: 0 0 15px 0;
            font-size: 16px;
            opacity: 0.9;
        }

        .course-name {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 8px;
        }

        .course-price {
            font-size: 24px;
            font-weight: 700;
            color: #ffd700;
        }

        .benefits {
            background: rgba(255, 255, 255, 0.1);
            padding: 20px;
            border-radius: 10px;
            backdrop-filter: blur(10px);
        }

        .benefits h5 {
            margin: 0 0 15px 0;
            font-size: 16px;
            opacity: 0.9;
        }

        .benefits ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .benefits li {
            padding: 5px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .reminder-footer {
            margin-top: 25px;
            text-align: center;
        }

        .reminder-footer p {
            margin: 0 0 15px 0;
            font-size: 14px;
            opacity: 0.9;
        }

        .security-badges {
            display: flex;
            justify-content: center;
            gap: 15px;
            flex-wrap: wrap;
        }

        .badge {
            background: rgba(255, 255, 255, 0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 500;
            backdrop-filter: blur(10px);
        }

        /* Enhanced Submit Button */
        .enhanced-submit {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white !important;
            border: none !important;
            padding: 15px 30px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            border-radius: 8px !important;
            cursor: pointer !important;
            transition: all 0.3s ease !important;
            box-shadow: 0 4px 15px rgba(40, 167, 69, 0.3) !important;
        }

        .enhanced-submit:hover {
            transform: translateY(-2px) !important;
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.4) !important;
        }

        .enhanced-submit:disabled {
            opacity: 0.7 !important;
            cursor: not-allowed !important;
            transform: none !important;
        }

        /* Processing Form */
        .processing {
            opacity: 0.8;
            pointer-events: none;
        }

        /* Post Registration Success */
        .post-registration-success {
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            padding: 20px;
        }

        .success-content {
            background: white;
            padding: 40px;
            border-radius: 20px;
            text-align: center;
            max-width: 500px;
            width: 100%;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            animation: successSlideIn 0.8s ease-out;
        }

        @keyframes successSlideIn {
            from {
                opacity: 0;
                transform: translateY(50px) scale(0.9);
            }
            to {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .success-header h2 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 32px;
        }

        .success-header p {
            margin: 0 0 30px 0;
            color: #666;
            font-size: 16px;
        }

        .next-step h3 {
            margin: 0 0 20px 0;
            color: #333;
            font-size: 20px;
        }

        .course-summary {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            border-left: 4px solid #667eea;
        }

        .course-summary .course-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .course-summary .course-price {
            font-size: 24px;
            font-weight: 700;
            color: #667eea;
        }

        .redirect-info {
            margin-top: 30px;
        }

        .countdown-container {
            margin-bottom: 25px;
        }

        .countdown-container p {
            margin: 5px 0;
            color: #666;
            font-size: 16px;
        }

        .countdown {
            display: inline-block;
            background: #667eea;
            color: white;
            width: 60px;
            height: 60px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: 700;
            margin: 10px;
            transition: transform 0.2s ease;
        }

        .btn {
            display: inline-block;
            padding: 15px 30px;
            border-radius: 8px;
            text-decoration: none;
            font-weight: 600;
            text-align: center;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
            font-size: 16px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-large {
            padding: 18px 40px;
            font-size: 18px;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .course-details {
                grid-template-columns: 1fr;
                gap: 15px;
            }

            .course-reminder-banner {
                margin: 10px;
                padding: 20px;
            }

            .reminder-header h3 {
                font-size: 22px;
            }

            .security-badges {
                justify-content: center;
            }

            .success-content {
                margin: 10px;
                padding: 30px 20px;
            }

            .success-header h2 {
                font-size: 26px;
            }

            .countdown {
                width: 50px;
                height: 50px;
                font-size: 20px;
            }
        }
    `;

    document.head.appendChild(styles);
}

// Inyectar estilos al cargar
document.addEventListener('DOMContentLoaded', function() {
    injectRegisterStyles();
});
