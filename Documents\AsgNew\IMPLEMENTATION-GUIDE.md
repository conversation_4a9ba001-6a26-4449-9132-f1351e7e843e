# 🚀 ASG Student Flow - Guía de Implementación

## 📋 **ARCHIVOS CREADOS**

### **✅ Backend (PHP)**
- `asg-student-endpoints.php` - 4 endpoints REST API principales
- `payment-page.php` - Página de pago con PayPal integrado

### **✅ Frontend (JavaScript)**
- `vistapreliminar-enrollment-button.js` - Botón de inscripción + modal
- `lessons-enrollment-check.js` - Verificación de acceso + tracking
- `register-course-reminder.js` - Recordatorio de curso en registro

### **✅ Base de Datos**
- `database/data.sql` - 3 tablas del sistema de estudiantes

## 🔧 **PASOS DE IMPLEMENTACIÓN**

### **PASO 1: Base de Datos**
```sql
-- Ejecutar en phpMyAdmin
SOURCE Documents/AsgNew/database/data.sql;

-- Verificar que se crearon las 3 tablas:
SHOW TABLES LIKE 'wpic_asg_%';
```

### **PASO 2: Subir Archivos Backend**
```bash
# Subir a la raíz del proyecto WordPress
- asg-student-endpoints.php
- payment-page.php
```

### **PASO 3: Configurar PayPal**
En `payment-page.php`, línea 302:
```javascript
// Cambiar YOUR_PAYPAL_CLIENT_ID por tu Client ID real
<script src="https://www.paypal.com/sdk/js?client-id=TU_PAYPAL_CLIENT_ID&currency=USD"></script>
```

### **PASO 4: Crear Página de Pago en WordPress**
1. Ir a **Páginas → Agregar nueva**
2. Título: "Pago"
3. Slug: "payment"
4. Contenido: `[asg_payment_page]`
5. Publicar

### **PASO 5: Modificar Archivos Existentes**

#### **A. vistapreliminar.php**
Agregar al final del archivo (antes del `</body>`):
```php
<script>
// Configuración del curso
window.COURSE_CODE = '<?php echo $course_code; ?>'; // Reemplazar con variable real
window.COURSE_PRICE = <?php echo $course_price; ?>; // Reemplazar con variable real
</script>

<?php
// Incluir JavaScript inline
$js_content = file_get_contents(__DIR__ . '/vistapreliminar-enrollment-button.js');
echo '<script>' . $js_content . '</script>';
?>
```

#### **B. lessons.php**
Agregar al final del archivo (antes del `</body>`):
```php
<script>
// Configuración del curso
window.COURSE_CODE = '<?php echo $course_code; ?>'; // Reemplazar con variable real
</script>

<?php
// Incluir JavaScript inline
$js_content = file_get_contents(__DIR__ . '/lessons-enrollment-check.js');
echo '<script>' . $js_content . '</script>';
?>
```

#### **C. register.html**
Agregar al final del archivo (antes del `</body>`):
```html
<script>
// Incluir JavaScript inline
<?php
$js_content = file_get_contents(__DIR__ . '/register-course-reminder.js');
echo $js_content;
?>
</script>
```

### **PASO 6: Modificar Botones de Inscripción**
En `vistapreliminar.php`, cambiar botones existentes:
```html
<!-- ANTES -->
<button onclick="inscribirse()">Inscribirse $99</button>

<!-- DESPUÉS -->
<button onclick="startEnrollment()">Inscribirse $99</button>
```

## 🧪 **TESTING**

### **Test 1: Endpoints API**
```bash
# Verificar que los endpoints respondan
curl https://tudominio.com/wp-json/asg/v1/check-enrollment?course=course_1
```

### **Test 2: Flujo Completo**
1. **Visitante no registrado:**
   - Ir a `vistapreliminar.php?course=course_1`
   - Clic "Inscribirse" → Modal aparece
   - Clic "Crear Cuenta" → Registro con recordatorio
   - Completar registro → Redirect automático a pago

2. **Usuario registrado:**
   - Login en WordPress
   - Ir a `vistapreliminar.php?course=course_1`
   - Clic "Inscribirse" → Redirect directo a pago

3. **Proceso de pago:**
   - Página de pago carga correctamente
   - PayPal funciona (usar sandbox)
   - Post-pago redirect a lecciones

4. **Acceso a lecciones:**
   - `lessons.php?course=course_1` verifica acceso
   - Contenido se muestra solo si está inscrito
   - Botones de completar lección funcionan

## ⚙️ **CONFIGURACIONES IMPORTANTES**

### **PayPal Sandbox (Testing)**
```javascript
// En payment-page.php para testing
client-id=AZDxjDScFpQtjWTOUtWKbyN_bDt4OgqaF4eYXlewfBP4-8aqX3PiV8e1GWU6liB2CU21rbWUVscg9RqE
```

### **PayPal Producción**
```javascript
// En payment-page.php para producción
client-id=TU_CLIENT_ID_REAL_DE_PAYPAL
```

### **URLs Importantes**
- Pago: `/payment/?course=course_1&price=99`
- API: `/wp-json/asg/v1/`
- Registro: `/register/?redirect_course=course_1&price=99`

## 🔍 **DEBUGGING**

### **Logs de JavaScript**
Abrir DevTools → Console para ver:
```
ASG: Iniciando proceso de inscripción
ASG: Usuario no logueado, mostrando modal
ASG: Verificando acceso a lecciones
```

### **Logs de PHP**
Revisar error_log de WordPress para:
```
ASG: Enrollment exitoso - Usuario: john (42), Curso: course_1
ASG: Error creando estudiante: [error details]
```

### **Base de Datos**
```sql
-- Verificar estudiantes
SELECT * FROM wpic_asg_students;

-- Verificar enrollments
SELECT * FROM wpic_asg_enrollments;

-- Verificar progreso
SELECT * FROM wpic_asg_progress;
```

## 🚨 **PROBLEMAS COMUNES**

### **Error: Endpoints no funcionan**
- Verificar que `asg-student-endpoints.php` esté en la raíz
- Verificar permalinks de WordPress
- Revisar logs de error

### **Error: PayPal no carga**
- Verificar Client ID en `payment-page.php`
- Verificar conexión a internet
- Usar sandbox para testing

### **Error: Modal no aparece**
- Verificar que JavaScript se incluye correctamente
- Verificar consola de errores
- Verificar que `COURSE_CODE` está definido

### **Error: Acceso denegado en lecciones**
- Verificar que el usuario está inscrito
- Verificar que el curso existe
- Revisar logs de API

## 📞 **SOPORTE**

Si encuentras problemas:
1. Revisar logs de JavaScript (DevTools)
2. Revisar logs de PHP (error_log)
3. Verificar base de datos
4. Contactar al equipo de desarrollo

---

## 🎯 **PRÓXIMOS PASOS**

Una vez implementado y probado:
1. Configurar PayPal en producción
2. Configurar webhooks PayPal (opcional)
3. Implementar analytics/tracking
4. Crear dashboard de administración
5. Optimizar performance

**¡El sistema está listo para producción!** 🚀
