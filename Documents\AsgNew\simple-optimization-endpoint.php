<?php
/**
 * ENDPOINT SIMPLE PARA OPTIMIZACIÓN DE PROGRESO
 * Solo lo esencial para mejorar performance
 */

// Registrar endpoint de optimización simple
add_action('rest_api_init', function() {
    register_rest_route('asg/v1', '/optimize-progress-simple', array(
        'methods' => 'POST',
        'callback' => 'asg_optimize_progress_simple',
        'permission_callback' => function() {
            return current_user_can('manage_options'); // Solo administradores
        }
    ));
    
    register_rest_route('asg/v1', '/progress-stats', array(
        'methods' => 'GET',
        'callback' => 'asg_get_progress_stats',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        }
    ));
});

/**
 * Ejecutar optimización simple en un solo paso
 */
function asg_optimize_progress_simple($request) {
    global $wpdb;
    
    try {
        $results = [];
        
        // PASO 1: <PERSON><PERSON>r índices básicos
        $indexes = [
            "CREATE INDEX IF NOT EXISTS idx_student_lesson ON wpic_asg_progress (student_id, lesson_id)",
            "CREATE INDEX IF NOT EXISTS idx_student_completed ON wpic_asg_progress (student_id, completed)"
        ];
        
        foreach ($indexes as $sql) {
            $result = $wpdb->query($sql);
            $results['indexes'][] = [
                'sql' => $sql,
                'success' => $result !== false
            ];
        }
        
        // PASO 2: Agregar columna course_code si no existe
        $column_exists = $wpdb->get_var("
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'wpic_asg_progress'
            AND COLUMN_NAME = 'course_code'
        ");

        $results['column_check'] = [
            'exists_before' => $column_exists > 0
        ];

        if (!$column_exists) {
            // Agregar columna course_code
            $sql = "ALTER TABLE wpic_asg_progress ADD COLUMN course_code VARCHAR(100) NULL AFTER lesson_id";
            $result = $wpdb->query($sql);

            if ($result === false) {
                throw new Exception("Error al agregar columna course_code: " . $wpdb->last_error);
            }

            $results['add_column'] = [
                'executed' => true,
                'success' => true,
                'message' => 'Columna course_code agregada exitosamente'
            ];

            // Crear índices para course_code
            $course_indexes = [
                "CREATE INDEX IF NOT EXISTS idx_student_course ON wpic_asg_progress (student_id, course_code)",
                "CREATE INDEX IF NOT EXISTS idx_student_course_completed ON wpic_asg_progress (student_id, course_code, completed)"
            ];

            $index_results = [];
            foreach ($course_indexes as $index_sql) {
                $index_result = $wpdb->query($index_sql);
                $index_results[] = [
                    'sql' => $index_sql,
                    'success' => $index_result !== false
                ];
            }
            $results['course_indexes'] = $index_results;

        } else {
            $results['add_column'] = [
                'executed' => false,
                'message' => 'Column already exists',
                'skipped' => true
            ];
        }
        
        // PASO 3: Poblar course_code en registros existentes
        $missing_before = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NULL OR course_code = ''");

        if ($missing_before > 0) {
            $sql = "
                UPDATE wpic_asg_progress p
                JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
                JOIN wpic_modules m ON l.code_module = m.code_module
                SET p.course_code = m.code_course,
                    p.updated_at = CURRENT_TIMESTAMP
                WHERE (p.course_code IS NULL OR p.course_code = '')
            ";

            $updated = $wpdb->query($sql);
            $missing_after = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NULL OR course_code = ''");

            $results['populate_course_code'] = [
                'missing_before' => $missing_before,
                'updated' => $updated !== false ? $updated : 0,
                'missing_after' => $missing_after,
                'success' => $updated !== false,
                'error' => $updated === false ? $wpdb->last_error : null
            ];
        } else {
            $results['populate_course_code'] = [
                'message' => 'All records already have course_code',
                'skipped' => true
            ];
        }
        
        // PASO 4: Estadísticas finales
        $total_records = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress");
        $with_course_code = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NOT NULL");
        $unique_students = $wpdb->get_var("SELECT COUNT(DISTINCT student_id) FROM wpic_asg_progress");
        $unique_courses = $wpdb->get_var("SELECT COUNT(DISTINCT course_code) FROM wpic_asg_progress WHERE course_code IS NOT NULL");
        
        $results['final_stats'] = [
            'total_records' => $total_records,
            'with_course_code' => $with_course_code,
            'missing_course_code' => $total_records - $with_course_code,
            'unique_students' => $unique_students,
            'unique_courses' => $unique_courses,
            'optimization_percentage' => $total_records > 0 ? round(($with_course_code / $total_records) * 100, 1) : 0
        ];
        
        return [
            'success' => true,
            'message' => 'Optimización completada exitosamente',
            'results' => $results,
            'next_steps' => [
                'Los endpoints ya están optimizados',
                'Las consultas serán más rápidas automáticamente',
                'Los nuevos registros incluirán course_code'
            ]
        ];
        
    } catch (Exception $e) {
        return new WP_Error('optimization_error', $e->getMessage(), ['status' => 500]);
    }
}

/**
 * Obtener estadísticas de progreso
 */
function asg_get_progress_stats() {
    global $wpdb;
    
    // Estadísticas generales
    $general_stats = $wpdb->get_row("
        SELECT 
            COUNT(*) as total_records,
            COUNT(CASE WHEN course_code IS NOT NULL THEN 1 END) as with_course_code,
            COUNT(CASE WHEN course_code IS NULL THEN 1 END) as missing_course_code,
            COUNT(DISTINCT student_id) as unique_students,
            COUNT(DISTINCT course_code) as unique_courses,
            COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons,
            AVG(CASE WHEN completed = 1 THEN 1 ELSE 0 END) * 100 as avg_completion_rate
        FROM wpic_asg_progress
    ");
    
    // Estadísticas por curso
    $course_stats = $wpdb->get_results("
        SELECT 
            course_code,
            COUNT(*) as total_lessons,
            COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons,
            COUNT(DISTINCT student_id) as unique_students,
            ROUND((COUNT(CASE WHEN completed = 1 THEN 1 END) / COUNT(*)) * 100, 1) as completion_rate
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL
        GROUP BY course_code
        ORDER BY total_lessons DESC
        LIMIT 10
    ");
    
    // Estudiantes más activos
    $top_students = $wpdb->get_results("
        SELECT 
            student_id,
            COUNT(*) as total_lessons,
            COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons,
            COUNT(DISTINCT course_code) as enrolled_courses,
            ROUND((COUNT(CASE WHEN completed = 1 THEN 1 END) / COUNT(*)) * 100, 1) as completion_rate
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL
        GROUP BY student_id
        ORDER BY completed_lessons DESC
        LIMIT 10
    ");
    
    // Verificar índices
    $indexes = $wpdb->get_results("SHOW INDEX FROM wpic_asg_progress WHERE Key_name LIKE 'idx_%'");
    
    return [
        'success' => true,
        'general_stats' => $general_stats,
        'course_stats' => $course_stats,
        'top_students' => $top_students,
        'indexes' => array_map(function($index) {
            return [
                'name' => $index->Key_name,
                'column' => $index->Column_name,
                'unique' => $index->Non_unique == 0
            ];
        }, $indexes),
        'optimization_status' => [
            'course_code_column_exists' => $wpdb->get_var("
                SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
                WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'wpic_asg_progress' AND COLUMN_NAME = 'course_code'
            ") > 0,
            'indexes_created' => count($indexes) >= 2,
            'data_populated' => $general_stats->with_course_code > 0,
            'optimization_complete' => $general_stats->missing_course_code == 0
        ]
    ];
}

/**
 * Función auxiliar para probar performance (opcional)
 */
function test_query_performance() {
    global $wpdb;
    
    // Obtener datos de prueba
    $sample = $wpdb->get_row("
        SELECT student_id, course_code 
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL 
        LIMIT 1
    ");
    
    if (!$sample) {
        return ['error' => 'No hay datos para probar'];
    }
    
    // Consulta antigua (con JOINs)
    $start = microtime(true);
    $old_result = $wpdb->get_row($wpdb->prepare("
        SELECT COUNT(*) as total, SUM(p.completed) as completed
        FROM wpic_asg_progress p
        JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE p.student_id = %d AND m.code_course = %s
    ", $sample->student_id, $sample->course_code));
    $old_time = microtime(true) - $start;
    
    // Consulta nueva (optimizada)
    $start = microtime(true);
    $new_result = $wpdb->get_row($wpdb->prepare("
        SELECT COUNT(*) as total, SUM(completed) as completed
        FROM wpic_asg_progress 
        WHERE student_id = %d AND course_code = %s
    ", $sample->student_id, $sample->course_code));
    $new_time = microtime(true) - $start;
    
    return [
        'old_query_time' => round($old_time, 6),
        'new_query_time' => round($new_time, 6),
        'improvement' => $old_time > 0 ? round((($old_time - $new_time) / $old_time) * 100, 1) . '%' : 'N/A',
        'results_match' => $old_result->total == $new_result->total && $old_result->completed == $new_result->completed
    ];
}
?>
