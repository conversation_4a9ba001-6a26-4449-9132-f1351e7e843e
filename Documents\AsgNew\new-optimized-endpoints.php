<?php
/**
 * ENDPOINTS OPTIMIZADOS PARA NUEVA ESTRUCTURA
 * wpic_asg_user_progress - Máximo rendimiento
 */

// Registrar endpoints optimizados
add_action('rest_api_init', function() {
    
    // Endpoint para obtener progreso de usuario
    register_rest_route('asg/v1', '/user-progress/(?P<user_id>[0-9]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_get_user_progress_optimized',
        'permission_callback' => 'is_user_logged_in',
        'args' => array(
            'user_id' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_numeric($param);
                }
            )
        )
    ));
    
    // Endpoint para completar lección (actualizar progreso)
    register_rest_route('asg/v1', '/complete-lesson-optimized', array(
        'methods' => 'POST',
        'callback' => 'asg_complete_lesson_optimized',
        'permission_callback' => 'is_user_logged_in',
        'args' => array(
            'user_id' => array('required' => true),
            'lesson_id' => array('required' => true),
            'course_code' => array('required' => true)
        )
    ));
    
    // Endpoint para inicializar progreso de curso
    register_rest_route('asg/v1', '/initialize-course-optimized', array(
        'methods' => 'POST',
        'callback' => 'asg_initialize_course_optimized',
        'permission_callback' => 'is_user_logged_in',
        'args' => array(
            'user_id' => array('required' => true),
            'course_code' => array('required' => true)
        )
    ));
    
    // Endpoint para estadísticas de curso
    register_rest_route('asg/v1', '/course-stats/(?P<course_code>[a-zA-Z0-9_-]+)', array(
        'methods' => 'GET',
        'callback' => 'asg_get_course_stats_optimized',
        'permission_callback' => '__return_true',
        'args' => array(
            'course_code' => array('required' => true)
        )
    ));
});

/**
 * Obtener progreso completo de un usuario (SÚPER OPTIMIZADO)
 */
function asg_get_user_progress_optimized($request) {
    global $wpdb;
    $user_id = intval($request['user_id']);
    
    // Consulta súper rápida - sin JOINs, datos pre-calculados
    $user_progress = $wpdb->get_results($wpdb->prepare("
        SELECT 
            up.*,
            c.name_course,
            c.cover_img,
            c.description_course
        FROM wpic_asg_user_progress up
        LEFT JOIN wpic_courses c ON up.course_code = c.code_course
        WHERE up.user_id = %d
        ORDER BY up.progress_percentage DESC, up.updated_at DESC
    ", $user_id));
    
    if (empty($user_progress)) {
        return new WP_Error('no_progress', 'No se encontró progreso para este usuario', ['status' => 404]);
    }
    
    // Estadísticas generales del usuario
    $user_stats = $wpdb->get_row($wpdb->prepare("
        SELECT 
            COUNT(*) as total_courses,
            SUM(completed_count) as total_lessons_completed,
            SUM(total_lessons) as total_lessons_enrolled,
            AVG(progress_percentage) as average_progress,
            COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as courses_completed,
            MAX(last_completed_at) as last_activity
        FROM wpic_asg_user_progress
        WHERE user_id = %d
    ", $user_id));
    
    // Formatear datos para respuesta
    $courses = [];
    foreach ($user_progress as $course) {
        $completed_lessons = json_decode($course->completed_lessons, true) ?: [];
        
        $courses[] = [
            'course_code' => $course->course_code,
            'course_name' => $course->name_course,
            'course_image' => $course->cover_img,
            'course_description' => $course->description_course,
            'total_lessons' => intval($course->total_lessons),
            'completed_count' => intval($course->completed_count),
            'progress_percentage' => floatval($course->progress_percentage),
            'completed_lessons' => $completed_lessons,
            'last_lesson_id' => $course->last_lesson_id,
            'last_completed_at' => $course->last_completed_at,
            'created_at' => $course->created_at,
            'updated_at' => $course->updated_at
        ];
    }
    
    return [
        'success' => true,
        'user_id' => $user_id,
        'user_stats' => [
            'total_courses' => intval($user_stats->total_courses),
            'total_lessons_completed' => intval($user_stats->total_lessons_completed),
            'total_lessons_enrolled' => intval($user_stats->total_lessons_enrolled),
            'average_progress' => round(floatval($user_stats->average_progress), 2),
            'courses_completed' => intval($user_stats->courses_completed),
            'last_activity' => $user_stats->last_activity
        ],
        'courses' => $courses
    ];
}

/**
 * Completar lección (SÚPER OPTIMIZADO)
 */
function asg_complete_lesson_optimized($request) {
    global $wpdb;
    
    $user_id = intval($request['user_id']);
    $lesson_id = intval($request['lesson_id']);
    $course_code = sanitize_text_field($request['course_code']);
    
    // Verificar si existe el registro de progreso
    $existing = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_user_progress 
        WHERE user_id = %d AND course_code = %s
    ", $user_id, $course_code));
    
    if (!$existing) {
        return new WP_Error('no_progress', 'No se encontró progreso para este curso. Inicializar primero.', ['status' => 404]);
    }
    
    // Obtener lecciones completadas actuales
    $completed_lessons = json_decode($existing->completed_lessons, true) ?: [];
    
    // Verificar si la lección ya está completada
    if (in_array($lesson_id, $completed_lessons)) {
        return [
            'success' => true,
            'message' => 'Lección ya estaba completada',
            'already_completed' => true,
            'progress' => [
                'completed_count' => intval($existing->completed_count),
                'total_lessons' => intval($existing->total_lessons),
                'progress_percentage' => floatval($existing->progress_percentage)
            ]
        ];
    }
    
    // Agregar nueva lección completada
    $completed_lessons[] = $lesson_id;
    sort($completed_lessons); // Mantener ordenado
    
    // Calcular nuevos valores
    $new_completed_count = count($completed_lessons);
    $new_progress_percentage = ($new_completed_count / $existing->total_lessons) * 100;
    
    // Actualizar registro
    $result = $wpdb->update('wpic_asg_user_progress', [
        'completed_lessons' => json_encode($completed_lessons),
        'completed_count' => $new_completed_count,
        'progress_percentage' => round($new_progress_percentage, 2),
        'last_lesson_id' => $lesson_id,
        'last_completed_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ], [
        'user_id' => $user_id,
        'course_code' => $course_code
    ]);
    
    if ($result === false) {
        return new WP_Error('update_failed', 'Error al actualizar progreso: ' . $wpdb->last_error, ['status' => 500]);
    }
    
    return [
        'success' => true,
        'message' => 'Lección completada exitosamente',
        'lesson_id' => $lesson_id,
        'progress' => [
            'completed_count' => $new_completed_count,
            'total_lessons' => intval($existing->total_lessons),
            'progress_percentage' => round($new_progress_percentage, 2),
            'completed_lessons' => $completed_lessons,
            'last_lesson_id' => $lesson_id
        ]
    ];
}

/**
 * Inicializar progreso de curso (OPTIMIZADO)
 */
function asg_initialize_course_optimized($request) {
    global $wpdb;
    
    $user_id = intval($request['user_id']);
    $course_code = sanitize_text_field($request['course_code']);
    
    // Verificar si ya existe
    $existing = $wpdb->get_var($wpdb->prepare("
        SELECT id FROM wpic_asg_user_progress 
        WHERE user_id = %d AND course_code = %s
    ", $user_id, $course_code));
    
    if ($existing) {
        return [
            'success' => true,
            'message' => 'Progreso ya inicializado para este curso',
            'already_exists' => true
        ];
    }
    
    // Obtener total de lecciones del curso
    $total_lessons = $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(l.id_lesson)
        FROM wpic_lessons l
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE m.code_course = %s AND l.is_deleted = 0
    ", $course_code));
    
    if (!$total_lessons) {
        return new WP_Error('no_lessons', 'No se encontraron lecciones para este curso', ['status' => 404]);
    }
    
    // Crear registro inicial
    $result = $wpdb->insert('wpic_asg_user_progress', [
        'user_id' => $user_id,
        'course_code' => $course_code,
        'completed_lessons' => json_encode([]),
        'total_lessons' => $total_lessons,
        'completed_count' => 0,
        'progress_percentage' => 0.00,
        'last_lesson_id' => null,
        'last_completed_at' => null,
        'created_at' => current_time('mysql'),
        'updated_at' => current_time('mysql')
    ]);
    
    if ($result === false) {
        return new WP_Error('insert_failed', 'Error al inicializar progreso: ' . $wpdb->last_error, ['status' => 500]);
    }
    
    return [
        'success' => true,
        'message' => 'Progreso inicializado exitosamente',
        'user_id' => $user_id,
        'course_code' => $course_code,
        'total_lessons' => $total_lessons
    ];
}

/**
 * Obtener estadísticas de curso (SÚPER OPTIMIZADO)
 */
function asg_get_course_stats_optimized($request) {
    global $wpdb;
    $course_code = sanitize_text_field($request['course_code']);
    
    // Estadísticas generales del curso
    $course_stats = $wpdb->get_row($wpdb->prepare("
        SELECT 
            COUNT(*) as total_students,
            AVG(progress_percentage) as average_progress,
            SUM(completed_count) as total_lessons_completed,
            SUM(total_lessons) as total_lessons_assigned,
            COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as students_completed,
            COUNT(CASE WHEN progress_percentage > 0 THEN 1 END) as students_started,
            MAX(last_completed_at) as last_activity
        FROM wpic_asg_user_progress
        WHERE course_code = %s
    ", $course_code));
    
    if (!$course_stats || $course_stats->total_students == 0) {
        return new WP_Error('no_data', 'No se encontraron datos para este curso', ['status' => 404]);
    }
    
    // Distribución de progreso
    $progress_distribution = $wpdb->get_results($wpdb->prepare("
        SELECT 
            CASE 
                WHEN progress_percentage = 0 THEN '0% - Sin empezar'
                WHEN progress_percentage < 25 THEN '1-24% - Iniciado'
                WHEN progress_percentage < 50 THEN '25-49% - En progreso'
                WHEN progress_percentage < 75 THEN '50-74% - Avanzado'
                WHEN progress_percentage < 100 THEN '75-99% - Casi completo'
                ELSE '100% - Completado'
            END as progress_range,
            COUNT(*) as student_count
        FROM wpic_asg_user_progress
        WHERE course_code = %s
        GROUP BY 
            CASE 
                WHEN progress_percentage = 0 THEN '0% - Sin empezar'
                WHEN progress_percentage < 25 THEN '1-24% - Iniciado'
                WHEN progress_percentage < 50 THEN '25-49% - En progreso'
                WHEN progress_percentage < 75 THEN '50-74% - Avanzado'
                WHEN progress_percentage < 100 THEN '75-99% - Casi completo'
                ELSE '100% - Completado'
            END
        ORDER BY 
            CASE 
                WHEN progress_percentage = 0 THEN 1
                WHEN progress_percentage < 25 THEN 2
                WHEN progress_percentage < 50 THEN 3
                WHEN progress_percentage < 75 THEN 4
                WHEN progress_percentage < 100 THEN 5
                ELSE 6
            END
    ", $course_code));
    
    // Top estudiantes
    $top_students = $wpdb->get_results($wpdb->prepare("
        SELECT 
            user_id,
            progress_percentage,
            completed_count,
            total_lessons,
            last_completed_at
        FROM wpic_asg_user_progress
        WHERE course_code = %s
        ORDER BY progress_percentage DESC, completed_count DESC, last_completed_at DESC
        LIMIT 10
    ", $course_code));
    
    return [
        'success' => true,
        'course_code' => $course_code,
        'stats' => [
            'total_students' => intval($course_stats->total_students),
            'average_progress' => round(floatval($course_stats->average_progress), 2),
            'total_lessons_completed' => intval($course_stats->total_lessons_completed),
            'total_lessons_assigned' => intval($course_stats->total_lessons_assigned),
            'students_completed' => intval($course_stats->students_completed),
            'students_started' => intval($course_stats->students_started),
            'completion_rate' => round((intval($course_stats->students_completed) / intval($course_stats->total_students)) * 100, 2),
            'last_activity' => $course_stats->last_activity
        ],
        'progress_distribution' => array_map(function($item) {
            return [
                'range' => $item->progress_range,
                'count' => intval($item->student_count)
            ];
        }, $progress_distribution),
        'top_students' => array_map(function($student) {
            return [
                'user_id' => intval($student->user_id),
                'progress_percentage' => floatval($student->progress_percentage),
                'completed_count' => intval($student->completed_count),
                'total_lessons' => intval($student->total_lessons),
                'last_completed_at' => $student->last_completed_at
            ];
        }, $top_students)
    ];
}
?>
