-- =====================================================
-- VERIFICACIÓN DE OPTIMIZACIÓN wpic_asg_progress
-- Ejecutar después de la optimización para verificar resultados
-- =====================================================

-- VERIFICACIÓN 1: ESTRUCTURA DE TABLA
-- ====================================
SELECT 'VERIFICACIÓN 1: ESTRUCTURA DE TABLA' as verificacion;

-- Mostrar todas las columnas
SELECT 
    COLUMN_NAME as columna,
    DATA_TYPE as tipo,
    IS_NULLABLE as permite_null,
    COLUMN_DEFAULT as valor_default,
    CASE 
        WHEN COLUMN_NAME = 'course_code' THEN '✅ NUEVA COLUMNA OPTIMIZADA'
        ELSE 'Columna original'
    END as estado
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'wpic_asg_progress'
ORDER BY ORDINAL_POSITION;

-- VERIFICACIÓN 2: ÍNDICES CREADOS
-- ===============================
SELECT 'VERIFICACIÓN 2: ÍNDICES DE OPTIMIZACIÓN' as verificacion;

SELECT 
    Key_name as nombre_indice,
    Column_name as columna,
    CASE 
        WHEN Key_name LIKE 'idx_%' THEN '✅ ÍNDICE DE OPTIMIZACIÓN'
        ELSE 'Índice original'
    END as tipo,
    CASE WHEN Non_unique = 0 THEN 'UNIQUE' ELSE 'INDEX' END as categoria
FROM INFORMATION_SCHEMA.STATISTICS 
WHERE TABLE_SCHEMA = DATABASE() 
AND TABLE_NAME = 'wpic_asg_progress'
ORDER BY Key_name, Seq_in_index;

-- VERIFICACIÓN 3: DATOS POBLADOS
-- ==============================
SELECT 'VERIFICACIÓN 3: ESTADO DE LOS DATOS' as verificacion;

SELECT 
    COUNT(*) as total_registros,
    COUNT(CASE WHEN course_code IS NOT NULL AND course_code != '' THEN 1 END) as con_course_code,
    COUNT(CASE WHEN course_code IS NULL OR course_code = '' THEN 1 END) as sin_course_code,
    COUNT(DISTINCT student_id) as estudiantes_unicos,
    COUNT(DISTINCT course_code) as cursos_unicos,
    ROUND((COUNT(CASE WHEN course_code IS NOT NULL AND course_code != '' THEN 1 END) / COUNT(*)) * 100, 2) as porcentaje_optimizado
FROM wpic_asg_progress;

-- VERIFICACIÓN 4: DISTRIBUCIÓN POR CURSO
-- ======================================
SELECT 'VERIFICACIÓN 4: DISTRIBUCIÓN POR CURSO' as verificacion;

SELECT 
    course_code,
    COUNT(*) as total_lecciones,
    COUNT(CASE WHEN completed = 1 THEN 1 END) as lecciones_completadas,
    COUNT(DISTINCT student_id) as estudiantes_unicos,
    ROUND((COUNT(CASE WHEN completed = 1 THEN 1 END) / COUNT(*)) * 100, 1) as tasa_completado,
    MIN(created_at) as primer_registro,
    MAX(updated_at) as ultimo_update
FROM wpic_asg_progress 
WHERE course_code IS NOT NULL AND course_code != ''
GROUP BY course_code
ORDER BY total_lecciones DESC;

-- VERIFICACIÓN 5: REGISTROS PROBLEMÁTICOS
-- =======================================
SELECT 'VERIFICACIÓN 5: REGISTROS SIN course_code (PROBLEMÁTICOS)' as verificacion;

SELECT 
    p.id_progress,
    p.student_id,
    p.lesson_id,
    l.title_lesson,
    l.code_module,
    m.code_course,
    CASE 
        WHEN l.id_lesson IS NULL THEN '❌ LECCIÓN NO EXISTE'
        WHEN m.code_module IS NULL THEN '❌ MÓDULO NO EXISTE'
        WHEN m.code_course IS NULL THEN '❌ CURSO NO EXISTE'
        ELSE '✅ DATOS VÁLIDOS'
    END as problema
FROM wpic_asg_progress p
LEFT JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
LEFT JOIN wpic_modules m ON l.code_module = m.code_module
WHERE (p.course_code IS NULL OR p.course_code = '')
LIMIT 10;

-- VERIFICACIÓN 6: PRUEBA DE PERFORMANCE
-- =====================================
SELECT 'VERIFICACIÓN 6: PREPARACIÓN PARA PRUEBA DE PERFORMANCE' as verificacion;

-- Obtener datos de muestra para prueba
SELECT 
    student_id,
    course_code,
    COUNT(*) as total_lecciones,
    SUM(completed) as lecciones_completadas
FROM wpic_asg_progress 
WHERE course_code IS NOT NULL AND course_code != ''
GROUP BY student_id, course_code
HAVING COUNT(*) >= 3
ORDER BY total_lecciones DESC
LIMIT 5;

-- VERIFICACIÓN 7: CONSULTAS OPTIMIZADAS VS ANTIGUAS
-- =================================================
SELECT 'VERIFICACIÓN 7: EJEMPLO DE CONSULTAS OPTIMIZADAS' as verificacion;

-- EJEMPLO: Reemplazar 1 y 'course_ejemplo' con datos reales
/*
-- CONSULTA ANTIGUA (LENTA - con JOINs):
SELECT 
    'CONSULTA ANTIGUA (LENTA)' as tipo,
    COUNT(*) as total_lecciones,
    SUM(p.completed) as completadas,
    ROUND((SUM(p.completed) / COUNT(*)) * 100, 1) as porcentaje
FROM wpic_asg_progress p
JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
JOIN wpic_modules m ON l.code_module = m.code_module
WHERE p.student_id = 1 AND m.code_course = 'course_ejemplo';

-- CONSULTA NUEVA (RÁPIDA - sin JOINs):
SELECT 
    'CONSULTA NUEVA (RÁPIDA)' as tipo,
    COUNT(*) as total_lecciones,
    SUM(completed) as completadas,
    ROUND((SUM(completed) / COUNT(*)) * 100, 1) as porcentaje
FROM wpic_asg_progress 
WHERE student_id = 1 AND course_code = 'course_ejemplo';
*/

-- VERIFICACIÓN 8: ESTADÍSTICAS DE RENDIMIENTO
-- ===========================================
SELECT 'VERIFICACIÓN 8: ESTADÍSTICAS DE RENDIMIENTO' as verificacion;

-- Contar registros por estudiante (para evaluar carga)
SELECT 
    'DISTRIBUCIÓN DE CARGA' as categoria,
    CASE 
        WHEN COUNT(*) <= 10 THEN '1-10 lecciones'
        WHEN COUNT(*) <= 50 THEN '11-50 lecciones'
        WHEN COUNT(*) <= 100 THEN '51-100 lecciones'
        ELSE '100+ lecciones'
    END as rango_lecciones,
    COUNT(DISTINCT student_id) as cantidad_estudiantes
FROM wpic_asg_progress
WHERE course_code IS NOT NULL
GROUP BY student_id
ORDER BY rango_lecciones;

-- VERIFICACIÓN 9: INTEGRIDAD DE DATOS
-- ===================================
SELECT 'VERIFICACIÓN 9: INTEGRIDAD DE DATOS' as verificacion;

-- Verificar consistencia de course_code
SELECT 
    l.id_lesson,
    l.title_lesson,
    m.code_course as course_code_real,
    p.course_code as course_code_tabla,
    CASE 
        WHEN m.code_course = p.course_code THEN '✅ CONSISTENTE'
        ELSE '❌ INCONSISTENTE'
    END as estado
FROM wpic_asg_progress p
JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
JOIN wpic_modules m ON l.code_module = m.code_module
WHERE p.course_code IS NOT NULL AND p.course_code != ''
AND m.code_course != p.course_code
LIMIT 10;

-- VERIFICACIÓN 10: RESUMEN FINAL
-- ==============================
SELECT 'VERIFICACIÓN 10: RESUMEN FINAL DE OPTIMIZACIÓN' as verificacion;

SELECT 
    'RESUMEN FINAL' as categoria,
    CASE 
        WHEN (
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'wpic_asg_progress' AND COLUMN_NAME = 'course_code'
        ) > 0 THEN '✅ COLUMNA course_code EXISTE'
        ELSE '❌ COLUMNA course_code NO EXISTE'
    END as estructura,
    
    CASE 
        WHEN (
            SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS 
            WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'wpic_asg_progress' AND Key_name LIKE 'idx_%'
        ) >= 4 THEN '✅ ÍNDICES OPTIMIZADOS CREADOS'
        ELSE '⚠️ FALTAN ÍNDICES DE OPTIMIZACIÓN'
    END as indices,
    
    CASE 
        WHEN (
            SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NULL OR course_code = ''
        ) = 0 THEN '✅ TODOS LOS DATOS POBLADOS'
        WHEN (
            SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NOT NULL AND course_code != ''
        ) > 0 THEN '⚠️ DATOS PARCIALMENTE POBLADOS'
        ELSE '❌ DATOS NO POBLADOS'
    END as datos,
    
    CONCAT(
        ROUND((
            SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NOT NULL AND course_code != ''
        ) / (
            SELECT COUNT(*) FROM wpic_asg_progress
        ) * 100, 1), '%'
    ) as porcentaje_optimizado;

-- INSTRUCCIONES FINALES
-- =====================
SELECT 
    'INSTRUCCIONES FINALES' as seccion,
    'Si todas las verificaciones muestran ✅, la optimización fue exitosa' as paso_1,
    'Los endpoints automáticamente usarán las consultas optimizadas' as paso_2,
    'Performance esperado: 80% más rápido en consultas de progreso' as paso_3,
    'Nuevos registros se crearán automáticamente con course_code' as paso_4;

-- CONSULTAS DE MANTENIMIENTO (OPCIONAL)
-- =====================================
SELECT 'CONSULTAS DE MANTENIMIENTO FUTURO' as seccion;

/*
-- Para poblar course_code en registros futuros que puedan faltar:
UPDATE wpic_asg_progress p
JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
JOIN wpic_modules m ON l.code_module = m.code_module
SET p.course_code = m.code_course,
    p.updated_at = CURRENT_TIMESTAMP
WHERE (p.course_code IS NULL OR p.course_code = '');

-- Para verificar performance periódicamente:
SELECT 
    course_code,
    COUNT(*) as registros,
    COUNT(DISTINCT student_id) as estudiantes,
    AVG(completed) * 100 as tasa_completado_promedio
FROM wpic_asg_progress 
WHERE course_code IS NOT NULL
GROUP BY course_code
ORDER BY registros DESC;
*/
