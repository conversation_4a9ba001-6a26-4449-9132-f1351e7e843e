<?php
/**
 * ENDPOINT PARA OPTIMIZACIÓN DE TABLA DE PROGRESO
 * Ejecuta la optimización paso a paso de forma segura
 */

// Registrar endpoints de optimización
add_action('rest_api_init', function() {
    // Endpoint para ejecutar optimización paso a paso
    register_rest_route('asg/v1', '/optimize-progress/(?P<step>[0-9]+)', array(
        'methods' => 'POST',
        'callback' => 'asg_optimize_progress_step',
        'permission_callback' => function() {
            return current_user_can('manage_options'); // Solo administradores
        },
        'args' => array(
            'step' => array(
                'required' => true,
                'validate_callback' => function($param) {
                    return is_numeric($param) && $param >= 1 && $param <= 8;
                }
            )
        )
    ));
    
    // Endpoint para verificar estado de optimización
    register_rest_route('asg/v1', '/optimize-progress/status', array(
        'methods' => 'GET',
        'callback' => 'asg_optimization_status',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        }
    ));
    
    // Endpoint para probar performance
    register_rest_route('asg/v1', '/optimize-progress/test', array(
        'methods' => 'GET',
        'callback' => 'asg_test_progress_performance',
        'permission_callback' => function() {
            return current_user_can('manage_options');
        }
    ));
});

/**
 * Ejecutar paso específico de optimización
 */
function asg_optimize_progress_step($request) {
    global $wpdb;
    $step = intval($request['step']);
    
    try {
        switch ($step) {
            case 1:
                return execute_step_1_indexes();
            case 2:
                return execute_step_2_add_column();
            case 3:
                return execute_step_3_populate_course_code();
            case 4:
                return execute_step_4_create_view();
            case 5:
                return execute_step_5_create_cache_table();
            case 6:
                return execute_step_6_create_procedure();
            case 7:
                return execute_step_7_create_triggers();
            case 8:
                return execute_step_8_populate_cache();
            default:
                return new WP_Error('invalid_step', 'Paso de optimización inválido', ['status' => 400]);
        }
    } catch (Exception $e) {
        return new WP_Error('optimization_error', $e->getMessage(), ['status' => 500]);
    }
}

/**
 * PASO 1: Crear índices optimizados
 */
function execute_step_1_indexes() {
    global $wpdb;
    
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_student_lesson ON wpic_asg_progress (student_id, lesson_id)",
        "CREATE INDEX IF NOT EXISTS idx_student_completed ON wpic_asg_progress (student_id, completed)",
        "CREATE INDEX IF NOT EXISTS idx_completion_date ON wpic_asg_progress (completion_date)",
        "CREATE INDEX IF NOT EXISTS idx_student_lesson_completed ON wpic_asg_progress (student_id, lesson_id, completed)"
    ];
    
    $results = [];
    foreach ($indexes as $sql) {
        $result = $wpdb->query($sql);
        $results[] = [
            'sql' => $sql,
            'success' => $result !== false,
            'error' => $result === false ? $wpdb->last_error : null
        ];
    }
    
    return [
        'success' => true,
        'step' => 1,
        'message' => 'Índices optimizados creados exitosamente',
        'details' => $results
    ];
}

/**
 * PASO 2: Agregar columna course_code
 */
function execute_step_2_add_column() {
    global $wpdb;
    
    // Verificar si la columna ya existe
    $column_exists = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'wpic_asg_progress' 
        AND COLUMN_NAME = 'course_code'
    ");
    
    if ($column_exists) {
        return [
            'success' => true,
            'step' => 2,
            'message' => 'La columna course_code ya existe',
            'skipped' => true
        ];
    }
    
    $sql = "ALTER TABLE wpic_asg_progress ADD COLUMN course_code VARCHAR(100) NULL AFTER lesson_id";
    $result = $wpdb->query($sql);
    
    if ($result === false) {
        throw new Exception("Error al agregar columna course_code: " . $wpdb->last_error);
    }
    
    // Crear índices para la nueva columna
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_student_course ON wpic_asg_progress (student_id, course_code)",
        "CREATE INDEX IF NOT EXISTS idx_student_course_completed ON wpic_asg_progress (student_id, course_code, completed)"
    ];
    
    foreach ($indexes as $sql) {
        $wpdb->query($sql);
    }
    
    return [
        'success' => true,
        'step' => 2,
        'message' => 'Columna course_code agregada exitosamente con índices'
    ];
}

/**
 * PASO 3: Poblar course_code en registros existentes
 */
function execute_step_3_populate_course_code() {
    global $wpdb;
    
    $sql = "
        UPDATE wpic_asg_progress p
        JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
        JOIN wpic_modules m ON l.code_module = m.code_module
        SET p.course_code = m.code_course
        WHERE p.course_code IS NULL
    ";
    
    $updated = $wpdb->query($sql);
    
    if ($updated === false) {
        throw new Exception("Error al poblar course_code: " . $wpdb->last_error);
    }
    
    // Verificar registros sin course_code
    $missing = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NULL");
    
    return [
        'success' => true,
        'step' => 3,
        'message' => "Se actualizaron {$updated} registros con course_code",
        'updated_records' => $updated,
        'missing_records' => $missing
    ];
}

/**
 * PASO 4: Crear vista optimizada
 */
function execute_step_4_create_view() {
    global $wpdb;
    
    $sql = "
        CREATE OR REPLACE VIEW v_user_course_progress AS
        SELECT 
            student_id,
            course_code,
            COUNT(*) as total_lessons,
            SUM(completed) as completed_lessons,
            ROUND((SUM(completed) / COUNT(*)) * 100, 2) as progress_percentage,
            GROUP_CONCAT(
                CASE WHEN completed = 1 THEN lesson_id END 
                ORDER BY lesson_id
            ) as completed_lessons_list,
            MAX(CASE WHEN completed = 1 THEN lesson_id END) as last_completed_lesson,
            MAX(CASE WHEN completed = 1 THEN completion_date END) as last_completion_date,
            MIN(created_at) as started_at,
            MAX(updated_at) as last_activity
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL
        GROUP BY student_id, course_code
    ";
    
    $result = $wpdb->query($sql);
    
    if ($result === false) {
        throw new Exception("Error al crear vista: " . $wpdb->last_error);
    }
    
    return [
        'success' => true,
        'step' => 4,
        'message' => 'Vista v_user_course_progress creada exitosamente'
    ];
}

/**
 * PASO 5: Crear tabla de cache
 */
function execute_step_5_create_cache_table() {
    global $wpdb;
    
    $sql = "
        CREATE TABLE IF NOT EXISTS wpic_asg_progress_cache (
            id INT AUTO_INCREMENT PRIMARY KEY,
            student_id INT NOT NULL,
            course_code VARCHAR(100) NOT NULL,
            total_lessons INT NOT NULL,
            completed_lessons INT NOT NULL,
            progress_percentage DECIMAL(5,2) NOT NULL,
            completed_lessons_list TEXT NOT NULL,
            last_completed_lesson INT NULL,
            last_completion_date TIMESTAMP NULL,
            cache_updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            
            UNIQUE KEY unique_student_course (student_id, course_code),
            INDEX idx_student_id (student_id),
            INDEX idx_course_code (course_code),
            INDEX idx_progress (progress_percentage)
        )
    ";
    
    $result = $wpdb->query($sql);
    
    if ($result === false) {
        throw new Exception("Error al crear tabla cache: " . $wpdb->last_error);
    }
    
    return [
        'success' => true,
        'step' => 5,
        'message' => 'Tabla wpic_asg_progress_cache creada exitosamente'
    ];
}

/**
 * PASO 6: Crear procedimiento almacenado
 */
function execute_step_6_create_procedure() {
    global $wpdb;
    
    // Nota: Los procedimientos almacenados pueden no estar disponibles en todos los hostings
    // Este paso es opcional
    
    return [
        'success' => true,
        'step' => 6,
        'message' => 'Procedimiento almacenado omitido (opcional)',
        'skipped' => true,
        'note' => 'Los procedimientos se pueden crear manualmente si el hosting lo permite'
    ];
}

/**
 * PASO 7: Crear triggers
 */
function execute_step_7_create_triggers() {
    global $wpdb;
    
    // Nota: Los triggers pueden no estar disponibles en todos los hostings
    // Este paso es opcional
    
    return [
        'success' => true,
        'step' => 7,
        'message' => 'Triggers omitidos (opcional)',
        'skipped' => true,
        'note' => 'Los triggers se pueden crear manualmente si el hosting lo permite'
    ];
}

/**
 * PASO 8: Poblar cache inicial
 */
function execute_step_8_populate_cache() {
    global $wpdb;
    
    $sql = "
        INSERT INTO wpic_asg_progress_cache 
        (student_id, course_code, total_lessons, completed_lessons, progress_percentage, 
         completed_lessons_list, last_completed_lesson, last_completion_date)
        SELECT 
            student_id,
            course_code,
            total_lessons,
            completed_lessons,
            progress_percentage,
            IFNULL(completed_lessons_list, '') as completed_lessons_list,
            last_completed_lesson,
            last_completion_date
        FROM v_user_course_progress
        ON DUPLICATE KEY UPDATE
            total_lessons = VALUES(total_lessons),
            completed_lessons = VALUES(completed_lessons),
            progress_percentage = VALUES(progress_percentage),
            completed_lessons_list = VALUES(completed_lessons_list),
            last_completed_lesson = VALUES(last_completed_lesson),
            last_completion_date = VALUES(last_completion_date)
    ";
    
    $result = $wpdb->query($sql);
    
    if ($result === false) {
        throw new Exception("Error al poblar cache: " . $wpdb->last_error);
    }
    
    $cache_count = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress_cache");
    
    return [
        'success' => true,
        'step' => 8,
        'message' => "Cache poblado exitosamente con {$cache_count} registros",
        'cache_records' => $cache_count
    ];
}

/**
 * Verificar estado de optimización
 */
function asg_optimization_status() {
    global $wpdb;
    
    // Verificar índices
    $indexes = $wpdb->get_results("SHOW INDEX FROM wpic_asg_progress WHERE Key_name LIKE 'idx_%'");
    
    // Verificar columna course_code
    $course_code_exists = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'wpic_asg_progress' 
        AND COLUMN_NAME = 'course_code'
    ");
    
    // Verificar registros con course_code
    $populated_records = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NOT NULL");
    $total_records = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress");
    
    // Verificar vista
    $view_exists = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.VIEWS 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'v_user_course_progress'
    ");
    
    // Verificar tabla cache
    $cache_table_exists = $wpdb->get_var("
        SELECT COUNT(*) 
        FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() 
        AND TABLE_NAME = 'wpic_asg_progress_cache'
    ");
    
    $cache_records = 0;
    if ($cache_table_exists) {
        $cache_records = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress_cache");
    }
    
    return [
        'success' => true,
        'status' => [
            'step_1_indexes' => count($indexes) >= 4,
            'step_2_column' => $course_code_exists > 0,
            'step_3_populated' => $populated_records > 0,
            'step_4_view' => $view_exists > 0,
            'step_5_cache_table' => $cache_table_exists > 0,
            'step_8_cache_populated' => $cache_records > 0
        ],
        'statistics' => [
            'total_progress_records' => $total_records,
            'populated_course_code' => $populated_records,
            'missing_course_code' => $total_records - $populated_records,
            'cache_records' => $cache_records,
            'indexes_count' => count($indexes)
        ],
        'next_steps' => get_next_optimization_steps($wpdb)
    ];
}

/**
 * Determinar próximos pasos de optimización
 */
function get_next_optimization_steps($wpdb) {
    $steps = [];
    
    // Verificar cada paso
    $indexes = $wpdb->get_results("SHOW INDEX FROM wpic_asg_progress WHERE Key_name LIKE 'idx_%'");
    if (count($indexes) < 4) {
        $steps[] = ['step' => 1, 'description' => 'Crear índices optimizados'];
    }
    
    $course_code_exists = $wpdb->get_var("
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'wpic_asg_progress' AND COLUMN_NAME = 'course_code'
    ");
    if (!$course_code_exists) {
        $steps[] = ['step' => 2, 'description' => 'Agregar columna course_code'];
    }
    
    $populated = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NOT NULL");
    if ($course_code_exists && $populated == 0) {
        $steps[] = ['step' => 3, 'description' => 'Poblar course_code en registros existentes'];
    }
    
    return $steps;
}

/**
 * Probar performance de consultas
 */
function asg_test_progress_performance() {
    global $wpdb;
    
    // Obtener un estudiante y curso de ejemplo
    $sample = $wpdb->get_row("
        SELECT student_id, course_code 
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL 
        LIMIT 1
    ");
    
    if (!$sample) {
        return new WP_Error('no_data', 'No hay datos para probar', ['status' => 400]);
    }
    
    $student_id = $sample->student_id;
    $course_code = $sample->course_code;
    
    // Probar consulta antigua (con JOINs)
    $start_time = microtime(true);
    $old_result = $wpdb->get_row($wpdb->prepare("
        SELECT 
            COUNT(*) as total_lessons,
            SUM(p.completed) as completed_lessons,
            ROUND((SUM(p.completed) / COUNT(*)) * 100, 2) as progress_percentage
        FROM wpic_asg_progress p
        JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE p.student_id = %d AND m.code_course = %s
    ", $student_id, $course_code));
    $old_time = microtime(true) - $start_time;
    
    // Probar consulta nueva (optimizada)
    $start_time = microtime(true);
    $new_result = $wpdb->get_row($wpdb->prepare("
        SELECT 
            COUNT(*) as total_lessons,
            SUM(completed) as completed_lessons,
            ROUND((SUM(completed) / COUNT(*)) * 100, 2) as progress_percentage
        FROM wpic_asg_progress 
        WHERE student_id = %d AND course_code = %s
    ", $student_id, $course_code));
    $new_time = microtime(true) - $start_time;
    
    // Probar cache si existe
    $cache_time = null;
    $cache_result = null;
    $cache_exists = $wpdb->get_var("
        SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
        WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'wpic_asg_progress_cache'
    ");
    
    if ($cache_exists) {
        $start_time = microtime(true);
        $cache_result = $wpdb->get_row($wpdb->prepare("
            SELECT total_lessons, completed_lessons, progress_percentage
            FROM wpic_asg_progress_cache 
            WHERE student_id = %d AND course_code = %s
        ", $student_id, $course_code));
        $cache_time = microtime(true) - $start_time;
    }
    
    return [
        'success' => true,
        'test_data' => [
            'student_id' => $student_id,
            'course_code' => $course_code
        ],
        'performance' => [
            'old_query' => [
                'time_seconds' => round($old_time, 6),
                'result' => $old_result
            ],
            'optimized_query' => [
                'time_seconds' => round($new_time, 6),
                'result' => $new_result,
                'improvement' => $old_time > 0 ? round((($old_time - $new_time) / $old_time) * 100, 1) . '%' : 'N/A'
            ],
            'cache_query' => $cache_time !== null ? [
                'time_seconds' => round($cache_time, 6),
                'result' => $cache_result,
                'improvement' => $old_time > 0 ? round((($old_time - $cache_time) / $old_time) * 100, 1) . '%' : 'N/A'
            ] : null
        ]
    ];
}
?>
