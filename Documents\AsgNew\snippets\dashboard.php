/**
 * ASG Dashboard Optimized - WordPress Code Snippet
 *
 * Descripción: Dashboard optimizado usando endpoints /courses/api reales
 * Versión: 3.0.0 - FULLY OPTIMIZED WITH REAL DATA
 * Autor: ING. Bryan Marc S.M
 */

// Evitar acceso directo
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Crear página del dashboard optimizado
 */
function asg_create_dashboard_optimized_page() {
    // Verificar permisos
    if (!current_user_can('manage_options')) {
        wp_die(__('No tienes permisos para acceder a esta página.'));
    }

    // Obtener URL base
    $site_url = get_site_url();
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>ASG Dashboard - Course Management</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <style>
            .wg-li weglot-lang weglot-language weglot-flags flag-3 wg-es{
                display: none;
            }
            
            /* Navbar Styles */
            .navbar {
                background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
                height: 70px;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }

            .navbar-brand img {
                width: 130px;
                height: auto;
            }

            .navbar-nav .nav-link {
                color: rgba(255,255,255,0.9) !important;
                font-weight: 500;
                padding: 0.5rem 1rem !important;
                border-radius: 6px;
                transition: all 0.2s;
            }

            .navbar-nav .nav-link:hover {
                color: white !important;
                background-color: rgba(255,255,255,0.1);
            }

            .navbar-nav .nav-link.active {
                color: white !important;
                background-color: rgba(255,255,255,0.2);
            }

            /* Main Content */
            body {
                font-family: 'Inter', sans-serif;
                background-color: #f8fafc;
                padding-top: 70px;
            }

            .main-content {
                padding: 2rem;
                max-width: 1400px;
                margin: 0 auto;
            }

            /* Dashboard Header */
            .dashboard-header {
                background: white;
                border-radius: 12px;
                padding: 2rem;
                margin-bottom: 2rem;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .dashboard-title {
                font-size: 2rem;
                font-weight: 700;
                color: #1f2937;
                margin-bottom: 0.5rem;
            }

            .dashboard-subtitle {
                color: #6c757d;
                font-size: 1.1rem;
            }

            /* Stats Cards */
            .stats-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
                gap: 1.5rem;
                margin-bottom: 2rem;
            }

            .stat-card {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
                border-left: 4px solid var(--card-color, #2563eb);
                transition: transform 0.2s, box-shadow 0.2s;
            }

            .stat-card:hover {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

            .stat-header {
                display: flex;
                justify-content: space-between;
                align-items: flex-start;
            }

            .stat-icon {
                width: 48px;
                height: 48px;
                border-radius: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                background-color: var(--icon-bg, #dbeafe);
                color: var(--icon-color, #2563eb);
                font-size: 1.5rem;
            }

            .stat-label {
                font-size: 0.875rem;
                font-weight: 500;
                color: #6b7280;
                margin-bottom: 0.5rem;
            }

            .stat-number {
                font-size: 2rem;
                font-weight: 700;
                color: #1f2937;
                line-height: 1;
            }

            /* Recent Courses */
            .recent-courses {
                background: white;
                border-radius: 12px;
                padding: 1.5rem;
                box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            }

            .section-title {
                font-size: 1.25rem;
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 1rem;
                display: flex;
                align-items: center;
                gap: 0.5rem;
            }

            .course-item {
                display: flex;
                align-items: center;
                padding: 1rem;
                border-radius: 8px;
                transition: background-color 0.2s;
                border-bottom: 1px solid #f3f4f6;
            }

            .course-item:last-child {
                border-bottom: none;
            }

            .course-item:hover {
                background-color: #f9fafb;
            }

            .course-image {
                width: 60px;
                height: 60px;
                border-radius: 8px;
                object-fit: cover;
                margin-right: 1rem;
                background-color: #f3f4f6;
            }

            .course-info {
                flex: 1;
            }

            .course-title {
                font-weight: 600;
                color: #1f2937;
                margin-bottom: 0.25rem;
                font-size: 0.95rem;
            }

            .course-meta {
                font-size: 0.8rem;
                color: #6b7280;
                display: flex;
                gap: 1rem;
            }

            .course-status {
                padding: 0.25rem 0.75rem;
                border-radius: 20px;
                font-size: 0.75rem;
                font-weight: 500;
            }

            .status-published {
                background-color: #d1fae5;
                color: #065f46;
            }

            .status-draft {
                background-color: #fef3c7;
                color: #92400e;
            }

            /* Loading States */
            .loading-skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
                border-radius: 4px;
            }

            .loading-number {
                height: 2rem;
                width: 4rem;
            }

            .loading-text {
                height: 1rem;
                width: 8rem;
                margin-bottom: 0.5rem;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            /* Action Buttons */
            .action-buttons {
                display: flex;
                gap: 0.5rem;
            }

            .btn-action {
                padding: 0.5rem;
                border: none;
                border-radius: 6px;
                cursor: pointer;
                transition: all 0.2s;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .btn-edit {
                background-color: #dbeafe;
                color: #2563eb;
            }

            .btn-edit:hover {
                background-color: #bfdbfe;
            }

            .btn-view {
                background-color: #d1fae5;
                color: #059669;
            }

            .btn-view:hover {
                background-color: #a7f3d0;
            }

            /* Responsive */
            @media (max-width: 768px) {
                .main-content {
                    padding: 1rem;
                }

                .dashboard-header {
                    padding: 1.5rem;
                }

                .stats-grid {
                    grid-template-columns: 1fr;
                }

                .course-item {
                    flex-direction: column;
                    align-items: flex-start;
                    gap: 1rem;
                }

                .course-image {
                    margin-right: 0;
                }
            }

            /* Notifications */
            .notification {
                position: fixed;
                top: 90px;
                right: 20px;
                padding: 1rem 1.5rem;
                border-radius: 8px;
                color: white;
                font-weight: 500;
                z-index: 1050;
                transform: translateX(400px);
                transition: transform 0.3s ease;
            }

            .notification.show {
                transform: translateX(0);
            }

            .notification.success { background-color: #10b981; }
            .notification.error { background-color: #ef4444; }
            .notification.warning { background-color: #f59e0b; }
            .notification.info { background-color: #3b82f6; }
        </style>
    </head>
    <body>
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo $site_url; ?>/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo" style="width:130px;height:auto;">
                </a>

                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>

                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        <li class="nav-item">
                            <a class="nav-link active" href="<?php echo $site_url; ?>/dashboard">
                                <i class="bi bi-speedometer2 me-1"></i>Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/all-courses">
                                <i class="bi bi-collection me-1"></i>All Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/new-course">
                                <i class="bi bi-plus-circle me-1"></i>New Course
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="main-content">
            <!-- Dashboard Header -->
            <div class="dashboard-header">
                <h1 class="dashboard-title">
                    <i class="bi bi-speedometer2 me-2"></i>Dashboard
                </h1>
                <p class="dashboard-subtitle">
                    Welcome back, <strong><?php echo esc_html(wp_get_current_user()->display_name ?: wp_get_current_user()->user_login); ?></strong>!
                    Here's what's happening with your courses.
                </p>
            </div>

            <!-- Stats Grid -->
            <div class="stats-grid">
                <!-- Total Courses -->
                <div class="stat-card" style="--card-color: #2563eb; --icon-bg: #dbeafe; --icon-color: #2563eb;">
                    <div class="stat-header">
                        <div>
                            <p class="stat-label">Total Courses</p>
                            <div class="stat-number" id="totalCourses">
                                <div class="loading-skeleton loading-number"></div>
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-collection"></i>
                        </div>
                    </div>
                </div>

                <!-- Published Courses -->
                <div class="stat-card" style="--card-color: #10b981; --icon-bg: #d1fae5; --icon-color: #10b981;">
                    <div class="stat-header">
                        <div>
                            <p class="stat-label">Published Courses</p>
                            <div class="stat-number" id="publishedCourses">
                                <div class="loading-skeleton loading-number"></div>
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-check-circle"></i>
                        </div>
                    </div>
                </div>

                <!-- Total Modules -->
                <div class="stat-card" style="--card-color: #8b5cf6; --icon-bg: #ede9fe; --icon-color: #8b5cf6;">
                    <div class="stat-header">
                        <div>
                            <p class="stat-label">Total Modules</p>
                            <div class="stat-number" id="totalModules">
                                <div class="loading-skeleton loading-number"></div>
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-layers"></i>
                        </div>
                    </div>
                </div>

                <!-- Total Lessons -->
                <div class="stat-card" style="--card-color: #f59e0b; --icon-bg: #fef3c7; --icon-color: #f59e0b;">
                    <div class="stat-header">
                        <div>
                            <p class="stat-label">Total Lessons</p>
                            <div class="stat-number" id="totalLessons">
                                <div class="loading-skeleton loading-number"></div>
                            </div>
                        </div>
                        <div class="stat-icon">
                            <i class="bi bi-play-circle"></i>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Recent Courses -->
            <div class="recent-courses">
                <h2 class="section-title">
                    <i class="bi bi-clock-history"></i>
                    Recent Courses
                </h2>
                <div id="recentCoursesList">
                    <!-- Loading state -->
                    <div class="course-item">
                        <div class="course-image loading-skeleton"></div>
                        <div class="course-info">
                            <div class="loading-skeleton loading-text"></div>
                            <div class="loading-skeleton loading-text" style="width: 6rem;"></div>
                        </div>
                    </div>
                    <div class="course-item">
                        <div class="course-image loading-skeleton"></div>
                        <div class="course-info">
                            <div class="loading-skeleton loading-text"></div>
                            <div class="loading-skeleton loading-text" style="width: 6rem;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

        <script>
            // ========================================
            // ASG API CLIENT OPTIMIZED
            // ========================================
            class ASGApiClientOptimized {
                constructor() {
                    this.baseUrl = '<?php echo rest_url('asg/v1'); ?>';
                    this.nonce = '<?php echo wp_create_nonce('wp_rest'); ?>';
                }

                async request(endpoint, options = {}) {
                    const url = `${this.baseUrl}${endpoint}`;
                    const config = {
                        headers: {
                            'Content-Type': 'application/json',
                            'X-WP-Nonce': this.nonce,
                            ...options.headers
                        },
                        ...options
                    };

                    try {
                        const response = await fetch(url, config);
                        const data = await response.json();

                        if (!response.ok) {
                            throw new Error(data.message || `HTTP error! status: ${response.status}`);
                        }

                        return data;
                    } catch (error) {
                        console.error('API Error:', error);
                        throw error;
                    }
                }

                async get(endpoint, params = {}) {
                    const queryString = new URLSearchParams(params).toString();
                    const finalEndpoint = queryString ? `${endpoint}?${queryString}` : endpoint;
                    return this.request(finalEndpoint);
                }

                // Get all courses with stats
                async getCourses(params = {}) {
                    const defaultParams = {
                        include: 'stats,images',
                        per_page: 50
                    };
                    const finalParams = { ...defaultParams, ...params };
                    return this.get('/courses/api', finalParams);
                }
            }

            const ASG_API = new ASGApiClientOptimized();

            // ========================================
            // DASHBOARD FUNCTIONS
            // ========================================

            // Show notification
            function showNotification(message, type = 'info') {
                const notification = document.createElement('div');
                notification.className = `notification ${type}`;
                notification.textContent = message;
                document.body.appendChild(notification);

                setTimeout(() => notification.classList.add('show'), 100);
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => document.body.removeChild(notification), 300);
                }, 3000);
            }

            // Animate numbers
            function animateNumber(elementId, targetValue, duration = 1000) {
                const element = document.getElementById(elementId);
                if (!element) return;

                const startValue = 0;
                const increment = targetValue / (duration / 16);
                let currentValue = startValue;

                const timer = setInterval(() => {
                    currentValue += increment;
                    if (currentValue >= targetValue) {
                        currentValue = targetValue;
                        clearInterval(timer);
                    }
                    element.textContent = Math.floor(currentValue);
                }, 16);
            }

            // Load dashboard data
            async function loadDashboardData() {
                try {
                    console.log('🚀 Loading dashboard data with optimized API...');

                    // Get all courses with stats
                    const response = await ASG_API.getCourses({
                        include: 'stats,images',
                        per_page: 100 // Get all courses for stats
                    });

                    console.log('📊 Courses response:', response);

                    if (response.success && response.data) {
                        const courses = response.data;

                        // Calculate stats from courses
                        const stats = calculateStats(courses);
                        console.log('📈 Calculated stats:', stats);

                        // Update dashboard
                        updateStatsDisplay(stats);
                        updateRecentCourses(courses.slice(0, 5)); // Show 5 most recent

                        showNotification('✅ Dashboard loaded successfully!', 'success');
                    } else {
                        throw new Error('No data received');
                    }
                } catch (error) {
                    console.error('❌ Error loading dashboard:', error);
                    showNotification('⚠️ Error loading dashboard data', 'warning');

                    // Show fallback data
                    updateStatsDisplay({
                        totalCourses: 0,
                        publishedCourses: 0,
                        totalModules: 0,
                        totalLessons: 0
                    });
                    updateRecentCourses([]);
                }
            }

            // Calculate stats from courses data
            function calculateStats(courses) {
                const stats = {
                    totalCourses: courses.length,
                    publishedCourses: 0,
                    totalModules: 0,
                    totalLessons: 0
                };

                courses.forEach(course => {
                    console.log(`📊 Processing course: ${course.name_course}`);
                    console.log(`� Course stats:`, course.stats);

                    if (course.status_course === 'published') {
                        stats.publishedCourses++;
                    }

                    // Usar las estadísticas que ya vienen en la respuesta
                    if (course.stats) {
                        const moduleCount = parseInt(course.stats.modules_count || 0);
                        const lessonCount = parseInt(course.stats.lessons_count || 0);

                        stats.totalModules += moduleCount;
                        stats.totalLessons += lessonCount;

                        console.log(`� Found ${moduleCount} modules and ${lessonCount} lessons in ${course.name_course}`);
                    } else {
                        console.log(`⚠️ No stats found for course: ${course.name_course}`);
                    }
                });

                return stats;
            }

            // Update stats display
            function updateStatsDisplay(stats) {
                animateNumber('totalCourses', stats.totalCourses || 0);
                animateNumber('publishedCourses', stats.publishedCourses || 0);
                animateNumber('totalModules', stats.totalModules || 0);
                animateNumber('totalLessons', stats.totalLessons || 0);
            }

            // Update recent courses
            function updateRecentCourses(courses) {
                const container = document.getElementById('recentCoursesList');

                if (!courses || courses.length === 0) {
                    container.innerHTML = `
                        <div class="course-item">
                            <div class="course-info">
                                <div class="course-title">No courses found</div>
                                <div class="course-meta">Create your first course to get started</div>
                            </div>
                        </div>
                    `;
                    return;
                }

                container.innerHTML = courses.map(course => `
                    <div class="course-item">
                        <img src="${course.cover_img || 'https://via.placeholder.com/60x60?text=No+Image'}"
                             alt="${course.name_course}"
                             class="course-image"
                             onerror="this.src='https://via.placeholder.com/60x60?text=No+Image'">
                        <div class="course-info">
                            <div class="course-title">${course.name_course}</div>
                            <div class="course-meta">
                                <span>${course.stats ? course.stats.modules_count || 0 : 0} modules</span>
                                <span>${course.stats ? course.stats.lessons_count || 0 : 0} lessons</span>
                                <span>${course.category_course || 'General'}</span>
                            </div>
                        </div>
                        <div class="course-status status-${course.status_course}">
                            ${course.status_course === 'published' ? 'Published' : 'Draft'}
                        </div>
                        <div class="action-buttons">
                            <button class="btn-action btn-edit" onclick="editCourse('${course.code_course}')" title="Edit Course">
                                <i class="bi bi-pencil"></i>
                            </button>
                            <button class="btn-action btn-view" onclick="viewCourse('${course.code_course}')" title="View Course">
                                <i class="bi bi-eye"></i>
                            </button>
                        </div>
                    </div>
                `).join('');
            }

            // Course actions
            function editCourse(courseCode) {
                window.location.href = `<?php echo $site_url; ?>/edit-course?#${courseCode}`;
            }

            function viewCourse(courseCode) {
                window.location.href = `<?php echo $site_url; ?>/lessons/?course=${courseCode}`;
            }

            // Initialize dashboard
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🚀 Dashboard Optimized v3.0.0 loaded');
                loadDashboardData();
            });
        </script>
    </body>
    </html>
    <?php
}
/**
 * Registrar la página del dashboard optimizado
 */
function asg_register_dashboard_optimized_page_public() {
    // Crear página si no existe
    $page_slug = 'admin-dashboard';
    $page = get_page_by_path($page_slug);

    if (!$page) {
        $page_data = array(
            'post_title'    => 'Dashboard ASG',
            'post_content'  => '[asg_dashboard]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );

        wp_insert_post($page_data);
    }
}

/**
 * Shortcode para mostrar el dashboard (OPTIMIZADO)
 */
function asg_dashboard_shortcode($atts) {
    ob_start();
    asg_create_dashboard_optimized_page();
    return ob_get_clean();
}

// Registrar hooks
add_action('init', 'asg_register_dashboard_optimized_page_public');
add_shortcode('asg_dashboard', 'asg_dashboard_shortcode');

// Registrar ruta personalizada
add_action('init', function() {
    add_rewrite_rule('^admin-dashboard/?$', 'index.php?asg_page=dashboard', 'top');
});

add_filter('query_vars', function($vars) {
    $vars[] = 'asg_page';
    return $vars;
});

add_action('template_redirect', function() {
    $asg_page = get_query_var('asg_page');
    if ($asg_page === 'dashboard') {
        asg_create_dashboard_optimized_page();
        exit;
    }
});
