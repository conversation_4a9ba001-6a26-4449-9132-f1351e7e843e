<?php
// Verificar que estamos en WordPress
if (!defined('ABSPATH')) {
    exit;
}

// Configuración y variables globales
$site_url = get_site_url();
$course_code = isset($_GET['course']) ? sanitize_text_field($_GET['course']) : '';
$lesson_id = isset($_GET['lesson']) ? sanitize_text_field($_GET['lesson']) : '';
$auto_load_first = !$lesson_id && $course_code;

// Verificar acceso del usuario
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$access_denied = false;
$access_message = '';

// Verificar enrollment si el usuario está logueado
if ($user_id && $course_code) {
    global $wpdb;
    $enrollment_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}asg_student_enrollments 
         WHERE id_user = %d AND code_course = %s AND status = 'active'",
        $user_id, $course_code
    ));
    
    if (!$enrollment_check) {
        $access_denied = true;
        $access_message = 'Necesitas estar inscrito en este curso para acceder a las lecciones.';
    }
} elseif (!$user_id) {
    $access_denied = true;
    $access_message = 'Debes iniciar sesión para acceder a las lecciones.';
}

function asg_render_lessons_page() {
    global $site_url, $course_code, $lesson_id, $auto_load_first, $access_denied, $access_message;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Lecciones - <?php echo esc_html($course_code); ?></title>
</head>
<body>
    <?php if ($access_denied): ?>
        <div>
            <h2>Acceso Restringido</h2>
            <p><?php echo esc_html($access_message); ?></p>
            <?php if (!is_user_logged_in()): ?>
                <a href="/wp-login.php">Iniciar Sesión</a>
            <?php else: ?>
                <a href="/vistapreliminar/?course=<?php echo esc_attr($course_code); ?>">Ver Curso</a>
            <?php endif; ?>
        </div>
    <?php else: ?>
        <div>
            <h1>Contenido del Curso</h1>
            <p>Aquí va el contenido de las lecciones</p>
        </div>
    <?php endif; ?>
</body>
</html>
<?php
}

function asg_lessons_shortcode($atts) {
    ob_start();
    asg_render_lessons_page();
    return ob_get_clean();
}

add_shortcode('asg_lessons', 'asg_lessons_shortcode');

function asg_display_lessons() {
    echo do_shortcode('[asg_lessons]');
}

if (basename($_SERVER['PHP_SELF']) === 'test_php_only.php') {
    asg_render_lessons_page();
}
?>
