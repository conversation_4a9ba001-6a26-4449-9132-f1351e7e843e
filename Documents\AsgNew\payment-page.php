<?php
/**
 * ========================================
 * ASG PAYMENT PAGE - PayPal Integration
 * ========================================
 * 
 * Página de pago optimizada para AbilitySeminarsGroup
 * Integración completa con PayPal SDK
 * Procesamiento automático post-pago
 * 
 * URL: /payment/?course=course_1&price=99
 * 
 * Version: 1.0.0 - PAYMENT SYSTEM
 * Author: ASG Development Team
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * ========================================
 * PAYMENT PAGE SHORTCODE
 * ========================================
 */
add_shortcode('asg_payment_page', 'asg_payment_page_handler');

function asg_payment_page_handler($atts) {
    // Verificar que el usuario esté logueado
    if (!is_user_logged_in()) {
        return '<div class="asg-error">
            <h3>Acceso Restringido</h3>
            <p>Debes iniciar sesión para acceder a esta página.</p>
            <a href="' . wp_login_url(get_permalink()) . '" class="btn btn-primary">Iniciar Sesión</a>
        </div>';
    }
    
    // Obtener parámetros de la URL
    $course_code = sanitize_text_field($_GET['course'] ?? '');
    $price = floatval($_GET['price'] ?? 0);
    $new_user = isset($_GET['new_user']) ? '1' : '0';
    
    if (empty($course_code) || $price <= 0) {
        return '<div class="asg-error">
            <h3>Error en los Parámetros</h3>
            <p>Información del curso incompleta. Por favor, regresa a la página del curso.</p>
            <a href="/" class="btn btn-secondary">Volver al Inicio</a>
        </div>';
    }
    
    // Obtener información del curso
    global $wpdb;
    $course = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM wpic_courses WHERE code_course = %s AND status_course = 'published'",
        $course_code
    ));
    
    if (!$course) {
        return '<div class="asg-error">
            <h3>Curso No Encontrado</h3>
            <p>El curso solicitado no está disponible.</p>
            <a href="/" class="btn btn-secondary">Volver al Inicio</a>
        </div>';
    }
    
    // Verificar si ya está inscrito
    $current_user = wp_get_current_user();
    $existing_enrollment = $wpdb->get_row($wpdb->prepare("
        SELECT e.* FROM wpic_asg_enrollments e
        JOIN wpic_asg_students s ON e.student_id = s.id_student
        WHERE s.id_user = %d AND e.course_id = %d
    ", $current_user->ID, $course->id_course));
    
    if ($existing_enrollment) {
        return '<div class="asg-success">
            <h3>Ya Estás Inscrito</h3>
            <p>Ya tienes acceso a este curso.</p>
            <a href="/lessons/?course=' . $course_code . '" class="btn btn-success">Ir a las Lecciones</a>
        </div>';
    }
    
    // Generar el HTML de la página de pago
    ob_start();
    ?>
    
    <div class="asg-payment-container">
        <!-- Header de Bienvenida -->
        <?php if ($new_user === '1'): ?>
        <div class="welcome-banner">
            <h2>🎉 ¡Bienvenido, <?php echo esc_html($current_user->display_name); ?>!</h2>
            <p>Tu cuenta ha sido creada exitosamente. Ahora completa tu inscripción:</p>
        </div>
        <?php endif; ?>
        
        <!-- Información del Curso -->
        <div class="course-summary">
            <div class="course-header">
                <h1>Completar Inscripción</h1>
                <div class="course-info">
                    <?php if (!empty($course->cover_img)): ?>
                    <img src="<?php echo esc_url($course->cover_img); ?>" alt="<?php echo esc_attr($course->name_course); ?>" class="course-image">
                    <?php endif; ?>
                    <div class="course-details">
                        <h2><?php echo esc_html($course->name_course); ?></h2>
                        <p class="course-description"><?php echo esc_html($course->description_course); ?></p>
                        <div class="course-meta">
                            <span class="category">📚 <?php echo esc_html($course->category_course); ?></span>
                            <span class="duration">⏱️ <?php echo esc_html($course->duration_course); ?> horas</span>
                            <span class="language">🌐 <?php echo esc_html($course->language_course); ?></span>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Precio y Resumen -->
            <div class="payment-summary">
                <div class="price-breakdown">
                    <div class="price-item">
                        <span>Curso: <?php echo esc_html($course->name_course); ?></span>
                        <span class="price">$<?php echo number_format($price, 2); ?> USD</span>
                    </div>
                    <div class="price-total">
                        <span>Total a Pagar:</span>
                        <span class="total-price">$<?php echo number_format($price, 2); ?> USD</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Botones de Pago -->
        <div class="payment-section">
            <h3>Método de Pago</h3>
            <p>Pago seguro procesado por PayPal. Puedes pagar con tu cuenta PayPal o tarjeta de crédito.</p>
            
            <!-- PayPal Button Container -->
            <div id="paypal-button-container"></div>
            
            <!-- Loading State -->
            <div id="payment-loading" style="display: none;">
                <div class="loading-spinner"></div>
                <p>Procesando pago...</p>
            </div>
            
            <!-- Success State -->
            <div id="payment-success" style="display: none;">
                <div class="success-message">
                    <h3>🎉 ¡Pago Exitoso!</h3>
                    <p>Tu inscripción ha sido procesada. Redirigiendo a las lecciones...</p>
                </div>
            </div>
            
            <!-- Error State -->
            <div id="payment-error" style="display: none;">
                <div class="error-message">
                    <h3>❌ Error en el Pago</h3>
                    <p id="error-details">Hubo un problema procesando tu pago. Por favor, intenta nuevamente.</p>
                    <button onclick="location.reload()" class="btn btn-primary">Reintentar</button>
                </div>
            </div>
        </div>
        
        <!-- Garantía y Soporte -->
        <div class="guarantee-section">
            <h4>🛡️ Garantía de Satisfacción</h4>
            <ul>
                <li>✅ Acceso de por vida al curso</li>
                <li>✅ Actualizaciones gratuitas</li>
                <li>✅ Soporte técnico incluido</li>
                <li>✅ Certificado de finalización</li>
            </ul>
        </div>
    </div>
    
    <!-- CSS Styles -->
    <style>
        .asg-payment-container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }
        
        .welcome-banner {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .course-summary {
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            overflow: hidden;
            margin-bottom: 30px;
        }
        
        .course-header {
            padding: 30px;
            background: #f8f9fa;
        }
        
        .course-info {
            display: flex;
            gap: 20px;
            align-items: flex-start;
        }
        
        .course-image {
            width: 150px;
            height: 100px;
            object-fit: cover;
            border-radius: 8px;
            flex-shrink: 0;
        }
        
        .course-details h2 {
            margin: 0 0 10px 0;
            color: #2c3e50;
        }
        
        .course-description {
            color: #666;
            margin-bottom: 15px;
            line-height: 1.5;
        }
        
        .course-meta {
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
        }
        
        .course-meta span {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 14px;
            color: #495057;
        }
        
        .payment-summary {
            padding: 20px 30px;
            border-top: 1px solid #dee2e6;
            background: #f8f9fa;
        }
        
        .price-breakdown {
            max-width: 400px;
        }
        
        .price-item {
            display: flex;
            justify-content: space-between;
            padding: 10px 0;
            border-bottom: 1px solid #dee2e6;
        }
        
        .price-total {
            display: flex;
            justify-content: space-between;
            padding: 15px 0 5px 0;
            font-weight: bold;
            font-size: 18px;
            color: #2c3e50;
        }
        
        .total-price {
            color: #28a745;
        }
        
        .payment-section {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .payment-section h3 {
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        #paypal-button-container {
            margin: 20px 0;
            min-height: 50px;
        }
        
        .loading-spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success-message, .error-message {
            text-align: center;
            padding: 20px;
            border-radius: 8px;
        }
        
        .success-message {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .error-message {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .guarantee-section {
            background: #e8f5e8;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #28a745;
        }
        
        .guarantee-section h4 {
            margin-bottom: 15px;
            color: #2c3e50;
        }
        
        .guarantee-section ul {
            list-style: none;
            padding: 0;
        }
        
        .guarantee-section li {
            padding: 5px 0;
            color: #495057;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            text-align: center;
            cursor: pointer;
            border: none;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-primary:hover {
            background: #0056b3;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .asg-error, .asg-success {
            text-align: center;
            padding: 40px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .asg-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .asg-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        @media (max-width: 768px) {
            .asg-payment-container {
                padding: 15px;
            }
            
            .course-info {
                flex-direction: column;
            }
            
            .course-image {
                width: 100%;
                height: 200px;
            }
            
            .course-meta {
                justify-content: center;
            }
        }
    </style>

    <!-- PayPal SDK -->
    <?php
    // Configuración PayPal simple
    $paypal_client_id = 'AZDxjDScFpQtjWTOUtWKbyN_bDt4OgqaF4eYXlewfBP4-8aqX3PiV8e1GWU6liB2CU21rbWUVscg9RqE'; // Sandbox
    // Para producción, cambiar por tu Client ID real
    ?>
    <script src="https://www.paypal.com/sdk/js?client-id=<?php echo $paypal_client_id; ?>&currency=USD"></script>

    <!-- Payment Integration JavaScript -->
    <script>
        // Configuración del curso
        const COURSE_CONFIG = {
            code: '<?php echo esc_js($course_code); ?>',
            name: '<?php echo esc_js($course->name_course); ?>',
            price: <?php echo json_encode($price); ?>,
            currency: 'USD'
        };

        // URLs de la API
        const API_BASE = '<?php echo esc_url(rest_url('asg/v1/')); ?>';
        const NONCE = '<?php echo wp_create_nonce('wp_rest'); ?>';

        // Inicializar PayPal cuando el DOM esté listo
        document.addEventListener('DOMContentLoaded', function() {
            initializePayPal();
        });

        function initializePayPal() {
            paypal.Buttons({
                style: {
                    layout: 'vertical',
                    color: 'blue',
                    shape: 'rect',
                    label: 'paypal',
                    height: 50
                },

                createOrder: function(data, actions) {
                    return actions.order.create({
                        purchase_units: [{
                            amount: {
                                value: COURSE_CONFIG.price.toString(),
                                currency_code: COURSE_CONFIG.currency
                            },
                            description: `Curso: ${COURSE_CONFIG.name}`,
                            custom_id: COURSE_CONFIG.code
                        }]
                    });
                },

                onApprove: function(data, actions) {
                    showLoading();

                    return actions.order.capture().then(function(details) {
                        console.log('PayPal payment captured:', details);

                        // Procesar enrollment en el backend
                        processEnrollment(details);
                    });
                },

                onError: function(err) {
                    console.error('PayPal error:', err);
                    showError('Error en el procesamiento del pago. Por favor, intenta nuevamente.');
                },

                onCancel: function(data) {
                    console.log('PayPal payment cancelled:', data);
                    showError('Pago cancelado. Puedes intentar nuevamente cuando desees.');
                }

            }).render('#paypal-button-container');
        }

        async function processEnrollment(paypalDetails) {
            try {
                const response = await fetch(API_BASE + 'process-enrollment', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'X-WP-Nonce': NONCE
                    },
                    body: JSON.stringify({
                        course_code: COURSE_CONFIG.code,
                        paypal_payment_id: paypalDetails.id,
                        amount_paid: COURSE_CONFIG.price
                    })
                });

                const result = await response.json();

                if (result.success) {
                    showSuccess(result.data);

                    // Redirect a las lecciones después de 3 segundos
                    setTimeout(() => {
                        window.location.href = result.data.redirect_url || `/lessons/?course=${COURSE_CONFIG.code}`;
                    }, 3000);
                } else {
                    throw new Error(result.error || 'Error procesando la inscripción');
                }

            } catch (error) {
                console.error('Enrollment error:', error);
                showError('Error procesando la inscripción: ' + error.message);
            }
        }

        function showLoading() {
            document.getElementById('paypal-button-container').style.display = 'none';
            document.getElementById('payment-loading').style.display = 'block';
            document.getElementById('payment-success').style.display = 'none';
            document.getElementById('payment-error').style.display = 'none';
        }

        function showSuccess(data) {
            document.getElementById('payment-loading').style.display = 'none';
            document.getElementById('payment-success').style.display = 'block';
            document.getElementById('payment-error').style.display = 'none';

            // Actualizar mensaje de éxito con datos específicos
            const successDiv = document.getElementById('payment-success');
            successDiv.innerHTML = `
                <div class="success-message">
                    <h3>🎉 ¡Inscripción Exitosa!</h3>
                    <p>¡Felicidades, ${data.student_name}! Te has inscrito exitosamente en:</p>
                    <h4>${data.course_name}</h4>
                    <p>Se han inicializado ${data.lessons_initialized} lecciones para tu progreso.</p>
                    <p>Redirigiendo a las lecciones en <span id="countdown">3</span> segundos...</p>
                </div>
            `;

            // Countdown visual
            let seconds = 3;
            const countdownEl = document.getElementById('countdown');
            const interval = setInterval(() => {
                seconds--;
                if (countdownEl) countdownEl.textContent = seconds;
                if (seconds <= 0) clearInterval(interval);
            }, 1000);
        }

        function showError(message) {
            document.getElementById('payment-loading').style.display = 'none';
            document.getElementById('payment-success').style.display = 'none';
            document.getElementById('payment-error').style.display = 'block';
            document.getElementById('paypal-button-container').style.display = 'block';

            // Actualizar mensaje de error
            document.getElementById('error-details').textContent = message;
        }

        // Función para reintentar pago
        function retryPayment() {
            document.getElementById('payment-error').style.display = 'none';
            document.getElementById('paypal-button-container').style.display = 'block';
        }

        // Debug: Log de configuración
        console.log('ASG Payment Page initialized:', COURSE_CONFIG);
    </script>

    </body>
    </html>
    <?php
    return ob_get_clean();
}

/**
 * Registrar la página de pago automáticamente
 */
function asg_register_payment_page() {
    // Verificar si la página ya existe
    $page_slug = 'payment';
    $existing_page = get_page_by_path($page_slug);

    if (!$existing_page) {
        // Crear la página automáticamente
        $page_data = array(
            'post_title'    => 'Pago',
            'post_content'  => '[asg_payment_page]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug,
            'post_author'   => 1,
            'comment_status' => 'closed',
            'ping_status'   => 'closed'
        );
        wp_insert_post($page_data);
    }
}

add_action('init', 'asg_register_payment_page');

?>
