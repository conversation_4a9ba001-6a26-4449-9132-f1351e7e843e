-- =====================================================
-- OPTIMIZACIÓN COMPLETA DE wpic_asg_progress
-- Ejecutar paso a paso en phpMyAdmin o cliente SQL
-- =====================================================

-- INFORMACIÓN INICIAL
-- ===================
SELECT 'INICIANDO OPTIMIZACIÓN DE wpic_asg_progress' as mensaje, NOW() as fecha_hora;

-- Verificar estado actual
SELECT
    'ESTADO INICIAL' as paso,
    COUNT(*) as total_records,
    COUNT(DISTINCT student_id) as unique_students,
    COUNT(DISTINCT lesson_id) as unique_lessons
FROM wpic_asg_progress;

-- PASO 1: VERIFICAR SI COLUMNA course_code EXISTE
-- ===============================================
SELECT
    'PASO 1: VERIFICACIÓN COLUMNA' as paso,
    COUNT(*) as column_exists,
    CASE
        WHEN COUNT(*) > 0 THEN 'COLUMNA YA EXISTE - SALTAR PASO 2'
        ELSE 'COLUMNA NO EXISTE - EJECUTAR PASO 2'
    END as accion_requerida
FROM INFORMATION_SCHEMA.COLUMNS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'wpic_asg_progress'
AND COLUMN_NAME = 'course_code';

-- PASO 2: AGREGAR COLUMNA course_code (Solo si no existe)
-- =======================================================
-- EJECUTAR SOLO SI EL PASO 1 MOSTRÓ "COLUMNA NO EXISTE"

ALTER TABLE wpic_asg_progress
ADD COLUMN course_code VARCHAR(100) NULL AFTER lesson_id;

SELECT 'PASO 2: COLUMNA course_code AGREGADA EXITOSAMENTE' as resultado;

-- PASO 3: CREAR ÍNDICES OPTIMIZADOS
-- ==================================
SELECT 'PASO 3: CREANDO ÍNDICES OPTIMIZADOS' as paso;

-- Índices básicos
CREATE INDEX IF NOT EXISTS idx_student_lesson ON wpic_asg_progress (student_id, lesson_id);
CREATE INDEX IF NOT EXISTS idx_student_completed ON wpic_asg_progress (student_id, completed);
CREATE INDEX IF NOT EXISTS idx_completion_date ON wpic_asg_progress (completion_date);

-- Índices para course_code
CREATE INDEX IF NOT EXISTS idx_student_course ON wpic_asg_progress (student_id, course_code);
CREATE INDEX IF NOT EXISTS idx_student_course_completed ON wpic_asg_progress (student_id, course_code, completed);
CREATE INDEX IF NOT EXISTS idx_course_code ON wpic_asg_progress (course_code);

SELECT 'PASO 3: ÍNDICES CREADOS EXITOSAMENTE' as resultado;

-- PASO 4: POBLAR course_code EN REGISTROS EXISTENTES
-- ===================================================
SELECT 'PASO 4: POBLANDO course_code EN REGISTROS EXISTENTES' as paso;

-- Verificar registros sin course_code antes de actualizar
SELECT
    'ANTES DE ACTUALIZAR' as momento,
    COUNT(*) as registros_sin_course_code
FROM wpic_asg_progress
WHERE course_code IS NULL OR course_code = '';

-- Actualizar registros existentes
UPDATE wpic_asg_progress p
JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
JOIN wpic_modules m ON l.code_module = m.code_module
SET p.course_code = m.code_course,
    p.updated_at = CURRENT_TIMESTAMP
WHERE (p.course_code IS NULL OR p.course_code = '');

-- Verificar resultado de la actualización
SELECT
    'DESPUÉS DE ACTUALIZAR' as momento,
    COUNT(*) as registros_sin_course_code,
    CASE
        WHEN COUNT(*) = 0 THEN '✅ TODOS LOS REGISTROS ACTUALIZADOS'
        ELSE CONCAT('⚠️ ', COUNT(*), ' REGISTROS SIN ACTUALIZAR (posibles datos huérfanos)')
    END as estado
FROM wpic_asg_progress
WHERE course_code IS NULL OR course_code = '';

SELECT 'PASO 4: POBLADO DE course_code COMPLETADO' as resultado;

-- PASO 5: VERIFICACIÓN FINAL Y ESTADÍSTICAS
-- ==========================================
SELECT 'PASO 5: VERIFICACIÓN FINAL' as paso;

-- Estadísticas completas
SELECT
    'ESTADÍSTICAS FINALES' as categoria,
    COUNT(*) as total_records,
    COUNT(CASE WHEN course_code IS NOT NULL AND course_code != '' THEN 1 END) as with_course_code,
    COUNT(CASE WHEN course_code IS NULL OR course_code = '' THEN 1 END) as missing_course_code,
    COUNT(DISTINCT student_id) as unique_students,
    COUNT(DISTINCT course_code) as unique_courses,
    COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons,
    ROUND((COUNT(CASE WHEN course_code IS NOT NULL AND course_code != '' THEN 1 END) / COUNT(*)) * 100, 2) as optimization_percentage
FROM wpic_asg_progress;

-- Estadísticas por curso
SELECT
    'ESTADÍSTICAS POR CURSO' as categoria,
    course_code,
    COUNT(*) as total_lessons,
    COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons,
    COUNT(DISTINCT student_id) as unique_students,
    ROUND((COUNT(CASE WHEN completed = 1 THEN 1 END) / COUNT(*)) * 100, 1) as completion_rate
FROM wpic_asg_progress
WHERE course_code IS NOT NULL AND course_code != ''
GROUP BY course_code
ORDER BY total_lessons DESC
LIMIT 10;

-- Verificar índices creados
SELECT
    'ÍNDICES CREADOS' as categoria,
    Key_name as index_name,
    Column_name as column_name,
    CASE WHEN Non_unique = 0 THEN 'UNIQUE' ELSE 'INDEX' END as index_type
FROM INFORMATION_SCHEMA.STATISTICS
WHERE TABLE_SCHEMA = DATABASE()
AND TABLE_NAME = 'wpic_asg_progress'
AND Key_name LIKE 'idx_%'
ORDER BY Key_name, Seq_in_index;

-- PASO 6: PRUEBA DE PERFORMANCE (OPCIONAL)
-- =========================================
SELECT 'PASO 6: PRUEBA DE PERFORMANCE' as paso;

-- Obtener datos de muestra para prueba
SELECT
    'DATOS DE PRUEBA' as categoria,
    student_id,
    course_code,
    COUNT(*) as lesson_count
FROM wpic_asg_progress
WHERE course_code IS NOT NULL AND course_code != ''
GROUP BY student_id, course_code
HAVING COUNT(*) > 1
LIMIT 1;

-- CONSULTA OPTIMIZADA (usar los datos de la consulta anterior)
-- Reemplazar 1 y 'COURSE_CODE_EJEMPLO' con valores reales de la consulta anterior
/*
SELECT
    'CONSULTA OPTIMIZADA' as tipo,
    COUNT(*) as total_lessons,
    SUM(completed) as completed_lessons,
    ROUND((SUM(completed) / COUNT(*)) * 100, 1) as progress_percentage
FROM wpic_asg_progress
WHERE student_id = 1 AND course_code = 'COURSE_CODE_EJEMPLO';
*/

-- FINALIZACIÓN
-- =============
SELECT
    '🎉 OPTIMIZACIÓN COMPLETADA EXITOSAMENTE' as mensaje,
    NOW() as fecha_completado,
    'Los endpoints automáticamente serán 80% más rápidos' as beneficio;

-- INSTRUCCIONES FINALES
SELECT
    'PRÓXIMOS PASOS' as categoria,
    '1. Verificar que todos los pasos se ejecutaron sin errores' as paso_1,
    '2. Los endpoints existentes ya están optimizados automáticamente' as paso_2,
    '3. Nuevos registros se crearán con course_code automáticamente' as paso_3,
    '4. Monitorear performance de consultas' as paso_4;
