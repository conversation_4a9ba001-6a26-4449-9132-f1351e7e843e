# 🚀 ASG API Endpoints - Guía de Testing

## 🔗 **BASE URL**
```
https://abilityseminarsgroup.com/wp-json/asg/v1/
```

---

## 📚 **1. VERIFICAR INSCRIPCIÓN**

### **GET** `/check-enrollment`

**Descripción:** Verifica si el usuario actual está inscrito en un curso específico.

**Parámetros:**
- `course` (string, required): Código del curso (ej: `course_curso_de_ajedrez_1751930947`)

**Ejemplo de uso:**
```bash
curl "https://abilityseminarsgroup.com/wp-json/asg/v1/check-enrollment?course=course_curso_de_ajedrez_1751930947"
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "enrolled": true,
  "user_info": {
    "user_id": 1,
    "username": "admin",
    "display_name": "Admin User"
  }
}
```

**Respuesta no inscrito:**
```json
{
  "success": true,
  "enrolled": false,
  "reason": "Usuario no inscrito en el curso"
}
```

---

## 🎓 **2. MIS CURSOS (DASHBOARD)**

### **GET** `/my-courses`

**Descripción:** Obtiene todos los cursos inscritos del usuario con progreso detallado.

**Autenticación:** Requerida (usuario logueado)

**Ejemplo de uso:**
```bash
curl -H "Authorization: Bearer YOUR_TOKEN" \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/my-courses"
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "data": {
    "user_info": {
      "display_name": "John Doe",
      "email": "<EMAIL>",
      "username": "johndoe"
    },
    "courses": [
      {
        "id_course": "1",
        "code_course": "course_curso_de_ajedrez_1751930947",
        "name_course": "Curso de Ajedrez",
        "price_course": "99.00",
        "amount_paid": "99.00",
        "payment_date": "2025-07-09 11:29:38",
        "total_lessons": 6,
        "completed_lessons": 3,
        "progress_percentage": 50.0,
        "completed_lessons_list": [51, 52, 57],
        "unlocked_lessons_list": [51, 52, 57, 58],
        "preview_lessons_list": [51, 57],
        "all_lessons_list": [51, 52, 57, 58, 59, 60]
      }
    ],
    "stats": {
      "total_courses": 1,
      "total_spent": "99.00",
      "total_lessons_completed": 3,
      "total_lessons_enrolled": 6
    }
  }
}
```

---

## ✅ **3. COMPLETAR LECCIÓN**

### **POST** `/lesson/{lesson_id}/complete`

**Descripción:** Marca una lección específica como completada.

**Autenticación:** Requerida (usuario logueado)

**Parámetros de URL:**
- `lesson_id` (integer, required): ID de la lección

**Body (JSON):**
```json
{
  "course": "course_curso_de_ajedrez_1751930947",
  "lesson_id": 52
}
```

**Ejemplo de uso:**
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"course":"course_curso_de_ajedrez_1751930947","lesson_id":52}' \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/lesson/52/complete"
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "message": "Lección completada exitosamente",
  "data": {
    "lesson_id": 52,
    "lesson_title": "Movimientos Básicos",
    "course_progress": {
      "total_lessons": "6",
      "completed_lessons": "4",
      "progress_percentage": "66.7"
    }
  }
}
```

**Respuesta de error:**
```json
{
  "success": false,
  "error": "No tienes acceso a esta lección"
}
```

---

## 🔄 **4. INICIALIZAR PROGRESO**

### **POST** `/initialize-progress`

**Descripción:** Crea registros iniciales de progreso para todas las lecciones de un curso.

**Autenticación:** Requerida (usuario logueado)

**Body (JSON):**
```json
{
  "course": "course_curso_de_ajedrez_1751930947"
}
```

**Ejemplo de uso:**
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"course":"course_curso_de_ajedrez_1751930947"}' \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/initialize-progress"
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "message": "Progreso inicializado correctamente",
  "data": {
    "course_code": "course_curso_de_ajedrez_1751930947",
    "total_lessons": 6,
    "initialized": 6,
    "already_existed": 0
  }
}
```

---

## 💳 **5. PROCESAR INSCRIPCIÓN**

### **POST** `/process-enrollment`

**Descripción:** Procesa la inscripción de un usuario a un curso después del pago.

**Autenticación:** Requerida (usuario logueado)

**Body (JSON):**
```json
{
  "course_code": "course_curso_de_ajedrez_1751930947",
  "paypal_payment_id": "PAYID-123456789",
  "amount_paid": "99.00"
}
```

**Ejemplo de uso:**
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -d '{"course_code":"course_curso_de_ajedrez_1751930947","paypal_payment_id":"PAYID-123456789","amount_paid":"99.00"}' \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/process-enrollment"
```

**Respuesta exitosa:**
```json
{
  "success": true,
  "message": "Inscripción procesada exitosamente",
  "data": {
    "enrollment_id": 15,
    "course_name": "Curso de Ajedrez",
    "amount_paid": "99.00",
    "payment_date": "2025-07-09 12:00:00"
  }
}
```

---

## 🧪 **EJEMPLOS DE TESTING CON CURL**

### **Test 1: Verificar si usuario está inscrito**
```bash
curl "https://abilityseminarsgroup.com/wp-json/asg/v1/check-enrollment?course=course_curso_de_ajedrez_1751930947"
```

### **Test 2: Ver cursos del usuario (requiere login)**
```bash
curl -b "wordpress_cookies.txt" \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/my-courses"
```

### **Test 3: Completar lección 52**
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -b "wordpress_cookies.txt" \
     -d '{"course":"course_curso_de_ajedrez_1751930947","lesson_id":52}' \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/lesson/52/complete"
```

### **Test 4: Inicializar progreso para curso**
```bash
curl -X POST \
     -H "Content-Type: application/json" \
     -b "wordpress_cookies.txt" \
     -d '{"course":"course_curso_de_ajedrez_1751930947"}' \
     "https://abilityseminarsgroup.com/wp-json/asg/v1/initialize-progress"
```

---

## 🔍 **TESTING CON POSTMAN**

### **Configuración:**
1. **Base URL:** `https://abilityseminarsgroup.com/wp-json/asg/v1`
2. **Headers:** 
   - `Content-Type: application/json`
   - `X-WP-Nonce: [obtener del meta tag]`

### **Autenticación:**
- Usar cookies de WordPress o JWT token
- Para testing rápido, loguearse en WordPress primero

---

## 📊 **CÓDIGOS DE RESPUESTA**

- **200:** Éxito
- **400:** Parámetros inválidos
- **401:** No autenticado
- **403:** Sin permisos
- **404:** Recurso no encontrado
- **500:** Error del servidor

---

## 🐛 **DEBUGGING**

### **Verificar logs:**
```bash
tail -f /path/to/wordpress/wp-content/debug.log
```

### **Verificar base de datos:**
```sql
-- Ver inscripciones
SELECT * FROM wpic_asg_enrollments WHERE student_id = 1;

-- Ver progreso
SELECT * FROM wpic_asg_progress WHERE student_id = 1;

-- Ver estudiantes
SELECT * FROM wpic_asg_students WHERE id_user = 1;
```

### **Testing JavaScript (DevTools Console):**
```javascript
// Verificar inscripción
fetch('/wp-json/asg/v1/check-enrollment?course=course_curso_de_ajedrez_1751930947')
  .then(r => r.json())
  .then(console.log);

// Completar lección
fetch('/wp-json/asg/v1/lesson/52/complete', {
  method: 'POST',
  headers: {'Content-Type': 'application/json'},
  body: JSON.stringify({course: 'course_curso_de_ajedrez_1751930947', lesson_id: 52})
}).then(r => r.json()).then(console.log);
```
