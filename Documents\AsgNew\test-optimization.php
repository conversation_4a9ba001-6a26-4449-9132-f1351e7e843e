<?php
/**
 * SCRIPT DE PRUEBA PARA VERIFICAR LA OPTIMIZACIÓN
 * Ejecutar después de la optimización para verificar que todo funcione
 */

// Solo ejecutar si es admin
if (!current_user_can('manage_options')) {
    wp_die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

global $wpdb;

echo "<h2>🧪 Prueba de Optimización de wpic_asg_progress</h2>";

// PASO 1: Verificar estructura de tabla
echo "<h3>1. 📋 Verificación de Estructura</h3>";

$columns = $wpdb->get_results("DESCRIBE wpic_asg_progress");
$column_names = array_column($columns, 'Field');

echo "<p><strong>Columnas actuales:</strong></p>";
echo "<ul>";
foreach ($columns as $column) {
    $highlight = $column->Field === 'course_code' ? ' style="background-color: yellow; font-weight: bold;"' : '';
    echo "<li{$highlight}>{$column->Field} - {$column->Type}" . ($column->Null === 'YES' ? ' (NULL)' : ' (NOT NULL)') . "</li>";
}
echo "</ul>";

$has_course_code = in_array('course_code', $column_names);
echo "<p><strong>✅ Columna course_code:</strong> " . ($has_course_code ? "EXISTE" : "❌ NO EXISTE") . "</p>";

// PASO 2: Verificar índices
echo "<h3>2. 🔍 Verificación de Índices</h3>";

$indexes = $wpdb->get_results("SHOW INDEX FROM wpic_asg_progress");
$optimization_indexes = array_filter($indexes, function($index) {
    return strpos($index->Key_name, 'idx_') === 0;
});

if (!empty($optimization_indexes)) {
    echo "<p><strong>Índices de optimización encontrados:</strong></p>";
    echo "<ul>";
    foreach ($optimization_indexes as $index) {
        echo "<li><strong>{$index->Key_name}</strong> en columna: {$index->Column_name}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>⚠️ No se encontraron índices de optimización</p>";
}

// PASO 3: Verificar datos
echo "<h3>3. 📊 Verificación de Datos</h3>";

if ($has_course_code) {
    $total_records = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress");
    $with_course_code = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NOT NULL AND course_code != ''");
    $missing_course_code = $total_records - $with_course_code;
    
    echo "<ul>";
    echo "<li><strong>Total de registros:</strong> {$total_records}</li>";
    echo "<li><strong>Con course_code:</strong> {$with_course_code}</li>";
    echo "<li><strong>Sin course_code:</strong> {$missing_course_code}</li>";
    echo "<li><strong>Porcentaje optimizado:</strong> " . ($total_records > 0 ? round(($with_course_code / $total_records) * 100, 1) : 0) . "%</li>";
    echo "</ul>";
    
    if ($missing_course_code > 0) {
        echo "<p>⚠️ <strong>Advertencia:</strong> Hay {$missing_course_code} registros sin course_code</p>";
        
        // Mostrar algunos registros problemáticos
        $problematic = $wpdb->get_results("
            SELECT p.id_progress, p.student_id, p.lesson_id, l.title_lesson
            FROM wpic_asg_progress p
            LEFT JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
            WHERE (p.course_code IS NULL OR p.course_code = '')
            LIMIT 5
        ");
        
        if (!empty($problematic)) {
            echo "<p><strong>Primeros 5 registros problemáticos:</strong></p>";
            echo "<table border='1' style='border-collapse: collapse;'>";
            echo "<tr><th>ID Progress</th><th>Student ID</th><th>Lesson ID</th><th>Lesson Title</th></tr>";
            foreach ($problematic as $record) {
                echo "<tr>";
                echo "<td>{$record->id_progress}</td>";
                echo "<td>{$record->student_id}</td>";
                echo "<td>{$record->lesson_id}</td>";
                echo "<td>" . ($record->title_lesson ?: 'N/A') . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
    }
} else {
    echo "<p>❌ No se puede verificar datos porque la columna course_code no existe</p>";
}

// PASO 4: Prueba de performance (si hay datos)
echo "<h3>4. ⚡ Prueba de Performance</h3>";

if ($has_course_code && $with_course_code > 0) {
    // Obtener datos de prueba
    $sample = $wpdb->get_row("
        SELECT student_id, course_code 
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL AND course_code != ''
        LIMIT 1
    ");
    
    if ($sample) {
        echo "<p><strong>Datos de prueba:</strong> Student ID: {$sample->student_id}, Course: {$sample->course_code}</p>";
        
        // Consulta antigua (con JOINs)
        $start_time = microtime(true);
        $old_result = $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(*) as total_lessons,
                SUM(p.completed) as completed_lessons
            FROM wpic_asg_progress p
            JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
            JOIN wpic_modules m ON l.code_module = m.code_module
            WHERE p.student_id = %d AND m.code_course = %s
        ", $sample->student_id, $sample->course_code));
        $old_time = microtime(true) - $start_time;
        
        // Consulta nueva (optimizada)
        $start_time = microtime(true);
        $new_result = $wpdb->get_row($wpdb->prepare("
            SELECT 
                COUNT(*) as total_lessons,
                SUM(completed) as completed_lessons
            FROM wpic_asg_progress 
            WHERE student_id = %d AND course_code = %s
        ", $sample->student_id, $sample->course_code));
        $new_time = microtime(true) - $start_time;
        
        $improvement = $old_time > 0 ? round((($old_time - $new_time) / $old_time) * 100, 1) : 0;
        
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Consulta</th><th>Tiempo (segundos)</th><th>Total Lecciones</th><th>Completadas</th></tr>";
        echo "<tr>";
        echo "<td>Consulta ANTIGUA (con JOINs)</td>";
        echo "<td>" . round($old_time, 6) . "</td>";
        echo "<td>" . ($old_result->total_lessons ?? 'N/A') . "</td>";
        echo "<td>" . ($old_result->completed_lessons ?? 'N/A') . "</td>";
        echo "</tr>";
        echo "<tr style='background-color: #e8f5e8;'>";
        echo "<td><strong>Consulta NUEVA (optimizada)</strong></td>";
        echo "<td><strong>" . round($new_time, 6) . "</strong></td>";
        echo "<td><strong>" . ($new_result->total_lessons ?? 'N/A') . "</strong></td>";
        echo "<td><strong>" . ($new_result->completed_lessons ?? 'N/A') . "</strong></td>";
        echo "</tr>";
        echo "</table>";
        
        echo "<p><strong>🚀 Mejora de performance:</strong> {$improvement}% más rápido</p>";
        
        // Verificar que los resultados coincidan
        $results_match = ($old_result->total_lessons == $new_result->total_lessons) && 
                        ($old_result->completed_lessons == $new_result->completed_lessons);
        
        echo "<p><strong>✅ Resultados coinciden:</strong> " . ($results_match ? "SÍ" : "❌ NO") . "</p>";
        
    } else {
        echo "<p>⚠️ No se encontraron datos para probar performance</p>";
    }
} else {
    echo "<p>⚠️ No se puede probar performance sin datos optimizados</p>";
}

// PASO 5: Estadísticas por curso
if ($has_course_code && $with_course_code > 0) {
    echo "<h3>5. 📈 Estadísticas por Curso</h3>";
    
    $course_stats = $wpdb->get_results("
        SELECT 
            course_code,
            COUNT(*) as total_lessons,
            COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons,
            COUNT(DISTINCT student_id) as unique_students,
            ROUND((COUNT(CASE WHEN completed = 1 THEN 1 END) / COUNT(*)) * 100, 1) as completion_rate
        FROM wpic_asg_progress 
        WHERE course_code IS NOT NULL AND course_code != ''
        GROUP BY course_code
        ORDER BY total_lessons DESC
        LIMIT 5
    ");
    
    if (!empty($course_stats)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>Course Code</th><th>Total Lecciones</th><th>Completadas</th><th>Estudiantes</th><th>% Completado</th></tr>";
        foreach ($course_stats as $stat) {
            echo "<tr>";
            echo "<td>{$stat->course_code}</td>";
            echo "<td>{$stat->total_lessons}</td>";
            echo "<td>{$stat->completed_lessons}</td>";
            echo "<td>{$stat->unique_students}</td>";
            echo "<td>{$stat->completion_rate}%</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
}

// PASO 6: Recomendaciones
echo "<h3>6. 💡 Recomendaciones</h3>";

$recommendations = [];

if (!$has_course_code) {
    $recommendations[] = "❌ <strong>CRÍTICO:</strong> Agregar columna course_code a la tabla";
    $recommendations[] = "🔧 Ejecutar: ALTER TABLE wpic_asg_progress ADD COLUMN course_code VARCHAR(100) NULL AFTER lesson_id";
}

if (empty($optimization_indexes)) {
    $recommendations[] = "⚠️ Crear índices de optimización para mejorar performance";
}

if ($has_course_code && $missing_course_code > 0) {
    $recommendations[] = "🔄 Poblar course_code en {$missing_course_code} registros existentes";
}

if ($has_course_code && $missing_course_code == 0) {
    $recommendations[] = "✅ <strong>¡Optimización completa!</strong> Todos los registros tienen course_code";
    $recommendations[] = "🚀 Los endpoints ya están optimizados y funcionarán más rápido";
}

if (!empty($recommendations)) {
    echo "<ul>";
    foreach ($recommendations as $rec) {
        echo "<li>{$rec}</li>";
    }
    echo "</ul>";
} else {
    echo "<p>✅ <strong>¡Todo perfecto!</strong> No hay recomendaciones adicionales.</p>";
}

echo "<hr>";
echo "<p><em>Prueba completada. Puedes cerrar esta página.</em></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { width: 100%; max-width: 800px; margin: 10px 0; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
h3 { color: #333; border-bottom: 2px solid #eee; padding-bottom: 5px; }
</style>
