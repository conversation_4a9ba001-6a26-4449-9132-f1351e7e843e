-- =====================================================
-- MIGRACIÓN COMPLETA A NUEVA ESTRUCTURA OPTIMIZADA
-- wpic_asg_progress → wpic_asg_user_progress
-- =====================================================

-- INFORMACIÓN INICIAL
SELECT 'INICIANDO MIGRACIÓN A NUEVA ESTRUCTURA OPTIMIZADA' as mensaje, NOW() as fecha_hora;

-- PASO 1: VERIFICAR DATOS ACTUALES
-- =================================
SELECT 'PASO 1: VERIFICANDO DATOS ACTUALES' as paso;

SELECT 
    'ESTADO ACTUAL wpic_asg_progress' as tabla,
    COUNT(*) as total_records,
    COUNT(DISTINCT student_id) as unique_students,
    COUNT(DISTINCT lesson_id) as unique_lessons,
    COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_lessons
FROM wpic_asg_progress;

-- Verificar distribución por estudiante y curso
SELECT 
    'DISTRIBUCIÓN POR ESTUDIANTE-CURSO' as categoria,
    p.student_id,
    m.code_course,
    COUNT(*) as total_lessons,
    COUNT(CASE WHEN p.completed = 1 THEN 1 END) as completed_lessons,
    ROUND((COUNT(CASE WHEN p.completed = 1 THEN 1 END) / COUNT(*)) * 100, 2) as progress_percentage
FROM wpic_asg_progress p
JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
JOIN wpic_modules mod ON l.code_module = mod.code_module
JOIN wpic_courses m ON mod.code_course = m.code_course
GROUP BY p.student_id, m.code_course
ORDER BY p.student_id, total_lessons DESC
LIMIT 10;

-- PASO 2: CREAR NUEVA TABLA OPTIMIZADA
-- ====================================
SELECT 'PASO 2: CREANDO NUEVA TABLA wpic_asg_user_progress' as paso;

CREATE TABLE IF NOT EXISTS wpic_asg_user_progress (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    course_code VARCHAR(100) NOT NULL,
    completed_lessons JSON NOT NULL,           -- [51, 52, 57, 58, 59, 60]
    total_lessons INT NOT NULL,
    completed_count INT NOT NULL,
    progress_percentage DECIMAL(5,2) NOT NULL,
    last_lesson_id INT NULL,
    last_completed_at TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_user_course (user_id, course_code),
    INDEX idx_user_id (user_id),
    INDEX idx_course_code (course_code),
    INDEX idx_progress (progress_percentage)
);

SELECT 'NUEVA TABLA wpic_asg_user_progress CREADA EXITOSAMENTE' as resultado;

-- PASO 3: MIGRAR DATOS DE LA TABLA ANTIGUA A LA NUEVA
-- ===================================================
SELECT 'PASO 3: MIGRANDO DATOS A NUEVA ESTRUCTURA' as paso;

INSERT INTO wpic_asg_user_progress (
    user_id,
    course_code,
    completed_lessons,
    total_lessons,
    completed_count,
    progress_percentage,
    last_lesson_id,
    last_completed_at,
    created_at,
    updated_at
)
SELECT 
    p.student_id as user_id,
    m.code_course as course_code,
    CONCAT('[', GROUP_CONCAT(
        CASE WHEN p.completed = 1 THEN p.lesson_id END 
        ORDER BY p.lesson_id SEPARATOR ','
    ), ']') as completed_lessons,
    COUNT(*) as total_lessons,
    COUNT(CASE WHEN p.completed = 1 THEN 1 END) as completed_count,
    ROUND((COUNT(CASE WHEN p.completed = 1 THEN 1 END) / COUNT(*)) * 100, 2) as progress_percentage,
    MAX(CASE WHEN p.completed = 1 THEN p.lesson_id END) as last_lesson_id,
    MAX(CASE WHEN p.completed = 1 THEN p.completion_date END) as last_completed_at,
    MIN(p.created_at) as created_at,
    MAX(p.updated_at) as updated_at
FROM wpic_asg_progress p
JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
JOIN wpic_modules mod ON l.code_module = mod.code_module
JOIN wpic_courses m ON mod.code_course = m.code_course
GROUP BY p.student_id, m.code_course;

SELECT 'MIGRACIÓN DE DATOS COMPLETADA' as resultado;

-- PASO 4: VERIFICAR MIGRACIÓN
-- ===========================
SELECT 'PASO 4: VERIFICANDO MIGRACIÓN' as paso;

-- Comparar registros
SELECT 
    'COMPARACIÓN DE REGISTROS' as categoria,
    (SELECT COUNT(*) FROM wpic_asg_progress) as registros_originales,
    (SELECT COUNT(DISTINCT CONCAT(student_id, '-', 
        (SELECT m.code_course 
         FROM wpic_lessons l 
         JOIN wpic_modules mod ON l.code_module = mod.code_module 
         JOIN wpic_courses m ON mod.code_course = m.code_course 
         WHERE l.id_lesson = p.lesson_id LIMIT 1)
    )) FROM wpic_asg_progress p) as combinaciones_usuario_curso,
    (SELECT COUNT(*) FROM wpic_asg_user_progress) as registros_nuevos;

-- Verificar datos migrados
SELECT 
    'VERIFICACIÓN DATOS MIGRADOS' as categoria,
    user_id,
    course_code,
    total_lessons,
    completed_count,
    progress_percentage,
    JSON_LENGTH(completed_lessons) as lessons_in_json,
    last_lesson_id,
    last_completed_at
FROM wpic_asg_user_progress
ORDER BY user_id, total_lessons DESC
LIMIT 10;

-- PASO 5: CREAR TABLA DE RESPALDO (OPCIONAL)
-- ==========================================
SELECT 'PASO 5: CREANDO RESPALDO DE TABLA ORIGINAL' as paso;

CREATE TABLE wpic_asg_progress_backup AS 
SELECT * FROM wpic_asg_progress;

SELECT 
    'RESPALDO CREADO' as resultado,
    COUNT(*) as registros_respaldados
FROM wpic_asg_progress_backup;

-- PASO 6: ELIMINAR TABLA ORIGINAL (COMENTADO POR SEGURIDAD)
-- =========================================================
SELECT 'PASO 6: PREPARADO PARA ELIMINAR TABLA ORIGINAL' as paso;

-- ⚠️ DESCOMENTA SOLO CUANDO ESTÉS SEGURO DE QUE LA MIGRACIÓN ES CORRECTA
-- DROP TABLE wpic_asg_progress;

SELECT 'TABLA ORIGINAL AÚN EXISTE - ELIMINAR MANUALMENTE CUANDO CONFIRMES QUE TODO FUNCIONA' as advertencia;

-- PASO 7: ESTADÍSTICAS FINALES
-- ============================
SELECT 'PASO 7: ESTADÍSTICAS FINALES' as paso;

SELECT 
    'ESTADÍSTICAS NUEVA TABLA' as categoria,
    COUNT(*) as total_registros,
    COUNT(DISTINCT user_id) as usuarios_unicos,
    COUNT(DISTINCT course_code) as cursos_unicos,
    AVG(progress_percentage) as progreso_promedio,
    SUM(completed_count) as total_lecciones_completadas,
    SUM(total_lessons) as total_lecciones_asignadas
FROM wpic_asg_user_progress;

-- Distribución de progreso
SELECT 
    'DISTRIBUCIÓN DE PROGRESO' as categoria,
    CASE 
        WHEN progress_percentage = 0 THEN '0% - Sin empezar'
        WHEN progress_percentage < 25 THEN '1-24% - Iniciado'
        WHEN progress_percentage < 50 THEN '25-49% - En progreso'
        WHEN progress_percentage < 75 THEN '50-74% - Avanzado'
        WHEN progress_percentage < 100 THEN '75-99% - Casi completo'
        ELSE '100% - Completado'
    END as rango_progreso,
    COUNT(*) as cantidad_usuarios
FROM wpic_asg_user_progress
GROUP BY 
    CASE 
        WHEN progress_percentage = 0 THEN '0% - Sin empezar'
        WHEN progress_percentage < 25 THEN '1-24% - Iniciado'
        WHEN progress_percentage < 50 THEN '25-49% - En progreso'
        WHEN progress_percentage < 75 THEN '50-74% - Avanzado'
        WHEN progress_percentage < 100 THEN '75-99% - Casi completo'
        ELSE '100% - Completado'
    END
ORDER BY 
    CASE 
        WHEN progress_percentage = 0 THEN 1
        WHEN progress_percentage < 25 THEN 2
        WHEN progress_percentage < 50 THEN 3
        WHEN progress_percentage < 75 THEN 4
        WHEN progress_percentage < 100 THEN 5
        ELSE 6
    END;

-- PASO 8: CONSULTAS DE PRUEBA CON NUEVA ESTRUCTURA
-- ================================================
SELECT 'PASO 8: EJEMPLOS DE CONSULTAS OPTIMIZADAS' as paso;

-- Ejemplo 1: Progreso de un usuario específico
SELECT 
    'PROGRESO USUARIO ESPECÍFICO' as consulta,
    user_id,
    course_code,
    progress_percentage,
    completed_count,
    total_lessons,
    completed_lessons
FROM wpic_asg_user_progress 
WHERE user_id = (SELECT MIN(user_id) FROM wpic_asg_user_progress)
ORDER BY progress_percentage DESC;

-- Ejemplo 2: Top usuarios por progreso
SELECT 
    'TOP USUARIOS POR PROGRESO' as consulta,
    user_id,
    COUNT(*) as cursos_inscritos,
    AVG(progress_percentage) as progreso_promedio,
    SUM(completed_count) as total_lecciones_completadas
FROM wpic_asg_user_progress
GROUP BY user_id
ORDER BY progreso_promedio DESC, total_lecciones_completadas DESC
LIMIT 5;

-- Ejemplo 3: Estadísticas por curso
SELECT 
    'ESTADÍSTICAS POR CURSO' as consulta,
    course_code,
    COUNT(*) as estudiantes_inscritos,
    AVG(progress_percentage) as progreso_promedio,
    COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) as estudiantes_completaron,
    ROUND((COUNT(CASE WHEN progress_percentage = 100 THEN 1 END) / COUNT(*)) * 100, 1) as tasa_completado
FROM wpic_asg_user_progress
GROUP BY course_code
ORDER BY estudiantes_inscritos DESC;

-- FINALIZACIÓN
-- ============
SELECT 
    '🎉 MIGRACIÓN COMPLETADA EXITOSAMENTE' as mensaje,
    NOW() as fecha_completado,
    'Nueva estructura optimizada lista para usar' as estado;

-- PRÓXIMOS PASOS
SELECT 
    'PRÓXIMOS PASOS' as seccion,
    '1. Verificar que todos los datos se migraron correctamente' as paso_1,
    '2. Actualizar endpoints para usar wpic_asg_user_progress' as paso_2,
    '3. Probar funcionalidad completa' as paso_3,
    '4. Eliminar tabla original cuando confirmes que todo funciona' as paso_4,
    '5. Performance esperado: 95% más rápido que la estructura anterior' as beneficio;
