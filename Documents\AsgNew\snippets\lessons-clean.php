/**
 * ASG Lessons System - Versión Limpia y Refactorizada
 * Sistema de lecciones para AbilitySeminarsGroup
 * 
 * Funcionalidades:
 * - Carga de lecciones (video/quiz)
 * - Sistema de progreso con persistencia
 * - Control de acceso y enrollment
 * - Marcado visual de lecciones completadas
 * - Navegación secuencial con bloqueo
 */

// Verificar que estamos en WordPress
if (!defined('ABSPATH')) {
    exit;
}

// Configuración y variables globales
$site_url = get_site_url();
$course_code = isset($_GET['course']) ? sanitize_text_field($_GET['course']) : '';
$lesson_id = isset($_GET['lesson']) ? sanitize_text_field($_GET['lesson']) : '';
$auto_load_first = !$lesson_id && $course_code;

// Debug: verificar parámetros recibidos
error_log('ASG Lessons Debug - Course: ' . $course_code . ', Lesson: ' . $lesson_id);

// Verificar acceso del usuario
$current_user = wp_get_current_user();
$user_id = $current_user->ID;
$access_denied = false;
$access_message = '';

// Verificar enrollment si el usuario está logueado
if ($user_id && $course_code) {
    global $wpdb;
    $enrollment_check = $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM {$wpdb->prefix}asg_student_enrollments 
         WHERE id_user = %d AND code_course = %s AND status = 'active'",
        $user_id, $course_code
    ));
    
    if (!$enrollment_check) {
        $access_denied = true;
        $access_message = 'Necesitas estar inscrito en este curso para acceder a las lecciones.';
    }
} elseif (!$user_id) {
    $access_denied = true;
    $access_message = 'Debes iniciar sesión para acceder a las lecciones.';
}

function asg_render_lessons_page() {
    global $site_url, $course_code, $lesson_id, $auto_load_first, $access_denied, $access_message;
?>
<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="wp-nonce" content="<?php echo wp_create_nonce('wp_rest'); ?>">
    <title>Lecciones - <?php echo esc_html($course_code); ?></title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
    
    <style>
        :root {
            --primary-blue: #1e88e5;
            --primary-dark: #1565c0;
            --accent-yellow: #ffc107;
            --success-color: #28a745;
            --light-blue: #e3f2fd;
            --transition: all 0.3s ease;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, var(--light-blue) 0%, #f8f9fa 50%, #fff3e0 100%);
            min-height: 100vh;
        }

/*         /* Header flotante */
        .floating-header {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-bottom: 1px solid rgba(30, 136, 229, 0.1);
            z-index: 1000;
            padding: 1rem 0;
        }

      
        .course-title {
            font-size: 1.2rem;
            font-weight: 600;
            color: white;
            margin: 0;
        }

        .progress-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }

        .progress-container {
            display: flex;
            align-items: center;
            gap: 0.5rem;
        }

        .progress-bar-container {
            width: 200px;
            height: 8px;
            background: rgba(30, 136, 229, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--accent-yellow) 0%, #ffb300 100%);
            border-radius: 4px;
            transition: width 0.3s ease;
            width: 0%;
        }

        .progress-text {
            font-weight: 600;
            color: var(--primary-dark);
            min-width: 40px;
        }

        /* Layout principal */
        .lesson-layout {
            display: flex;
            margin-top: 10px;
            min-height: calc(100vh - 80px);
        }

        /* Sidebar */
        .lesson-sidebar {
            width: 400px;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(25px);
            border-radius: 24px;
            overflow: hidden;
            box-shadow: 0 8px 32px rgba(30, 136, 229, 0.12);
            margin: 1rem;
            max-height: calc(100vh - 100px);
            transition: var(--transition);
        }

        .lesson-sidebar:hover {
            box-shadow: 0 12px 40px rgba(30, 136, 229, 0.18);
            transform: translateY(-2px);
        }

        .sidebar-header {
            padding: 2rem;
            background: linear-gradient(135deg, var(--primary-blue) 0%, var(--primary-dark) 100%);
            color: white;
            position: relative;
            overflow: hidden;
        }

        .sidebar-header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            opacity: 0.3;
        }

        .sidebar-header h3 {
            position: relative;
            z-index: 2;
            margin-bottom: 1rem;
            font-weight: 700;
            font-size: 1.2rem;
        }

        .course-progress {
            position: relative;
            z-index: 2;
        }

        .sidebar-content {
            padding: 1rem;
            max-height: calc(100vh - 280px);
            overflow-y: auto;
        }

        .sidebar-content::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar-content::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 3px;
        }

        .sidebar-content::-webkit-scrollbar-thumb {
            background: var(--primary-blue);
            border-radius: 3px;
        }

        .sidebar-title {
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid var(--primary-blue);
        }

        /* Módulos */
        .module-item {
            margin-bottom: 1rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .module-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 1rem 1.5rem;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: var(--transition);
            border-bottom: 1px solid rgba(0, 0, 0, 0.1);
        }

        .module-header:hover {
            background: linear-gradient(135deg, #e9ecef 0%, #dee2e6 100%);
        }

        .module-title {
            font-weight: 600;
            color: var(--primary-dark);
            margin: 0;
            font-size: 1rem;
        }

        .module-chevron {
            transition: transform 0.3s ease;
            color: var(--primary-blue);
            font-size: 1.2rem;
        }

        .module-chevron.rotated {
            transform: rotate(90deg);
        }

        .module-lessons {
            background: white;
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease;
        }

        .module-lessons.show {
            max-height: 1000px;
        }

        .lessons-list {
            padding: 0;
        }

        .lesson-item {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            cursor: pointer;
            transition: var(--transition);
            border-bottom: 1px solid #f0f0f0;
            position: relative;
        }

        .lesson-item:hover {
            background: var(--light-blue);
            transform: translateX(3px);
        }

        .lesson-item.active {
            background: var(--light-blue);
            border-left: 4px solid var(--primary-dark);
        }

        .lesson-item.active::before {
            content: '';
            position: absolute;
            left: 0;
            top: 0;
            bottom: 0;
            width: 4px;
            background: var(--primary-dark);
        }

        /* Estados de lecciones */
        .lesson-item.preview-lesson {
            border-left: 3px solid var(--accent-yellow);
        }

        .lesson-item.preview-lesson .lesson-icon {
            color: var(--accent-yellow);
        }

        /* Sequential Learning System Styles */
        .lesson-item.locked-lesson {
            opacity: 0.4;
            pointer-events: none;
            border-left: 3px solid #dc3545;
            background: linear-gradient(90deg, rgba(220, 53, 69, 0.05) 0%, rgba(220, 53, 69, 0.1) 100%);
            cursor: not-allowed;
        }

        .lesson-item.locked-lesson .lesson-icon {
            color: #dc3545;
        }

        .lesson-item.unlocked-lesson {
            border-left: 3px solid var(--primary-blue);
            background: linear-gradient(90deg, rgba(13, 110, 253, 0.05) 0%, rgba(13, 110, 253, 0.1) 100%);
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .lesson-item.unlocked-lesson:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(13, 110, 253, 0.2);
        }

        .lesson-item.unlocked-lesson .lesson-icon {
            color: var(--primary-blue);
        }

        .lesson-item.completed {
            background: linear-gradient(90deg, rgba(40, 167, 69, 0.05) 0%, rgba(40, 167, 69, 0.15) 100%);
            border-left-color: var(--success-color);
            text-decoration: line-through;
            text-decoration-color: rgba(40, 167, 69, 0.6);
            opacity: 0.8;
        }

        .lesson-item.completed:hover {
            opacity: 1;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.2);
        }

        /* Lesson badges */
        .lesson-badge {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
            border-radius: 12px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .lesson-badge.bg-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
            color: white;
        }

        .lesson-badge.bg-primary {
            background: linear-gradient(135deg, #007bff 0%, #6610f2 100%) !important;
            color: white;
        }

        .lesson-badge.bg-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
            color: white;
        }

        .lesson-icon {
            margin-right: 0.75rem;
            font-size: 1.2rem;
        }

        .lesson-item.completed .lesson-icon {
            color: var(--success-color);
        }

        .lesson-title {
            flex: 1;
            font-weight: 500;
        }

        .completed-badge {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
            color: white;
            font-size: 0.7rem;
            padding: 0.2rem 0.5rem;
            border-radius: 12px;
            margin-left: 0.5rem;
        }

        /* Contenido principal */
        .lesson-content {
            flex: 1;
            padding: 2rem;
            background: white;
            margin: 1rem;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }

        .lesson-header {
            margin-bottom: 2rem;
            padding-bottom: 1rem;
            border-bottom: 1px solid #eee;
        }

        .lesson-type-badge {
            display: inline-block;
            padding: 0.3rem 0.8rem;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            margin-bottom: 1rem;
        }

        .lesson-type-video {
            background: linear-gradient(135deg, #2196f3 0%, var(--primary-blue) 100%);
            color: white;
        }

        .lesson-type-quiz {
            background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
            color: white;
        }

        .lesson-type-text {
            background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
            color: white;
        }

        .lesson-title-main {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--primary-dark);
            margin: 0;
        }

        /* Video container */
        .video-container {
            position: relative;
            width: 100%;
            height: 0;
            padding-bottom: 56.25%;
            margin-bottom: 2rem;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .video-container iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
        }

        /* Quiz styles */
        .quiz-container {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .quiz-question {
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--primary-dark);
            margin-bottom: 1.5rem;
        }

        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .quiz-option {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            padding: 1rem;
            cursor: pointer;
            transition: var(--transition);
        }

        .quiz-option:hover {
            border-color: var(--primary-blue);
            background: var(--light-blue);
        }

        .quiz-option.selected {
            border-color: var(--primary-blue);
            background: var(--light-blue);
        }

        .quiz-option.correct {
            border-color: var(--success-color);
            background: #d4edda;
        }

        .quiz-option.incorrect {
            border-color: #dc3545;
            background: #f8d7da;
        }

        /* Botones de acción */
        .lesson-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 1rem;
            border-top: 1px solid #eee;
        }

        .btn-complete {
            background: linear-gradient(135deg, var(--success-color) 0%, #20c997 100%);
            border: none;
            color: white;
            padding: 0.75rem 2rem;
            border-radius: 25px;
            font-weight: 600;
            transition: var(--transition);
        }

        .btn-complete:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.3);
        }

        .btn-complete:disabled {
            background: #6c757d;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-navigation {
            background: var(--primary-blue);
            border: none;
            color: white;
            padding: 0.5rem 1.5rem;
            border-radius: 20px;
            font-weight: 500;
            transition: var(--transition);
        }

        .btn-navigation:hover {
            background: var(--primary-dark);
            transform: translateY(-1px);
        }

        /* Estados de carga */
        .loading-state {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 300px;
            flex-direction: column;
            gap: 1rem;
        }

        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid var(--light-blue);
            border-top: 4px solid var(--primary-blue);
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Quiz Styles */
        .quiz-lesson-container {
            max-width: 800px;
            margin: 0 auto;
        }

        .quiz-intro {
            text-align: center;
            padding: 3rem 2rem;
            background: linear-gradient(135deg, var(--light-blue) 0%, white 100%);
            border-radius: 20px;
            margin-bottom: 2rem;
        }

        .quiz-intro-content h3 {
            color: var(--primary-dark);
            margin-bottom: 1rem;
            font-size: 2rem;
        }

        .quiz-description {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        .quiz-stats {
            display: flex;
            justify-content: center;
            gap: 2rem;
            margin-bottom: 2rem;
            flex-wrap: wrap;
        }

        .stat-item {
            display: flex;
            align-items: center;
            color: var(--primary-blue);
            font-weight: 500;
        }

        .start-quiz-btn {
            padding: 1rem 2rem;
            font-size: 1.1rem;
            border-radius: 50px;
            box-shadow: 0 4px 15px rgba(30, 136, 229, 0.3);
            transition: var(--transition);
        }

        .start-quiz-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(30, 136, 229, 0.4);
        }

        .quiz-content {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--card-shadow);
        }

        .quiz-header {
            margin-bottom: 2rem;
        }

        .quiz-progress {
            background: #e9ecef;
            height: 8px;
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: 1rem;
        }

        .quiz-progress .progress-bar {
            background: linear-gradient(90deg, var(--primary-blue), var(--primary-dark));
            height: 100%;
            transition: width 0.3s ease;
        }

        .question-counter {
            text-align: center;
            color: var(--text-secondary);
            font-weight: 500;
        }

        .quiz-question-content {
            margin-bottom: 2rem;
        }

        .question-text {
            color: var(--primary-dark);
            margin-bottom: 1rem;
            font-size: 1.3rem;
        }

        .instruction-text {
            color: var(--text-secondary);
            margin-bottom: 1.5rem;
        }

        .quiz-options {
            display: flex;
            flex-direction: column;
            gap: 1rem;
        }

        .quiz-option {
            display: flex;
            align-items: center;
            padding: 1rem 1.5rem;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            cursor: pointer;
            transition: var(--transition);
            background: white;
        }

        .quiz-option:hover {
            border-color: var(--primary-blue);
            background: var(--light-blue);
        }

        .quiz-option.selected {
            border-color: var(--primary-blue);
            background: var(--light-blue);
            box-shadow: 0 2px 8px rgba(30, 136, 229, 0.2);
        }

        .quiz-option input {
            margin-right: 1rem;
            transform: scale(1.2);
        }

        .option-text {
            flex-grow: 1;
            font-weight: 500;
        }

        .subjective-answer textarea {
            border-radius: 12px;
            border: 2px solid #e9ecef;
            padding: 1rem;
            font-size: 1rem;
            transition: var(--transition);
        }

        .subjective-answer textarea:focus {
            border-color: var(--primary-blue);
            box-shadow: 0 0 0 0.2rem rgba(30, 136, 229, 0.25);
        }

        .quiz-navigation {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding-top: 2rem;
            border-top: 1px solid #e9ecef;
        }

        .quiz-results-container {
            background: white;
            border-radius: 20px;
            padding: 3rem 2rem;
            text-align: center;
            box-shadow: var(--card-shadow);
        }

        .results-header.passed {
            color: var(--success-color);
        }

        .results-header.failed {
            color: #dc3545;
        }

        .score-circle {
            width: 120px;
            height: 120px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 2rem;
            background: linear-gradient(135deg, var(--primary-blue), var(--primary-dark));
            color: white;
            box-shadow: 0 8px 25px rgba(30, 136, 229, 0.3);
        }

        .score-number {
            font-size: 2rem;
            font-weight: bold;
        }

        .results-details {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 1.5rem;
            margin: 2rem 0;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-item .label {
            font-weight: 500;
            color: var(--text-secondary);
        }

        .detail-item .value {
            font-weight: bold;
            color: var(--primary-dark);
        }

        .results-actions {
            display: flex;
            gap: 1rem;
            justify-content: center;
            flex-wrap: wrap;
        }

        /* Locked Lesson Styles */
        .locked-lesson-overlay {
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 400px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border-radius: 20px;
            margin: 2rem 0;
        }

        .locked-content {
            text-align: center;
            padding: 3rem 2rem;
            background: white;
            border-radius: 16px;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            max-width: 500px;
        }

        .locked-content h3 {
            color: var(--primary-dark);
            margin: 1rem 0;
            font-size: 1.5rem;
        }

        .locked-content p {
            color: var(--text-secondary);
            margin-bottom: 2rem;
            font-size: 1.1rem;
        }

        /* Completed lesson styles */
        .lesson-item.completed {
            background: rgba(76, 175, 80, 0.1);
            border-left-color: var(--success-color);
        }

        .completed-badge {
            font-size: 0.8rem;
            padding: 0.25rem 0.5rem;
        }

        /* Pulse animation for complete button */
        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .btn-complete {
            transition: all 0.3s ease;
        }

        .btn-complete:not(.disabled):hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(40, 167, 69, 0.3);
        }

        /* Sequential navigation improvements */
        .btn-navigation:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .lesson-actions {
            gap: 1rem;
        }

        .lesson-progress-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            color: #495057;
        }

        /* Locked lesson overlay improvements */
        .locked-lesson-overlay {
            background: linear-gradient(135deg, #fff5f5 0%, #fed7d7 100%);
            border: 2px dashed #fc8181;
        }

        .previous-lesson-info {
            background: rgba(13, 110, 253, 0.1);
            padding: 1rem;
            border-radius: 10px;
            margin: 1rem 0;
            border-left: 4px solid #007bff;
        }

        /* Lesson actions navigation */
        .lesson-actions {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 2rem;
            padding-top: 2rem;
            border-top: 1px solid #eee;
            flex-wrap: wrap;
            gap: 1rem;
        }

        .lesson-progress-info {
            display: flex;
            align-items: center;
            justify-content: center;
            flex-grow: 1;
        }

        .lesson-counter {
            background: var(--light-blue);
            color: var(--primary-dark);
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-weight: 600;
            font-size: 0.9rem;
        }

        .btn-navigation:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }

        .btn-navigation:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        /* Text lesson content styles */
        .text-lesson-container {
            background: white;
            border-radius: 20px;
            padding: 2rem;
            box-shadow: var(--card-shadow);
            margin: 2rem 0;
        }

        .lesson-image-container {
            text-align: center;
            margin-bottom: 2rem;
        }

        .lesson-image {
            max-width: 100%;
            height: auto;
            border-radius: 12px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            transition: var(--transition);
        }

        .lesson-image:hover {
            transform: scale(1.02);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
        }

        .image-caption {
            text-align: center;
            margin-top: 0.5rem;
        }

        .image-caption small {
            font-style: italic;
        }

        .lesson-text-content {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-dark);
        }

        .lesson-text-content h1,
        .lesson-text-content h2,
        .lesson-text-content h3,
        .lesson-text-content h4,
        .lesson-text-content h5,
        .lesson-text-content h6 {
            color: var(--primary-dark);
            margin-top: 2rem;
            margin-bottom: 1rem;
            font-weight: 600;
        }

        .lesson-text-content h1 {
            font-size: 2rem;
            border-bottom: 2px solid var(--primary-blue);
            padding-bottom: 0.5rem;
        }

        .lesson-text-content h2 {
            font-size: 1.5rem;
        }

        .lesson-text-content h3 {
            font-size: 1.3rem;
        }

        .lesson-text-content p {
            margin-bottom: 1.5rem;
            text-align: justify;
        }

        .lesson-text-content ul,
        .lesson-text-content ol {
            margin-bottom: 1.5rem;
            padding-left: 2rem;
        }

        .lesson-text-content li {
            margin-bottom: 0.5rem;
        }

        .lesson-text-content blockquote {
            background: var(--light-blue);
            border-left: 4px solid var(--primary-blue);
            padding: 1rem 1.5rem;
            margin: 1.5rem 0;
            border-radius: 0 8px 8px 0;
            font-style: italic;
        }

        .lesson-text-content code {
            background: #f8f9fa;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: var(--primary-dark);
        }

        .lesson-text-content pre {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 8px;
            overflow-x: auto;
            margin: 1.5rem 0;
        }

        .lesson-text-content table {
            width: 100%;
            border-collapse: collapse;
            margin: 1.5rem 0;
        }

        .lesson-text-content th,
        .lesson-text-content td {
            border: 1px solid #dee2e6;
            padding: 0.75rem;
            text-align: left;
        }

        .lesson-text-content th {
            background: var(--light-blue);
            font-weight: 600;
            color: var(--primary-dark);
        }

        /* Legacy content styles for backward compatibility */
        .lesson-content-text {
            background: white;
            border-radius: 16px;
            padding: 2rem;
            box-shadow: var(--card-shadow);
            margin: 2rem 0;
        }

        .content-body {
            font-size: 1.1rem;
            line-height: 1.7;
            color: var(--text-dark);
        }

        @media (max-width: 768px) {
            .lesson-actions {
                flex-direction: column;
                gap: 1rem;
            }

            .lesson-progress-info {
                order: -1;
                width: 100%;
            }

            .btn-navigation, .btn-complete {
                width: 100%;
                max-width: 200px;
            }

            .text-lesson-container {
                padding: 1.5rem;
                margin: 1rem 0;
            }

            .lesson-text-content {
                font-size: 1rem;
            }

            .lesson-text-content h1 {
                font-size: 1.5rem;
            }

            .lesson-text-content h2 {
                font-size: 1.3rem;
            }

            .lesson-text-content h3 {
                font-size: 1.1rem;
            }

            .lesson-image {
                max-width: 100%;
                height: auto;
            }

            .lesson-content-text {
                padding: 1.5rem;
                margin: 1rem 0;
            }

            .content-body {
                font-size: 1rem;
            }
        }

        /* Notificaciones */
        .notification {
            position: fixed;
            top: 100px;
            right: 20px;
            padding: 1rem 1.5rem;
            border-radius: 8px;
            color: white;
            font-weight: 500;
            z-index: 1001;
            transform: translateX(400px);
            transition: transform 0.3s ease;
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-color);
        }

        .notification.error {
            background: #dc3545;
        }

        /* Sidebar toggle button */
        .sidebar-toggle {
            position: fixed;
            top: 100px;
            left: 20px;
            z-index: 1001;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            display: none;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Responsive */
        @media (max-width: 992px) {
            .lesson-sidebar {
                position: fixed;
                top: 80px;
                left: -400px;
                height: calc(100vh - 80px);
                z-index: 1000;
                transition: left 0.3s ease;
                margin: 0;
                border-radius: 0;
                width: 400px;
            }

            .lesson-sidebar.show {
                left: 0;
            }

            .sidebar-toggle {
                display: flex;
            }

            .lesson-content {
                margin-left: 0;
                width: 100%;
            }
        }

        @media (max-width: 768px) {
            .lesson-sidebar {
                width: 100%;
                left: -100%;
            }

            .lesson-sidebar.show {
                left: 0;
            }

            .header-content {
                flex-direction: column;
                gap: 2rem;
            }

            .progress-bar-container {
                width: 150px;
            }

            .sidebar-header {
                padding: 1.5rem;
            }

            .module-header {
                padding: 0.75rem 1rem;
            }

            .lesson-item {
                padding: 0.75rem 1rem;
            }
        }

        @media (max-width: 480px) {
            .sidebar-toggle {
                width: 45px;
                height: 45px;
                top: 90px;
                left: 15px;
            }

            .lesson-content {
                padding: 1rem;
            }
        }
    </style>
</head>
<body>
    <?php if ($access_denied): ?>
        <!-- Estado de acceso denegado -->
        <div class="container-fluid d-flex justify-content-center align-items-center min-vh-100">
            <div class="text-center">
                <div class="mb-4">
                    <i class="bi bi-lock-fill" style="font-size: 4rem; color: var(--primary-blue);"></i>
                </div>
                <h2 class="mb-3">Acceso Restringido</h2>
                <p class="mb-4"><?php echo esc_html($access_message); ?></p>
                <div class="d-flex gap-3 justify-content-center">
                    <?php if (!is_user_logged_in()): ?>
                        <a href="/wp-login.php" class="btn btn-primary">Iniciar Sesión</a>
                        <a href="/register/" class="btn btn-outline-secondary">Crear Cuenta</a>
                    <?php else: ?>
                        <a href="/vistapreliminar/?course=<?php echo esc_attr($course_code); ?>" class="btn btn-primary">Ver Curso</a>
                        <a href="/courses/" class="btn btn-outline-secondary">Explorar Cursos</a>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    <?php else: ?>

 

        <!-- Layout principal -->
        <div class="lesson-layout">
            <!-- Sidebar de lecciones -->
            <div class="lesson-sidebar" id="lessonSidebar">
                <div class="sidebar-header">
                    <h3 class="sidebar-title" id="courseTitle">
                        <i class="bi bi-mortarboard me-2"></i>
                        Contenido del Curso
                    </h3>
                    <div class="course-progress">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <small class="text-muted">Progress</small>
                            <small class="fw-bold" id="sidebarProgressText">0%</small>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill" id="sidebarProgressFill"></div>
                        </div>
                    </div>
                </div>

                <div class="sidebar-content">
                    <div id="modulesList">
                        <div class="loading-state">
                            <div class="spinner"></div>
                            <span>Loading modules...</span>
                        </div>
                    </div>
                </div>

                <!-- Toggle button for mobile -->
                <button class="btn btn-outline-primary d-lg-none sidebar-toggle" id="sidebarToggle">
                    <i class="bi bi-list"></i>
                </button>
            </div>

            <!-- Contenido principal -->
            <div class="lesson-content">
                <div id="lessonContentArea">
                    <div class="loading-state">
                        <div class="spinner"></div>
                        <span>Cargando contenido...</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Notificaciones -->
        <div id="notificationContainer"></div>

        <!-- Bootstrap JS -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>

        <script>
            /**
             * ASG Lessons System - JavaScript Limpio y Refactorizado
             * Funciones organizadas y reutilizables
             */

            // Prevenir conflictos con otros scripts
            (function() {
                'use strict';

            // ===== CONFIGURACIÓN GLOBAL =====
            const ASG_CONFIG = {
                API_BASE: '<?php echo $site_url; ?>/wp-json/asg/v1',
                COURSE_CODE: '<?php echo esc_js($course_code); ?>',
                LESSON_ID: '<?php echo esc_js($lesson_id); ?>',
                AUTO_LOAD_FIRST: <?php echo $auto_load_first ? 'true' : 'false'; ?>,
                USER_ID: <?php echo get_current_user_id(); ?>
            };

            // Fallback: obtener parámetros de URL si no están definidos
            if (!ASG_CONFIG.COURSE_CODE || !ASG_CONFIG.LESSON_ID) {
                const urlParams = new URLSearchParams(window.location.search);

                if (!ASG_CONFIG.COURSE_CODE) {
                    ASG_CONFIG.COURSE_CODE = urlParams.get('course') || '';
                }

                if (!ASG_CONFIG.LESSON_ID) {
                    ASG_CONFIG.LESSON_ID = urlParams.get('lesson') || '';
                }

                // Actualizar AUTO_LOAD_FIRST basado en parámetros
                ASG_CONFIG.AUTO_LOAD_FIRST = !ASG_CONFIG.LESSON_ID && ASG_CONFIG.COURSE_CODE;
            }

            console.log('Configuración inicial:', ASG_CONFIG);

            // ===== ESTADO GLOBAL =====
            let asgState = {
                currentCourse: null,
                currentLesson: null,
                courseProgress: null,
                lessonsList: [],
                isLoading: false
            };

            // ===== FUNCIONES UTILITARIAS =====

            /**
             * Realizar petición API con manejo de errores
             */
            async function apiRequest(endpoint, options = {}) {
                const url = `${ASG_CONFIG.API_BASE}${endpoint}`;
                const defaultOptions = {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                };

                try {
                    const response = await fetch(url, { ...defaultOptions, ...options });

                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }

                    return await response.json();
                } catch (error) {
                    console.error('API Error:', error);
                    throw error;
                }
            }

            /**
             * Mostrar notificación al usuario
             */
            function showNotification(message, type = 'success', duration = 3000) {
                const container = document.getElementById('notificationContainer');
                const notification = document.createElement('div');

                notification.className = `notification ${type}`;
                notification.innerHTML = `
                    <i class="bi bi-${type === 'success' ? 'check-circle' : 'exclamation-triangle'} me-2"></i>
                    ${message}
                `;

                container.appendChild(notification);

                // Mostrar con animación
                setTimeout(() => notification.classList.add('show'), 100);

                // Ocultar después del tiempo especificado
                setTimeout(() => {
                    notification.classList.remove('show');
                    setTimeout(() => container.removeChild(notification), 300);
                }, duration);
            }

            /**
             * DEBUG FUNCTION - Remove after testing
             */
            function debugLessonState() {
                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();
                const currentLesson = allLessons[currentIndex];
                const nextLesson = allLessons[currentIndex + 1];

                const debugInfo = {
                    '🎯 Current State': {
                        courseCode: asgState.courseCode,
                        lessonCode: asgState.lessonCode,
                        currentIndex,
                        totalLessons: allLessons.length
                    },
                    '📚 Current Lesson': currentLesson,
                    '➡️ Next Lesson': nextLesson,
                    '✅ Progress Data': {
                        courseProgress: asgState.courseProgress,
                        completedLessons: asgState.courseProgress?.completed_lessons_list || [],
                        isCurrentCompleted: asgState.courseProgress?.completed_lessons_list?.includes(currentLesson?.id),
                        isNextLocked: nextLesson ? isLessonLocked(nextLesson.id) : 'No next lesson'
                    },
                    '🔐 Lock Status': {
                        currentLocked: isLessonLocked(currentLesson?.id),
                        nextLocked: nextLesson ? isLessonLocked(nextLesson.id) : 'No next lesson'
                    }
                };

                console.log('🐛 LESSON STATE DEBUG:', debugInfo);

                // Show in alert too
                alert(`Debug Info (check console for details):

Current: ${currentLesson?.title}
Next: ${nextLesson?.title || 'None'}
Current Completed: ${asgState.courseProgress?.completed_lessons_list?.includes(currentLesson?.id)}
Next Locked: ${nextLesson ? isLessonLocked(nextLesson.id) : 'No next lesson'}

Check browser console for full details.`);
            }

            /**
             * Actualizar barra de progreso
             */
            function updateProgressBar(progressData) {
                // Barra de progreso del header
                const progressText = document.getElementById('progressText');
                const progressFill = document.getElementById('progressFill');

                if (progressText && progressData.progress_percentage !== undefined) {
                    progressText.textContent = `${progressData.progress_percentage}%`;
                }

                if (progressFill && progressData.progress_percentage !== undefined) {
                    progressFill.style.width = `${progressData.progress_percentage}%`;
                }

                // Barra de progreso del sidebar
                const sidebarProgressText = document.getElementById('sidebarProgressText');
                const sidebarProgressFill = document.getElementById('sidebarProgressFill');

                if (sidebarProgressText && progressData.progress_percentage !== undefined) {
                    sidebarProgressText.textContent = `${progressData.progress_percentage}%`;
                }

                if (sidebarProgressFill && progressData.progress_percentage !== undefined) {
                    sidebarProgressFill.style.width = `${progressData.progress_percentage}%`;
                }
            }

            /**
             * Obtener ID de lección actual desde URL
             */
            function getCurrentLessonId() {
                const urlParams = new URLSearchParams(window.location.search);
                return urlParams.get('lesson');
            }

            /**
             * Check if a lesson is locked (Sequential Learning System)
             */
            function isLessonLocked(lessonId) {
                if (!asgState.courseProgress || !asgState.lessonsList) {
                    console.log(`🔒 Lesson ${lessonId}: No progress data available`);
                    return false;
                }

                const completedLessons = asgState.courseProgress.completed_lessons_list || [];
                const lessonIndex = asgState.lessonsList.findIndex(lesson => parseInt(lesson.id) === parseInt(lessonId));

                // First lesson is always unlocked
                if (lessonIndex === 0) {
                    console.log(`🔒 Lesson ${lessonId}: First lesson - always unlocked`);
                    return false;
                }

                // Check if previous lesson is completed
                const previousLesson = asgState.lessonsList[lessonIndex - 1];
                if (!previousLesson) {
                    console.log(`🔒 Lesson ${lessonId}: No previous lesson found`);
                    return false;
                }

                const isPreviousCompleted = completedLessons.includes(parseInt(previousLesson.id));

                console.log(`🔒 Lesson ${lessonId} lock check:`, {
                    lessonIndex,
                    previousLessonId: previousLesson.id,
                    isPreviousCompleted,
                    completedLessons,
                    isLocked: !isPreviousCompleted
                });

                return !isPreviousCompleted;
            }

            /**
             * Show locked lesson message
             */
            function showLockedLessonMessage(lessonId) {
                const lessonIndex = asgState.lessonsList.findIndex(lesson => parseInt(lesson.id) === parseInt(lessonId));
                const previousLesson = lessonIndex > 0 ? asgState.lessonsList[lessonIndex - 1] : null;

                const message = `
                    <div class="locked-lesson-overlay">
                        <div class="locked-content">
                            <i class="bi bi-lock-fill text-warning" style="font-size: 3rem;"></i>
                            <h3>Lesson Locked</h3>
                            <p>You must complete the previous lesson to access this content.</p>
                            ${previousLesson ? `
                                <div class="previous-lesson-info">
                                    <p><strong>Complete first:</strong> ${previousLesson.title}</p>
                                    <button class="btn btn-primary" onclick="navigateToLesson(${previousLesson.id})">
                                        <i class="bi bi-arrow-left me-2"></i>Go to Previous Lesson
                                    </button>
                                </div>
                            ` : `
                                <button class="btn btn-primary" onclick="findNextUnlockedLesson()">
                                    <i class="bi bi-arrow-right me-2"></i>Find Available Lesson
                                </button>
                            `}
                        </div>
                    </div>
                `;

                const contentArea = document.getElementById('lessonContentArea');
                if (contentArea) {
                    contentArea.innerHTML = message;
                }
            }

            /**
             * Find the next available lesson (Sequential Learning)
             */
            function findNextUnlockedLesson() {
                if (!asgState.courseProgress || !asgState.lessonsList) return null;

                const completedLessons = asgState.courseProgress.completed_lessons_list || [];

                // Find the first incomplete lesson
                for (let i = 0; i < asgState.lessonsList.length; i++) {
                    const lesson = asgState.lessonsList[i];
                    const lessonId = parseInt(lesson.id);

                    // If this lesson is not completed
                    if (!completedLessons.includes(lessonId)) {
                        // Check if it's unlocked (first lesson or previous is completed)
                        if (i === 0 || completedLessons.includes(parseInt(asgState.lessonsList[i - 1].id))) {
                            console.log(`🎯 Next available lesson found: ${lesson.title} (ID: ${lessonId})`);
                            navigateToLesson(lessonId);
                            return lessonId;
                        }
                    }
                }

                // All lessons completed
                showNotification('🎉 Congratulations! You have completed all lessons!', 'success');
                return null;
            }

            /**
             * Navegar a una lección específica
             */
            function navigateToLesson(lessonId) {
                if (!lessonId) return;

                console.log('Navegando a lección:', lessonId);

                // Verificar si la lección está bloqueada
                if (isLessonLocked(lessonId)) {
                    console.log('Lección bloqueada:', lessonId);
                    showLockedLessonMessage(lessonId);
                    showNotification('Esta lección está bloqueada. Completa las lecciones anteriores.', 'warning');
                    return;
                }

                const newUrl = `${window.location.pathname}?course=${ASG_CONFIG.COURSE_CODE}&lesson=${lessonId}`;
                window.history.pushState({}, '', newUrl);
                loadLessonContent(lessonId);
            }

            // ===== GESTIÓN DE PROGRESO =====

            /**
             * Load user progress from server
             */
            async function loadUserProgress() {
                if (!ASG_CONFIG.COURSE_CODE) return;

                try {
                    console.log('🔄 Loading user progress for:', ASG_CONFIG.COURSE_CODE);

                    const result = await apiRequest('/my-courses');
                    console.log('📊 Progress response:', result);

                    if (result.success && result.data.courses) {
                        const courseProgress = result.data.courses.find(course =>
                            course.code_course === ASG_CONFIG.COURSE_CODE
                        );

                        if (courseProgress) {
                            console.log('✅ Course progress found:', courseProgress);
                            asgState.courseProgress = courseProgress;
                            updateProgressBar(courseProgress);
                            updateLessonsListUI(courseProgress);
                            return courseProgress;
                        } else {
                            console.log('⚠️ No progress found for course:', ASG_CONFIG.COURSE_CODE);
                            // Create initial progress if it doesn't exist
                            const initialProgress = {
                                code_course: ASG_CONFIG.COURSE_CODE,
                                completed_lessons: 0,
                                total_lessons: asgState.lessonsList.length,
                                progress_percentage: 0,
                                completed_lessons_list: [],
                                unlocked_lessons_list: [parseInt(asgState.lessonsList[0]?.id)] // Only first lesson unlocked
                            };
                            asgState.courseProgress = initialProgress;
                            updateProgressBar(initialProgress);
                            updateLessonsListUI(initialProgress);
                            return initialProgress;
                        }
                    } else {
                        console.error('❌ Error in progress response:', result);
                        throw new Error(result.message || 'Error getting progress');
                    }

                } catch (error) {
                    console.error('❌ Error loading progress:', error);
                    // Fallback to initial progress
                    const fallbackProgress = {
                        code_course: ASG_CONFIG.COURSE_CODE,
                        completed_lessons: 0,
                        total_lessons: asgState.lessonsList.length,
                        progress_percentage: 0,
                        completed_lessons_list: [],
                        unlocked_lessons_list: [parseInt(asgState.lessonsList[0]?.id)] // Only first lesson unlocked
                    };
                    asgState.courseProgress = fallbackProgress;
                    updateProgressBar(fallbackProgress);
                    updateLessonsListUI(fallbackProgress);
                    return fallbackProgress;
                }
            }

            /**
             * Mark lesson as completed
             */
            async function markLessonComplete(lessonId) {
                if (!lessonId) {
                    lessonId = getCurrentLessonId();
                }

                if (!lessonId) {
                    showNotification('Could not identify current lesson', 'error');
                    return;
                }

                try {
                    console.log('🎯 Completing lesson:', lessonId, 'for course:', ASG_CONFIG.COURSE_CODE);

                    // Use real lesson completion endpoint
                    const url = `/lesson/${lessonId}/complete`;
                    console.log('📤 Sending POST to:', ASG_CONFIG.API_BASE + url);

                    const result = await apiRequest(url, {
                        method: 'POST',
                        headers: {
                            'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]')?.content || ''
                        },
                        body: JSON.stringify({
                            course: ASG_CONFIG.COURSE_CODE,
                            lesson_id: parseInt(lessonId)
                        })
                    });

                    console.log('📥 Lesson completion response:', result);

                    if (result.success) {
                        showNotification('🎉 Lesson completed successfully!', 'success');

                        // Reload progress from server and update UI
                        const updatedProgress = await loadUserProgress();

                        // Update current lesson UI
                        updateLessonCompleteButton(lessonId, true);

                        // Force refresh all lesson states
                        setTimeout(() => {
                            refreshLessonStates();

                            const nextLesson = getNextLesson(lessonId);
                            if (nextLesson) {
                                showNotification(`🔓 Next lesson unlocked: ${nextLesson.title}`, 'info');
                            }
                        }, 500);

                    } else {
                        throw new Error(result.message || 'Server error');
                    }

                } catch (error) {
                    console.error('❌ Error completing lesson:', error);

                    // Try endpoint without nonce as fallback
                    try {
                        console.log('🔄 Trying endpoint without nonce...');
                        const fallbackUrl = `/lesson/${lessonId}/complete-no-nonce`;

                        const fallbackResult = await apiRequest(fallbackUrl, {
                            method: 'POST',
                            body: JSON.stringify({
                                course: ASG_CONFIG.COURSE_CODE,
                                lesson_id: parseInt(lessonId)
                            })
                        });

                        if (fallbackResult.success) {
                            showNotification('🎉 Lesson completed successfully!', 'success');

                            // Reload progress and update UI
                            const updatedProgress = await loadUserProgress();
                            updateLessonCompleteButton(lessonId, true);

                            // Force refresh all lesson states
                            setTimeout(() => {
                                refreshLessonStates();

                                const nextLesson = getNextLesson(lessonId);
                                if (nextLesson) {
                                    showNotification(`🔓 Next lesson unlocked: ${nextLesson.title}`, 'info');
                                }
                            }, 500);
                        } else {
                            throw new Error(fallbackResult.message || 'Fallback error');
                        }

                    } catch (fallbackError) {
                        console.error('❌ Fallback error:', fallbackError);
                        showNotification('Error completing lesson: ' + error.message, 'error');
                    }
                }
            }

            /**
             * Get next lesson in sequence
             */
            function getNextLesson(currentLessonId) {
                if (!asgState.lessonsList) return null;

                const currentIndex = asgState.lessonsList.findIndex(lesson =>
                    parseInt(lesson.id) === parseInt(currentLessonId)
                );

                if (currentIndex >= 0 && currentIndex < asgState.lessonsList.length - 1) {
                    return asgState.lessonsList[currentIndex + 1];
                }

                return null;
            }

            /**
             * Get previous lesson in sequence
             */
            function getPreviousLesson(currentLessonId) {
                if (!asgState.lessonsList) return null;

                const currentIndex = asgState.lessonsList.findIndex(lesson =>
                    parseInt(lesson.id) === parseInt(currentLessonId)
                );

                if (currentIndex > 0) {
                    return asgState.lessonsList[currentIndex - 1];
                }

                return null;
            }

            /**
             * Update navigation buttons state
             */
            function updateNavigationButtons() {
                const navInfo = getNavigationInfo();
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                if (prevBtn) {
                    const canNavigatePrev = navInfo.hasPrevious;
                    prevBtn.disabled = !canNavigatePrev;
                    prevBtn.title = !navInfo.hasPrevious ? 'First lesson' : navInfo.previousLesson?.title;
                }

                if (nextBtn) {
                    const canNavigateNext = navInfo.hasNext && !isLessonLocked(navInfo.nextLesson?.id);
                    nextBtn.disabled = !canNavigateNext;
                    nextBtn.title = !navInfo.hasNext ? 'Last lesson' :
                                   !canNavigateNext ? 'Complete this lesson to continue' :
                                   navInfo.nextLesson?.title;

                    console.log('🔄 Navigation updated:', {
                        hasNext: navInfo.hasNext,
                        nextLessonId: navInfo.nextLesson?.id,
                        isNextLocked: navInfo.nextLesson ? isLessonLocked(navInfo.nextLesson.id) : 'N/A',
                        canNavigateNext
                    });
                }
            }

            /**
             * Force refresh all lesson states after completion
             */
            function refreshLessonStates() {
                console.log('🔄 Refreshing all lesson states...');

                // Force re-render of lessons list
                if (asgState.courseProgress) {
                    updateLessonsListUI(asgState.courseProgress);
                }

                // Update navigation buttons
                updateNavigationButtons();

                // Update progress bar
                if (asgState.courseProgress) {
                    updateProgressBar(asgState.courseProgress);
                }

                console.log('✅ All lesson states refreshed');
            }

            /**
             * Check user enrollment in course
             */
            async function checkEnrollment() {
                if (!ASG_CONFIG.COURSE_CODE) return false;

                try {
                    console.log('Verificando enrollment para curso:', ASG_CONFIG.COURSE_CODE);

                    const result = await apiRequest(`/check-enrollment?course=${ASG_CONFIG.COURSE_CODE}`);
                    console.log('Respuesta de enrollment:', result);

                    if (result.success && result.enrolled) {
                        console.log('Usuario está inscrito en el curso');
                        return true;
                    } else {
                        console.log('Usuario NO está inscrito en el curso');
                        return false;
                    }

                } catch (error) {
                    console.error('Error verificando enrollment:', error);
                    // En caso de error, asumir que no está inscrito
                    return false;
                }
            }

            // ===== GESTIÓN DE LECCIONES =====

            /**
             * Cargar datos del curso y lecciones
             */
            async function loadCourseData() {
                if (!ASG_CONFIG.COURSE_CODE) return;

                try {
                    asgState.isLoading = true;
                    console.log('Cargando datos del curso:', ASG_CONFIG.COURSE_CODE);

                    // Usar el endpoint correcto que existe
                    const result = await apiRequest(`/courses/api/${ASG_CONFIG.COURSE_CODE}`);
                    console.log('Respuesta del curso:', result);

                    if (result.success && result.data) {
                        // El endpoint devuelve un curso individual
                        const courseData = result.data;

                        asgState.currentCourse = courseData;
                        asgState.lessonsList = extractLessonsFromCourse(courseData);

                        console.log('Datos del curso cargados:', {
                            id: courseData.id_course,
                            name: courseData.name_course,
                            cover_img: courseData.cover_img,
                            modules_count: courseData.modules ? courseData.modules.length : 0,
                            total_lessons: asgState.lessonsList.length
                        });

                        // Log de módulos e imágenes
                        if (courseData.modules) {
                            courseData.modules.forEach((module, index) => {
                                console.log(`Módulo ${index + 1}:`, {
                                    title: module.title_module,
                                    cover_img: module.cover_img,
                                    lessons_count: module.lessons ? module.lessons.length : 0
                                });
                            });
                        }

                        renderLessonsList();

                        // Verificar enrollment del usuario
                        const isEnrolled = await checkEnrollment();
                        if (!isEnrolled) {
                            console.warn('Usuario no está inscrito en el curso');
                            // Mostrar mensaje de enrollment requerido pero permitir continuar
                            showNotification('Nota: Debes estar inscrito para completar lecciones', 'info', 5000);
                        }

                        // Cargar progreso del usuario
                        await loadUserProgress();

                        // Cargar lección específica o primera lección
                        if (ASG_CONFIG.LESSON_ID) {
                            loadLessonContent(ASG_CONFIG.LESSON_ID);
                        } else if (ASG_CONFIG.AUTO_LOAD_FIRST && asgState.lessonsList.length > 0) {
                            loadLessonContent(asgState.lessonsList[0].id);
                        }

                    } else {
                        console.error('Error en respuesta del curso:', result);
                        showError('No se pudo cargar el curso');
                    }
                } catch (error) {
                    console.error('Error cargando datos del curso:', error);
                    showError('Error cargando datos del curso: ' + error.message);
                } finally {
                    asgState.isLoading = false;
                }
            }

            /**
             * Extraer lista de lecciones desde datos del curso
             */
            function extractLessonsFromCourse(courseData) {
                const lessons = [];

                console.log('Extrayendo lecciones de:', courseData);

                if (courseData.modules && Array.isArray(courseData.modules)) {
                    courseData.modules.forEach((module, moduleIndex) => {
                        console.log(`Procesando módulo ${moduleIndex}:`, module);

                        if (module.lessons && Array.isArray(module.lessons)) {
                            module.lessons.forEach((lesson, lessonIndex) => {
                                console.log(`  Procesando lección ${lessonIndex}:`, lesson);

                                lessons.push({
                                    id: lesson.id_lesson || lesson.id,
                                    title: lesson.title_lesson || lesson.title || `Lección ${lessonIndex + 1}`,
                                    type: lesson.lesson_type || lesson.type_lesson || lesson.type || 'video',
                                    content: lesson.content_lesson || lesson.content || '',
                                    video_url: lesson.video_url || lesson.videoUrl || '',
                                    cover_img: lesson.cover_img || '',
                                    module_title: module.title_module || module.title || `Módulo ${moduleIndex + 1}`,
                                    module_cover_img: module.cover_img || '',
                                    is_preview: lesson.is_preview === '1' || lesson.is_preview === 1 || lesson.isPreview === true
                                });
                            });
                        } else {
                            console.log('  No hay lecciones en este módulo');
                        }
                    });
                } else {
                    console.log('No hay módulos en el curso o no es un array');
                }

                console.log('Total de lecciones extraídas:', lessons.length);
                return lessons;
            }

            /**
             * Cargar contenido de una lección específica
             */
            async function loadLessonContent(lessonId) {
                const lesson = asgState.lessonsList.find(l => l.id == lessonId);

                if (!lesson) {
                    console.error('Lección no encontrada:', lessonId);
                    showError('Lección no encontrada');
                    return;
                }

                try {
                    // Normalizar tipo de lección antes de renderizar
                    if (!lesson.type && !lesson.type_lesson && !lesson.lesson_type) {
                        // Auto-detectar tipo basado en contenido
                        if (lesson.content) {
                            try {
                                const parsedContent = JSON.parse(lesson.content);
                                if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                                    lesson.type = 'quiz';
                                } else {
                                    lesson.type = lesson.video_url ? 'video' : 'text';
                                }
                            } catch (e) {
                                // No es JSON válido, determinar por video_url
                                lesson.type = lesson.video_url ? 'video' : 'text';
                            }
                        } else if (lesson.video_url) {
                            lesson.type = 'video';
                        } else {
                            // Sin contenido ni video, asumir text
                            lesson.type = 'text';
                        }

                        console.log('Tipo auto-detectado para lección', lessonId, ':', lesson.type, {
                            hasContent: !!lesson.content,
                            hasVideo: !!lesson.video_url,
                            hasImage: !!(lesson.lesson_image || lesson.lesson_medium_url || lesson.lesson_large_url)
                        });
                    }

                    asgState.currentLesson = lesson;
                    updateLessonsListActive(lessonId);
                    renderLessonContent(lesson);
                } catch (error) {
                    console.error('Error cargando contenido de lección:', error);
                    showError('Error cargando contenido de la lección');
                }
            }

            // ===== GESTIÓN DE UI =====

            /**
             * Renderizar estructura de módulos y lecciones en el sidebar
             */
            function renderLessonsList() {
                const container = document.getElementById('modulesList');

                if (!container) {
                    console.error('Container modulesList no encontrado');
                    return;
                }

                if (!asgState.currentCourse || !asgState.currentCourse.modules) {
                    container.innerHTML = `
                        <div class="text-center py-4">
                            <i class="bi bi-book text-muted" style="font-size: 2rem;"></i>
                            <p class="text-muted mt-2">No modules available</p>
                            <button class="btn btn-sm btn-outline-primary" onclick="loadCourseData()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Reload
                            </button>
                        </div>
                    `;
                    return;
                }

                console.log('Renderizando', asgState.currentCourse.modules.length, 'módulos');

                // Actualizar título del curso
                const courseTitle = document.getElementById('courseTitle');
                if (courseTitle) {
                    courseTitle.innerHTML = `
                        <i class="bi bi-mortarboard me-2"></i>
                        ${asgState.currentCourse.name_course || 'Curso'}
                    `;
                }

                // Renderizar módulos
                const modulesHtml = asgState.currentCourse.modules.map((module, moduleIndex) => {
                    const moduleId = `module-${moduleIndex}`;
                    const lessonsHtml = module.lessons ? module.lessons.map(lesson => {
                        const isActive = lesson.id_lesson == ASG_CONFIG.LESSON_ID;
                        const lessonType = lesson.type_lesson || lesson.lesson_type || 'video';
                        const icon = lessonType === 'video' ? 'play-circle' : 'question-circle';
                        const isPreview = lesson.is_preview === '1' || lesson.is_preview === 1;

                        return `
                            <div class="lesson-item ${isActive ? 'active' : ''} ${isPreview ? 'preview-lesson' : 'unlocked-lesson'}"
                                 onclick="navigateToLesson(${lesson.id_lesson})"
                                 data-lesson-id="${lesson.id_lesson}">
                                <div class="lesson-icon me-3">
                                    <i class="bi bi-${icon}"></i>
                                </div>
                                <div class="lesson-title flex-grow-1">${lesson.title_lesson}</div>
                                ${isPreview ? '<span class="badge bg-warning ms-2">Preview</span>' : ''}
                                <div class="lesson-status ms-2">
                                    <i class="bi bi-circle text-muted"></i>
                                </div>
                            </div>
                        `;
                    }).join('') : '<p class="text-muted p-3">No hay lecciones en este módulo</p>';

                    return `
                        <div class="module-item">
                            <div class="module-header" onclick="toggleModule('${moduleId}')">
                                <h6 class="module-title">${module.title_module || `Module ${moduleIndex + 1}`}</h6>
                                <i class="bi bi-chevron-right module-chevron" id="chevron-${moduleId}"></i>
                            </div>
                            <div class="module-lessons" id="${moduleId}">
                                <div class="lessons-list">
                                    ${lessonsHtml}
                                </div>
                            </div>
                        </div>
                    `;
                }).join('');

                container.innerHTML = modulesHtml;

                // Expandir el primer módulo por defecto
                setTimeout(() => {
                    const firstModule = document.getElementById('module-0');
                    const firstChevron = document.getElementById('chevron-module-0');
                    if (firstModule && firstChevron) {
                        firstModule.classList.add('show');
                        firstChevron.classList.add('rotated');
                    }
                }, 100);

                console.log('Estructura de módulos renderizada correctamente');
            }

            /**
             * Actualizar estado activo en lista de lecciones
             */
            function updateLessonsListActive(activeLessonId) {
                const items = document.querySelectorAll('.lesson-item');
                items.forEach(item => {
                    item.classList.remove('active');
                    if (item.dataset.lessonId == activeLessonId) {
                        item.classList.add('active');
                    }
                });
            }

            /**
             * Update lessons list UI with progress and sequential locking
             */
            function updateLessonsListUI(progressData) {
                if (!progressData) return;

                const completedLessons = progressData.completed_lessons_list || [];
                const allLessons = progressData.all_lessons_list || [];

                console.log('🎨 Updating lessons UI (Sequential Learning):', {
                    completed: completedLessons,
                    total: allLessons.length
                });

                // If no lesson data, get from current structure
                if (allLessons.length === 0 && asgState.currentCourse && asgState.currentCourse.modules) {
                    asgState.currentCourse.modules.forEach(module => {
                        if (module.lessons) {
                            module.lessons.forEach(lesson => {
                                allLessons.push(parseInt(lesson.id_lesson));
                            });
                        }
                    });
                }

                // Use lessonsList for sequential order
                const orderedLessons = asgState.lessonsList || [];

                // Apply states to all lessons (Sequential Learning Logic)
                orderedLessons.forEach((lesson, index) => {
                    const lessonId = parseInt(lesson.id);
                    const lessonElement = document.querySelector(`[data-lesson-id="${lessonId}"]`);
                    if (!lessonElement) return;

                    // Clear previous classes
                    lessonElement.classList.remove('completed', 'locked-lesson', 'unlocked-lesson');

                    // Remove previous badges
                    const existingBadge = lessonElement.querySelector('.lesson-badge');
                    if (existingBadge) existingBadge.remove();

                    const isCompleted = completedLessons.includes(lessonId);
                    const isFirstLesson = index === 0;
                    const isPreviousCompleted = index === 0 || completedLessons.includes(parseInt(orderedLessons[index - 1].id));
                    const isUnlocked = isFirstLesson || isPreviousCompleted;

                    // Apply lesson state
                    if (isCompleted) {
                        // ✅ COMPLETED LESSON
                        lessonElement.classList.add('completed');
                        const statusIcon = lessonElement.querySelector('.lesson-status i');
                        if (statusIcon) {
                            statusIcon.className = 'bi bi-check-circle-fill text-success';
                        }

                        // Visual completed style
                        lessonElement.style.opacity = '0.8';
                        lessonElement.style.textDecoration = 'line-through';

                        // Add completed badge
                        const badge = document.createElement('span');
                        badge.className = 'lesson-badge badge bg-success ms-2';
                        badge.innerHTML = '<i class="bi bi-check"></i> Completed';
                        lessonElement.appendChild(badge);

                    } else if (isUnlocked) {
                        // 🔓 UNLOCKED LESSON
                        lessonElement.classList.add('unlocked-lesson');
                        const statusIcon = lessonElement.querySelector('.lesson-status i');
                        if (statusIcon) {
                            statusIcon.className = 'bi bi-play-circle text-primary';
                        }

                        // Reset styles
                        lessonElement.style.opacity = '1';
                        lessonElement.style.textDecoration = 'none';
                        lessonElement.style.cursor = 'pointer';

                        // Add available badge
                        const badge = document.createElement('span');
                        badge.className = 'lesson-badge badge bg-primary ms-2';
                        badge.innerHTML = '<i class="bi bi-play"></i> Available';
                        lessonElement.appendChild(badge);

                    } else {
                        // 🔒 LOCKED LESSON
                        lessonElement.classList.add('locked-lesson');
                        const statusIcon = lessonElement.querySelector('.lesson-status i');
                        if (statusIcon) {
                            statusIcon.className = 'bi bi-lock-fill text-muted';
                        }

                        // Locked styles
                        lessonElement.style.opacity = '0.5';
                        lessonElement.style.textDecoration = 'none';
                        lessonElement.style.cursor = 'not-allowed';

                        // Add locked badge
                        const badge = document.createElement('span');
                        badge.className = 'lesson-badge badge bg-secondary ms-2';
                        badge.innerHTML = '<i class="bi bi-lock"></i> Locked';
                        lessonElement.appendChild(badge);

                        // Disable click for locked lessons
                        lessonElement.onclick = (e) => {
                            e.preventDefault();
                            showNotification('🔒 Complete the previous lesson to unlock this one', 'warning');
                        };
                    }

                    console.log(`📚 Lesson ${lessonId}: ${isCompleted ? 'Completed' : isUnlocked ? 'Available' : 'Locked'}`);
                });

                console.log('✅ Sequential lessons UI updated successfully');
            }

            /**
             * Renderizar contenido de lección
             */
            function renderLessonContent(lesson) {
                const container = document.getElementById('lessonContentArea');

                const isCompleted = asgState.courseProgress &&
                    asgState.courseProgress.completed_lessons_list &&
                    asgState.courseProgress.completed_lessons_list.includes(parseInt(lesson.id));

                // Detectar tipo de lección con múltiples fallbacks
                let lessonType = lesson.type || lesson.type_lesson || lesson.lesson_type || 'video';

                // Debug logging
                console.log('Renderizando lección:', {
                    id: lesson.id,
                    title: lesson.title,
                    type: lesson.type,
                    type_lesson: lesson.type_lesson,
                    lesson_type: lesson.lesson_type,
                    detected_type: lessonType,
                    has_video_url: !!lesson.video_url,
                    content_preview: lesson.content ? lesson.content.substring(0, 100) : 'No content'
                });

                // Detectar automáticamente si es quiz basado en contenido
                if (lessonType === 'video' && lesson.content) {
                    try {
                        const parsedContent = JSON.parse(lesson.content);
                        if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                            lessonType = 'quiz';
                            console.log('Auto-detectado como quiz por contenido JSON');
                        }
                    } catch (e) {
                        // No es JSON válido, mantener como video
                    }
                }

                // Si no hay video_url pero hay contenido, determinar si es quiz o text
                if (lessonType === 'video' && !lesson.video_url && lesson.content) {
                    try {
                        const parsedContent = JSON.parse(lesson.content);
                        if (parsedContent.questions && Array.isArray(parsedContent.questions)) {
                            lessonType = 'quiz';
                            console.log('Auto-detectado como quiz por contenido JSON');
                        } else {
                            lessonType = 'text';
                            console.log('Auto-detectado como text por contenido no-JSON');
                        }
                    } catch (e) {
                        // No es JSON válido, es contenido de texto
                        lessonType = 'text';
                        console.log('Auto-detectado como text por contenido no-JSON');
                    }
                }

                let contentHtml = '';

                // Lesson header
                const typeConfig = {
                    video: { icon: 'play-fill', label: 'Video Lesson' },
                    quiz: { icon: 'question-circle-fill', label: 'Interactive Quiz' },
                    text: { icon: 'file-text-fill', label: 'Text Lesson' }
                };

                const currentType = typeConfig[lessonType] || typeConfig.text;

                contentHtml += `
                    <div class="lesson-header">
                        <div class="lesson-type-badge lesson-type-${lessonType}">
                            <i class="bi bi-${currentType.icon} me-1"></i>
                            ${currentType.label}
                        </div>
                        <h1 class="lesson-title-main">${lesson.title}</h1>
                        <p class="text-muted mb-0">Module: ${lesson.module_title}</p>
                    </div>
                `;

                // Contenido según tipo de lección detectado
                if (lessonType === 'video') {
                    contentHtml += renderVideoContent(lesson);
                } else if (lessonType === 'quiz') {
                    contentHtml += renderQuizContent(lesson);
                } else if (lessonType === 'text') {
                    contentHtml += renderTextContent(lesson);
                } else {
                    // Fallback para tipos desconocidos
                    contentHtml += renderTextContent(lesson);
                }

                // Lesson actions with smart navigation
                const navInfo = getNavigationInfo();
                const canNavigatePrev = navInfo.hasPrevious;
                const canNavigateNext = navInfo.hasNext && !isLessonLocked(navInfo.nextLesson?.id);

                contentHtml += `
                    <div class="lesson-actions">
                        <button class="btn btn-navigation"
                                onclick="navigateToPrevious()"
                                id="prevBtn"
                                ${!canNavigatePrev ? 'disabled' : ''}
                                title="${!navInfo.hasPrevious ? 'First lesson' : navInfo.previousLesson?.title}">
                            <i class="bi bi-arrow-left me-1"></i> Previous
                        </button>

                        <div class="lesson-progress-info">
                            <span class="lesson-counter">${navInfo.currentIndex + 1} of ${navInfo.totalLessons}</span>
                        </div>

                        <button class="btn btn-complete ${isCompleted ? 'disabled' : ''}"
                                onclick="markLessonComplete()"
                                ${isCompleted ? 'disabled' : ''}>
                            <i class="bi bi-${isCompleted ? 'check-circle-fill' : 'check-circle'} me-2"></i>
                            ${isCompleted ? 'Completed' : 'Mark as Complete'}
                        </button>

                        <button class="btn btn-navigation"
                                onclick="navigateToNext()"
                                id="nextBtn"
                                ${!canNavigateNext ? 'disabled' : ''}
                                title="${!navInfo.hasNext ? 'Last lesson' : !canNavigateNext ? 'Complete this lesson to continue' : navInfo.nextLesson?.title}">
                            Next <i class="bi bi-arrow-right ms-1"></i>
                        </button>

                        <!-- DEBUG BUTTON - Remove after testing -->
                        <button class="btn btn-sm btn-outline-info ms-2" onclick="debugLessonState()" title="Debug Lesson State">
                            <i class="bi bi-bug"></i> Debug
                        </button>
                    </div>
                `;

                container.innerHTML = contentHtml;

                // Si es lección de texto, inicializamos comentarios
                if (lessonType === 'text') {
                    // Carga los comentarios existentes
                    loadComments();
                    // Al formulario de comentarios le agregamos el listener
                    const form = document.getElementById('commentForm');
                    if (form) {
                        form.addEventListener('submit', submitComment);
                    }
                }

                updateNavigationButtons();
            }

            /**
             * Renderizar contenido de texto con imagen
             */
            function renderTextContent(lesson) {
                let contentHtml = '';

                // Verificar si hay imagen de lección específica
                let lessonImage = lesson.cover_img;

                // Si no hay imagen de lección, usar imagen del módulo o curso
                if (!lessonImage) {
                    // Primero intentar usar la imagen del módulo desde los datos de la lección
                    if (lesson.module_cover_img) {
                        lessonImage = lesson.module_cover_img;
                    }
                    // Si no, buscar en la estructura del curso
                    else if (asgState.currentCourse && asgState.currentCourse.modules) {
                        const lessonModule = asgState.currentCourse.modules.find(module =>
                            module.lessons && module.lessons.some(l => l.id_lesson == lesson.id)
                        );

                        if (lessonModule && lessonModule.cover_img) {
                            lessonImage = lessonModule.cover_img;
                        }
                    }

                    // Si no hay imagen del módulo, usar imagen del curso
                    if (!lessonImage && asgState.currentCourse) {
                        lessonImage = asgState.currentCourse.cover_img;
                    }
                }

                // Fallback a imagen placeholder si no hay ninguna imagen
                if (!lessonImage) {
                    lessonImage = 'https://via.placeholder.com/800x400/1e88e5/ffffff?text=Lección+de+Texto';
                }

                console.log('Renderizando contenido de texto:', {
                    lessonId: lesson.id,
                    lessonTitle: lesson.title,
                    hasContent: !!lesson.content,
                    images: {
                        lesson_cover: lesson.cover_img,
                        module_cover: lesson.module_cover_img,
                        course_cover: asgState.currentCourse?.cover_img,
                        final_image: lessonImage
                    },
                    imageSource: lessonImage?.includes('placeholder') ? 'placeholder' :
                                lessonImage === lesson.cover_img ? 'lesson' :
                                lessonImage === lesson.module_cover_img ? 'module' :
                                lessonImage === asgState.currentCourse?.cover_img ? 'course' : 'unknown'
                });

                contentHtml += '<div class="text-lesson-container">';

                // Mostrar imagen si existe
                if (lessonImage) {
                    const isLessonImage = lessonImage === lesson.cover_img;
                    const isModuleImage = lessonImage === lesson.module_cover_img;
                    const isCourseImage = lessonImage === asgState.currentCourse?.cover_img;
                    const isPlaceholder = lessonImage.includes('placeholder');

                    let captionText = 'Imagen del módulo';
                    if (isLessonImage) {
                        captionText = 'Imagen de la lección';
                    } else if (isModuleImage) {
                        captionText = 'Imagen del módulo';
                    } else if (isCourseImage) {
                        captionText = 'Imagen del curso';
                    } else if (isPlaceholder) {
                        captionText = 'Imagen de ejemplo';
                    }

                    contentHtml += `
                        <div class="lesson-image-container">
                            <img src="${lessonImage}"
                                 alt="${lesson.title}"
                                 class="lesson-image"
                                 onerror="this.style.display='none'">
                            <div class="image-caption">
                                <small class="text-muted">
                                    ${captionText}
                                </small>
                            </div>
                        </div>
                    `;
                }

                // Mostrar contenido de texto
                if (lesson.content) {
                    contentHtml += `
                        <div class="lesson-text-content">
                            ${lesson.content}
                        </div>
                    `;
                } else {
                    contentHtml += `
                        <div class="alert alert-info">
                            <i class="bi bi-info-circle me-2"></i>
                            No hay contenido disponible para esta lección.
                        </div>
                    `;
                }

                contentHtml += '</div>';


                    // Sección de comentarios
                    contentHtml += `
                        <div class="comment-section mt-5">
                            <h4><i class="bi bi-chat-dots me-2"></i>Comentarios</h4>
                            <form id="commentForm" class="mb-3">
                                <textarea id="commentText" class="form-control mb-2" rows="3" placeholder="Escribe tu comentario..." required></textarea>
                                <button class="btn btn-primary">Enviar</button>
                            </form>
                            <div id="commentsList" class="list-group">
                                <!-- Aquí se inyectarán los comentarios -->
                            </div>
                        </div>
                    `;


                return contentHtml;
            }

            /**
             * Renderizar contenido de video
             */
            function renderVideoContent(lesson) {
                if (!lesson.video_url) {
                    // Si no hay video pero hay contenido, mostrar el contenido
                    if (lesson.content) {
                        return `
                            <div class="lesson-content-text">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Esta lección contiene contenido de texto.
                                </div>
                                <div class="content-body">
                                    ${lesson.content}
                                </div>
                            </div>
                        `;
                    }
                    return `
                        <div class="alert alert-warning">
                            <i class="bi bi-exclamation-triangle me-2"></i>
                            No hay contenido de video disponible para esta lección.
                            <br><small class="text-muted">Contacta al instructor si esto es un error.</small>
                        </div>
                    `;
                }

                // Convertir URL de YouTube a formato embed si es necesario
                let embedUrl = lesson.video_url;
                if (lesson.video_url.includes('youtube.com/watch')) {
                    const videoId = lesson.video_url.split('v=')[1]?.split('&')[0];
                    if (videoId) {
                        embedUrl = `https://www.youtube.com/embed/${videoId}`;
                    }
                } else if (lesson.video_url.includes('youtu.be/')) {
                    const videoId = lesson.video_url.split('youtu.be/')[1]?.split('?')[0];
                    if (videoId) {
                        embedUrl = `https://www.youtube.com/embed/${videoId}`;
                    }
                }

                return `
                    <div class="video-container">
                        <iframe src="${embedUrl}"
                                allowfullscreen
                                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                        </iframe>
                    </div>
                    ${lesson.content ? `<div class="lesson-description">${lesson.content}</div>` : ''}
                `;
            }

            /**
             * Renderizar contenido de quiz
             */
            function renderQuizContent(lesson) {
                // Parse quiz data from content_lesson
                let quizData;
                try {
                    quizData = lesson.content ? JSON.parse(lesson.content) : null;
                } catch (e) {
                    quizData = null;
                }

                // If no quiz data, create a sample quiz
                if (!quizData || !quizData.questions) {
                    quizData = createSampleQuiz(lesson);
                }

                // Store quiz data globally
                asgState.currentQuiz = quizData;
                asgState.quizAnswers = [];
                asgState.currentQuestionIndex = 0;
                asgState.quizScore = 0;

                return `
                    <div class="quiz-lesson-container">
                        <div class="quiz-intro" id="quizIntro">
                            <div class="quiz-intro-content">
                                <h3><i class="bi bi-trophy me-2"></i>${quizData.title || lesson.title}</h3>
                                <p class="quiz-description">${quizData.description || 'Test your knowledge with this interactive quiz.'}</p>

                                <div class="quiz-stats">
                                    <div class="stat-item">
                                        <i class="bi bi-question-circle me-2"></i>
                                        <span>${quizData.questions.length} Questions</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="bi bi-clock me-2"></i>
                                        <span>10 minutes</span>
                                    </div>
                                    <div class="stat-item">
                                        <i class="bi bi-target me-2"></i>
                                        <span>${quizData.passing_score || 70}% to pass</span>
                                    </div>
                                </div>

                                <button class="btn btn-primary btn-lg start-quiz-btn" onclick="startQuiz()">
                                    <i class="bi bi-play-circle me-2"></i>Start Quiz
                                </button>
                            </div>
                        </div>

                        <div class="quiz-content" id="quizContent" style="display: none;">
                            <!-- Quiz questions will be rendered here -->
                        </div>

                        <div class="quiz-results" id="quizResults" style="display: none;">
                            <!-- Quiz results will be shown here -->
                        </div>
                    </div>
                `;
            }

            /**
             * Crear quiz de ejemplo si no hay datos
             */
            
            /**
             * Actualizar botón de completar lección
             */
            function updateLessonCompleteButton(lessonId, isCompleted) {
                const button = document.querySelector('.btn-complete');
                if (button) {
                    if (isCompleted) {
                        button.innerHTML = '<i class="bi bi-check-circle-fill me-2"></i>Completada';
                        button.classList.add('disabled');
                        button.disabled = true;
                    } else {
                        button.innerHTML = '<i class="bi bi-check-circle me-2"></i>Marcar como Completada';
                        button.classList.remove('disabled');
                        button.disabled = false;
                    }
                }
            }

            /**
             * Toggle de módulos (expandir/colapsar)
             */
            function toggleModule(moduleId) {
                const module = document.getElementById(moduleId);
                const chevron = document.getElementById(`chevron-${moduleId}`);

                if (!module || !chevron) return;

                if (module.classList.contains('show')) {
                    // Colapsar módulo
                    module.classList.remove('show');
                    chevron.classList.remove('rotated');
                } else {
                    // Expandir módulo (opcional: colapsar otros)
                    // Colapsar otros módulos
                    document.querySelectorAll('.module-lessons.show').forEach(openModule => {
                        if (openModule.id !== moduleId) {
                            openModule.classList.remove('show');
                            const otherChevron = document.getElementById(`chevron-${openModule.id}`);
                            if (otherChevron) {
                                otherChevron.classList.remove('rotated');
                            }
                        }
                    });

                    // Expandir módulo actual
                    module.classList.add('show');
                    chevron.classList.add('rotated');
                }
            }

            /**
             * Configurar navegación móvil
             */
            function setupMobileNavigation() {
                const sidebarToggle = document.getElementById('sidebarToggle');
                const sidebar = document.getElementById('lessonSidebar');

                if (sidebarToggle && sidebar) {
                    sidebarToggle.addEventListener('click', function(e) {
                        e.stopPropagation();
                        sidebar.classList.toggle('show');
                    });

                    // Cerrar sidebar al hacer clic fuera en móvil
                    document.addEventListener('click', function(e) {
                        if (window.innerWidth <= 992) {
                            if (!sidebar.contains(e.target) && !sidebarToggle.contains(e.target)) {
                                sidebar.classList.remove('show');
                            }
                        }
                    });

                    // Cerrar sidebar al navegar a una lección en móvil
                    sidebar.addEventListener('click', function(e) {
                        if (e.target.closest('.lesson-item') && window.innerWidth <= 992) {
                            setTimeout(() => {
                                sidebar.classList.remove('show');
                            }, 300);
                        }
                    });
                }
            }

            // ===== NAVEGACIÓN =====

            /**
             * Obtener lista ordenada de todas las lecciones
             */
            function getAllLessonsOrdered() {
                const allLessons = [];

                if (asgState.currentCourse && asgState.currentCourse.modules) {
                    asgState.currentCourse.modules.forEach(module => {
                        if (module.lessons) {
                            module.lessons.forEach(lesson => {
                                allLessons.push({
                                    id: parseInt(lesson.id_lesson),
                                    title: lesson.title_lesson,
                                    module: module.title_module
                                });
                            });
                        }
                    });
                }

                return allLessons.sort((a, b) => a.id - b.id);
            }

            /**
             * Encontrar índice de lección actual
             */
            function getCurrentLessonIndex() {
                const allLessons = getAllLessonsOrdered();
                const currentLessonId = parseInt(ASG_CONFIG.LESSON_ID);

                return allLessons.findIndex(lesson => lesson.id === currentLessonId);
            }

            /**
             * Navigate to previous lesson
             */
            function navigateToPrevious() {
                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();

                if (currentIndex <= 0) {
                    showNotification('This is the first lesson', 'info');
                    return;
                }

                const previousLesson = allLessons[currentIndex - 1];

                // Previous lessons are always accessible
                console.log('📖 Navigating to previous lesson:', previousLesson.title);
                navigateToLesson(previousLesson.id);
            }

            /**
             * Navigate to next lesson (Sequential Learning)
             */
            async function navigateToNext() {
                console.log('🚀 NEXT BUTTON CLICKED - Starting navigation process...');

                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();

                console.log('📊 Navigation Debug:', {
                    currentIndex,
                    totalLessons: allLessons.length,
                    currentLesson: allLessons[currentIndex],
                    nextLesson: allLessons[currentIndex + 1]
                });

                if (currentIndex >= allLessons.length - 1) {
                    showNotification('🎉 This is the last lesson! Congratulations!', 'success');
                    return;
                }

                const nextLesson = allLessons[currentIndex + 1];
                const currentLesson = allLessons[currentIndex];

                // Show current progress state BEFORE refresh
                console.log('📋 BEFORE REFRESH - Current Progress State:', {
                    courseProgress: asgState.courseProgress,
                    completedLessons: asgState.courseProgress?.completed_lessons_list || [],
                    currentLessonId: currentLesson.id,
                    nextLessonId: nextLesson.id,
                    isNextLocked: isLessonLocked(nextLesson.id)
                });

                // Force refresh progress before checking lock status
                console.log('🔄 Refreshing progress before navigation...');
                const updatedProgress = await loadUserProgress();

                // Show progress state AFTER refresh
                console.log('📋 AFTER REFRESH - Updated Progress State:', {
                    updatedProgress,
                    completedLessons: asgState.courseProgress?.completed_lessons_list || [],
                    isNextLocked: isLessonLocked(nextLesson.id)
                });

                // Check if next lesson is unlocked (Sequential Learning)
                if (isLessonLocked(nextLesson.id)) {
                    // Check if current lesson is completed
                    const completedLessons = asgState.courseProgress?.completed_lessons_list || [];
                    const isCurrentCompleted = completedLessons.includes(parseInt(currentLesson.id));

                    if (!isCurrentCompleted) {
                        showNotification('⚠️ Complete this lesson first to unlock the next one', 'warning');
                        // Highlight the complete button
                        const completeBtn = document.querySelector('.btn-complete');
                        if (completeBtn && !completeBtn.disabled) {
                            completeBtn.style.animation = 'pulse 1s infinite';
                            setTimeout(() => {
                                completeBtn.style.animation = '';
                            }, 3000);
                        }
                    } else {
                        showNotification('🔒 Next lesson is locked. Refreshing progress...', 'info');
                        // Try refreshing one more time
                        setTimeout(async () => {
                            await loadUserProgress();
                            refreshLessonStates();
                            if (!isLessonLocked(nextLesson.id)) {
                                console.log('✅ Next lesson now unlocked after refresh');
                                navigateToLesson(nextLesson.id);
                            } else {
                                showNotification('🔒 Next lesson is still locked. Please try again.', 'warning');
                            }
                        }, 1000);
                    }
                    return;
                }

                console.log('📖 Navigating to next lesson:', nextLesson.title);
                navigateToLesson(nextLesson.id);
            }

            /**
             * Obtener información de navegación
             */
            function getNavigationInfo() {
                const allLessons = getAllLessonsOrdered();
                const currentIndex = getCurrentLessonIndex();

                return {
                    currentIndex: currentIndex,
                    totalLessons: allLessons.length,
                    hasPrevious: currentIndex > 0,
                    hasNext: currentIndex < allLessons.length - 1,
                    previousLesson: currentIndex > 0 ? allLessons[currentIndex - 1] : null,
                    nextLesson: currentIndex < allLessons.length - 1 ? allLessons[currentIndex + 1] : null,
                    currentLesson: currentIndex >= 0 ? allLessons[currentIndex] : null
                };
            }

            /**
             * Actualizar estado de botones de navegación
             */
            function updateNavigationButtons() {
                if (!asgState.currentLesson) return;

                const currentIndex = asgState.lessonsList.findIndex(l => l.id == asgState.currentLesson.id);
                const prevBtn = document.getElementById('prevBtn');
                const nextBtn = document.getElementById('nextBtn');

                if (prevBtn) {
                    prevBtn.disabled = currentIndex <= 0;
                    prevBtn.style.opacity = currentIndex <= 0 ? '0.5' : '1';
                }

                if (nextBtn) {
                    nextBtn.disabled = currentIndex >= asgState.lessonsList.length - 1;
                    nextBtn.style.opacity = currentIndex >= asgState.lessonsList.length - 1 ? '0.5' : '1';
                }
            }

            // ===== FUNCIONES AUXILIARES =====

            /**
             * Mostrar mensaje de error
             */
            function showError(message) {
                const container = document.getElementById('lessonContentArea');
                container.innerHTML = `
                    <div class="text-center py-5">
                        <i class="bi bi-exclamation-triangle text-warning" style="font-size: 3rem;"></i>
                        <h3 class="mt-3">Error</h3>
                        <p class="text-muted">${message}</p>
                        <button class="btn btn-primary" onclick="loadCourseData()">
                            <i class="bi bi-arrow-clockwise me-2"></i>Reintentar
                        </button>
                    </div>
                `;
            }

            /**
             * Iniciar quiz
             */
            function startQuiz() {
                if (!asgState.currentQuiz || !asgState.currentQuiz.questions) {
                    showNotification('No hay datos de quiz disponibles', 'error');
                    return;
                }

                // Reset quiz state
                asgState.quizAnswers = [];
                asgState.currentQuestionIndex = 0;
                asgState.quizScore = 0;

                // Hide intro and show quiz content
                document.getElementById('quizIntro').style.display = 'none';
                document.getElementById('quizContent').style.display = 'block';
                document.getElementById('quizResults').style.display = 'none';

                // Render first question
                renderQuizQuestion();
            }

            /**
             * Renderizar pregunta actual del quiz
             */
            function renderQuizQuestion() {
                const question = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                const totalQuestions = asgState.currentQuiz.questions.length;
                const progress = ((asgState.currentQuestionIndex + 1) / totalQuestions) * 100;

                const quizContent = document.getElementById('quizContent');

                let optionsHtml = '';
                let instructionText = '';

                if (question.type === 'multiple_choice') {
                    instructionText = question.allow_multiple ? 'Select all that apply:' : 'Select one option:';
                    const inputType = question.allow_multiple ? 'checkbox' : 'radio';

                    optionsHtml = question.options.map((option, index) => `
                        <div class="quiz-option" onclick="selectQuizOption(${index}, ${question.allow_multiple || false})">
                            <input type="${inputType}" name="question_${question.id}" value="${index}" style="pointer-events: none;">
                            <span class="option-text">${option}</span>
                        </div>
                    `).join('');
                } else if (question.type === 'true_false') {
                    instructionText = 'Select True or False:';
                    optionsHtml = `
                        <div class="quiz-option" onclick="selectQuizOption(true, false)">
                            <input type="radio" name="question_${question.id}" value="true" style="pointer-events: none;">
                            <span class="option-text">True</span>
                        </div>
                        <div class="quiz-option" onclick="selectQuizOption(false, false)">
                            <input type="radio" name="question_${question.id}" value="false" style="pointer-events: none;">
                            <span class="option-text">False</span>
                        </div>
                    `;
                } else if (question.type === 'subjective') {
                    instructionText = 'Write your answer:';
                    optionsHtml = `
                        <div class="subjective-answer">
                            <textarea class="form-control" id="subjective_answer" rows="4"
                                      placeholder="Type your answer here..."></textarea>
                        </div>
                    `;
                }

                quizContent.innerHTML = `
                    <div class="quiz-header">
                        <div class="quiz-progress">
                            <div class="progress-bar" style="width: ${progress}%"></div>
                        </div>
                        <div class="question-counter">
                            Question ${asgState.currentQuestionIndex + 1} of ${totalQuestions}
                        </div>
                    </div>

                    <div class="quiz-question-content">
                        <h4 class="question-text">${question.question}</h4>
                        <p class="instruction-text">${instructionText}</p>

                        <div class="quiz-options">
                            ${optionsHtml}
                        </div>
                    </div>

                    <div class="quiz-navigation">
                        <button class="btn btn-outline-secondary" onclick="previousQuestion()"
                                ${asgState.currentQuestionIndex === 0 ? 'disabled' : ''}>
                            <i class="bi bi-arrow-left me-2"></i>Previous
                        </button>

                        <button class="btn btn-primary" onclick="nextQuestion()" id="nextQuestionBtn">
                            ${asgState.currentQuestionIndex === totalQuestions - 1 ? 'Finish Quiz' : 'Next'}
                            <i class="bi bi-arrow-right ms-2"></i>
                        </button>
                    </div>
                `;

                // Restore previous answer if exists
                const previousAnswer = asgState.quizAnswers[asgState.currentQuestionIndex];
                if (previousAnswer !== undefined) {
                    restorePreviousAnswer(previousAnswer, question);
                }
            }

            // ===== INICIALIZACIÓN =====

            /**
             * Inicializar la aplicación cuando el DOM esté listo
             */
            document.addEventListener('DOMContentLoaded', function() {
                console.log('=== ASG Lessons System Iniciado ===');
                console.log('URL actual:', window.location.href);
                console.log('Configuración final:', ASG_CONFIG);

                // Verificar configuración básica
                if (!ASG_CONFIG.COURSE_CODE) {
                    console.error('COURSE_CODE no definido después de fallbacks');
                    console.log('Parámetros URL disponibles:', new URLSearchParams(window.location.search));
                    showError('Código de curso no especificado en la URL');
                    return;
                }

                // Verificar elementos DOM necesarios
                const requiredElements = ['lessonsList', 'lessonContentArea', 'progressText', 'progressFill'];
            

                console.log('Elementos DOM verificados correctamente');

                // Cargar datos del curso
                console.log('Iniciando carga de datos del curso...');
                loadCourseData().catch(error => {
                    console.error('Error en inicialización:', error);
                    showError('Error de inicialización del sistema');
                });

                // Configurar navegación móvil
                setupMobileNavigation();

                // Configurar navegación del navegador
                window.addEventListener('popstate', function() {
                    const lessonId = getCurrentLessonId();
                    if (lessonId) {
                        loadLessonContent(lessonId);
                    }
                });

                console.log('Sistema inicializado correctamente');
            });

            /**
             * Seleccionar opción de quiz
             */
            function selectQuizOption(value, isMultiple = false) {
                if (isMultiple) {
                    // Handle multiple selection (checkboxes)
                    const checkbox = event.currentTarget.querySelector('input[type="checkbox"]');
                    checkbox.checked = !checkbox.checked;

                    // Update visual selection
                    if (checkbox.checked) {
                        event.currentTarget.classList.add('selected');
                    } else {
                        event.currentTarget.classList.remove('selected');
                    }

                    // Update answers array
                    let currentAnswers = asgState.quizAnswers[asgState.currentQuestionIndex] || [];
                    if (checkbox.checked) {
                        if (!currentAnswers.includes(value)) {
                            currentAnswers.push(value);
                        }
                    } else {
                        currentAnswers = currentAnswers.filter(answer => answer !== value);
                    }
                    asgState.quizAnswers[asgState.currentQuestionIndex] = currentAnswers;
                } else {
                    // Handle single selection (radio buttons)
                    const radio = event.currentTarget.querySelector('input[type="radio"]');
                    radio.checked = true;

                    // Remove selection from other options
                    document.querySelectorAll('.quiz-option').forEach(option => {
                        option.classList.remove('selected');
                    });

                    // Add selection to current option
                    event.currentTarget.classList.add('selected');

                    // Update answer
                    asgState.quizAnswers[asgState.currentQuestionIndex] = value;
                }
            }

            /**
             * Navegar a pregunta siguiente
             */
            function nextQuestion() {
                // Save current answer for subjective questions
                const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                if (currentQuestion.type === 'subjective') {
                    const textarea = document.getElementById('subjective_answer');
                    if (textarea) {
                        asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                    }
                }

                if (asgState.currentQuestionIndex < asgState.currentQuiz.questions.length - 1) {
                    asgState.currentQuestionIndex++;
                    renderQuizQuestion();
                } else {
                    finishQuiz();
                }
            }

            /**
             * Navegar a pregunta anterior
             */
            function previousQuestion() {
                if (asgState.currentQuestionIndex > 0) {
                    asgState.currentQuestionIndex--;
                    renderQuizQuestion();
                }
            }

            /**
             * Restaurar respuesta anterior
             */
            function restorePreviousAnswer(previousAnswer, question) {
                if (question.type === 'multiple_choice' && question.allow_multiple && Array.isArray(previousAnswer)) {
                    // Restore multiple selections
                    previousAnswer.forEach(value => {
                        const option = document.querySelector(`input[value="${value}"]`);
                        if (option) {
                            option.checked = true;
                            option.closest('.quiz-option').classList.add('selected');
                        }
                    });
                } else if (question.type === 'subjective') {
                    // Restore subjective answer
                    const textarea = document.getElementById('subjective_answer');
                    if (textarea && previousAnswer) {
                        textarea.value = previousAnswer;
                    }
                } else {
                    // Restore single selection
                    const option = document.querySelector(`input[value="${previousAnswer}"]`);
                    if (option) {
                        option.checked = true;
                        option.closest('.quiz-option').classList.add('selected');
                    }
                }
            }

            // Hacer funciones disponibles globalmente para onclick
            window.navigateToLesson = navigateToLesson;
            window.navigateToPrevious = navigateToPrevious;
            window.navigateToNext = navigateToNext;
            window.markLessonComplete = markLessonComplete;
            window.startQuiz = startQuiz;
            window.loadCourseData = loadCourseData;
            window.toggleModule = toggleModule;
            /**
             * Finalizar quiz y mostrar resultados
             */
            async function finishQuiz() {
                try {
                    console.log('Finalizando quiz...');

                    // Save current answer for subjective questions
                    const currentQuestion = asgState.currentQuiz.questions[asgState.currentQuestionIndex];
                    if (currentQuestion.type === 'subjective') {
                        const textarea = document.getElementById('subjective_answer');
                        if (textarea) {
                            asgState.quizAnswers[asgState.currentQuestionIndex] = textarea.value;
                        }
                    }

                    // Calculate score
                    const scoreData = calculateQuizScore();
                    const passed = scoreData.score >= (asgState.currentQuiz.passing_score || 70);

                    // Show results
                    showQuizResults(scoreData, passed);

                    // If passed, mark lesson as complete
                    if (passed) {
                        setTimeout(() => {
                            markLessonComplete();
                        }, 2000);
                    }

                } catch (error) {
                    console.error('Error finishing quiz:', error);
                    showNotification('Error al finalizar el quiz', 'error');
                }
            }

            /**
             * Calcular puntuación del quiz
             */
            function calculateQuizScore() {
                let correctAnswers = 0;
                let totalQuestions = asgState.currentQuiz.questions.length;
                let subjectiveAnswers = 0;
                let totalPoints = 0;
                let earnedPoints = 0;

                asgState.currentQuiz.questions.forEach((question, index) => {
                    const userAnswer = asgState.quizAnswers[index];
                    const questionPoints = question.points || 25;
                    totalPoints += questionPoints;

                    if (question.type === 'subjective') {
                        // Subjective questions are auto-passed if answered
                        if (userAnswer && userAnswer.trim().length > 0) {
                            correctAnswers++;
                            earnedPoints += questionPoints;
                            subjectiveAnswers++;
                        }
                    } else if (question.type === 'multiple_choice' && question.allow_multiple) {
                        // Multiple selection questions
                        const correctAnswerSet = new Set(question.correct_answer);
                        const userAnswerSet = new Set(Array.isArray(userAnswer) ? userAnswer : []);

                        if (correctAnswerSet.size === userAnswerSet.size &&
                            [...correctAnswerSet].every(answer => userAnswerSet.has(answer))) {
                            correctAnswers++;
                            earnedPoints += questionPoints;
                        }
                    } else {
                        // Single selection questions
                        if (Array.isArray(question.correct_answer)) {
                            if (question.correct_answer.includes(userAnswer)) {
                                correctAnswers++;
                                earnedPoints += questionPoints;
                            }
                        } else {
                            if (question.correct_answer === userAnswer) {
                                correctAnswers++;
                                earnedPoints += questionPoints;
                            }
                        }
                    }
                });

                const score = totalPoints > 0 ? Math.round((earnedPoints / totalPoints) * 100) : 0;

                return {
                    score: score,
                    correct_answers: correctAnswers,
                    total_questions: totalQuestions,
                    subjective_answers: subjectiveAnswers,
                    earned_points: earnedPoints,
                    total_points: totalPoints
                };
            }

            /**
             * Mostrar resultados del quiz
             */
            function showQuizResults(scoreData, passed) {
                const passingScore = asgState.currentQuiz.passing_score || 70;

                document.getElementById('quizContent').style.display = 'none';
                const resultsContainer = document.getElementById('quizResults');
                resultsContainer.style.display = 'block';

                resultsContainer.innerHTML = `
                    <div class="quiz-results-container">
                        <div class="results-header ${passed ? 'passed' : 'failed'}">
                            <div class="score-circle">
                                <div class="score-number">${scoreData.score}%</div>
                            </div>
                            <h3>${passed ? '🎉 Congratulations!' : '😔 Try Again'}</h3>
                            <p>${passed ? 'You passed the quiz!' : `You need ${passingScore}% to pass.`}</p>
                        </div>

                        <div class="results-details">
                            <div class="detail-item">
                                <span class="label">Correct Answers:</span>
                                <span class="value">${scoreData.correct_answers}/${scoreData.total_questions}</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Score:</span>
                                <span class="value">${scoreData.score}%</span>
                            </div>
                            <div class="detail-item">
                                <span class="label">Passing Score:</span>
                                <span class="value">${passingScore}%</span>
                            </div>
                            ${scoreData.subjective_answers > 0 ? `
                                <div class="detail-item">
                                    <span class="label">Subjective Questions:</span>
                                    <span class="value">${scoreData.subjective_answers}</span>
                                </div>
                            ` : ''}
                        </div>

                        <div class="results-actions">
                            ${!passed ? `
                                <button class="btn btn-warning" onclick="retakeQuiz()">
                                    <i class="bi bi-arrow-clockwise me-2"></i>Retake Quiz
                                </button>
                            ` : ''}
                            <button class="btn btn-primary" onclick="continueToNextLesson()">
                                <i class="bi bi-arrow-right me-2"></i>Continue
                            </button>
                        </div>
                    </div>
                `;
            }

            /**
             * Reintentar quiz
             */
            function retakeQuiz() {
                document.getElementById('quizResults').style.display = 'none';
                startQuiz();
            }

            /**
             * Continuar a siguiente lección
             */
            function continueToNextLesson() {
                navigateToNext();
            }

            window.selectQuizOption = selectQuizOption;

            /**
             * Carga los comentarios desde el backend y los inyecta en #commentsList
             */
            async function loadComments() {
            const lessonId = ASG_CONFIG.LESSON_ID;
            try {
                const res = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/comments`);
                const data = await res.json();
                const list = document.getElementById('commentsList');
                if (data.success && Array.isArray(data.comments)) {
                list.innerHTML = data.comments.map(c => `
                    <div class="list-group-item">
                    <strong>${c.author || 'Anónimo'}</strong>
                    <p class="mb-1">${c.comment}</p>
                    <small class="text-muted">${c.date || ''}</small>
                    </div>
                `).join('');
                } else {
                list.innerHTML = `<div class="list-group-item text-muted">No hay comentarios aún.</div>`;
                }
            } catch (e) {
                console.error('Error cargando comentarios:', e);
            }
            }

            /**
             * Envía un comentario nuevo y, si va bien, recarga la lista
             */
            async function submitComment(event) {
            event.preventDefault();
            const lessonId = ASG_CONFIG.LESSON_ID;
            const textEl = document.getElementById('commentText');
            const comment = textEl.value.trim();
            if (!comment) return;
            try {
                const res = await fetch(`${ASG_CONFIG.API_BASE}/lessons/${lessonId}/comments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-WP-Nonce': document.querySelector('meta[name="wp-nonce"]').content
                },
                body: JSON.stringify({ comment })
                });
                const data = await res.json();
                if (data.success) {
                textEl.value = '';
                loadComments();
                } else {
                console.warn('Error al enviar comentario:', data);
                }
            } catch (e) {
                console.error('Error al enviar comentario:', e);
            }
            }

            // Exponer para el formulario y renderizado
            window.loadComments = loadComments;
            window.submitComment = submitComment;

            window.nextQuestion = nextQuestion;
            window.previousQuestion = previousQuestion;
            window.finishQuiz = finishQuiz;
            window.retakeQuiz = retakeQuiz;
            window.continueToNextLesson = continueToNextLesson;
            window.findNextUnlockedLesson = findNextUnlockedLesson;

            // DEBUG FUNCTION - Remove after testing
            window.debugLessonState = debugLessonState;

            })(); // Cerrar función anónima

        </script>
    <?php endif; ?>
</body>
</html>
<?php
}

/**
 * Shortcode para renderizar la página de lecciones
 * Uso: [asg_lessons]
 */
function asg_lessons_shortcode($atts) {
    // Capturar la salida en buffer
    ob_start();

    // Renderizar la página
    asg_render_lessons_page();

    // Retornar el contenido capturado
    return ob_get_clean();
}

// Registrar el shortcode
add_shortcode('asg_lessons', 'asg_lessons_shortcode');

/**
 * Función para usar en templates de WordPress
 * Uso en PHP: <?php echo do_shortcode('[asg_lessons]'); ?>
 */
function asg_display_lessons() {
    echo do_shortcode('[asg_lessons]');
}

// Solo renderizar directamente si se accede al archivo directamente (para testing)
if (basename($_SERVER['PHP_SELF']) === 'lessons-clean.php') {
    asg_render_lessons_page();
}
