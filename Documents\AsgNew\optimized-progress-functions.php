<?php
/**
 * FUNCIONES OPTIMIZADAS PARA PROGRESO DE ESTUDIANTES
 * Aprovecha la nueva estructura optimizada sin cambiar la tabla original
 */

/**
 * Obtener progreso de un estudiante en un curso (OPTIMIZADO)
 * Usa cache si está disponible, sino consulta directa optimizada
 */
function get_student_course_progress_optimized($student_id, $course_code) {
    global $wpdb;
    
    // Intentar obtener desde cache primero
    $cached_progress = $wpdb->get_row($wpdb->prepare("
        SELECT * FROM wpic_asg_progress_cache 
        WHERE student_id = %d AND course_code = %s
    ", $student_id, $course_code));
    
    if ($cached_progress) {
        return [
            'total_lessons' => intval($cached_progress->total_lessons),
            'completed_lessons' => intval($cached_progress->completed_lessons),
            'progress_percentage' => floatval($cached_progress->progress_percentage),
            'completed_lessons_list' => $cached_progress->completed_lessons_list ? 
                array_map('intval', explode(',', $cached_progress->completed_lessons_list)) : [],
            'last_completed_lesson' => $cached_progress->last_completed_lesson,
            'last_completion_date' => $cached_progress->last_completion_date,
            'source' => 'cache'
        ];
    }
    
    // Fallback a consulta directa optimizada
    $progress = $wpdb->get_row($wpdb->prepare("
        SELECT 
            COUNT(*) as total_lessons,
            SUM(completed) as completed_lessons,
            ROUND((SUM(completed) / COUNT(*)) * 100, 2) as progress_percentage,
            GROUP_CONCAT(CASE WHEN completed = 1 THEN lesson_id END ORDER BY lesson_id) as completed_lessons_list,
            MAX(CASE WHEN completed = 1 THEN lesson_id END) as last_completed_lesson,
            MAX(CASE WHEN completed = 1 THEN completion_date END) as last_completion_date
        FROM wpic_asg_progress 
        WHERE student_id = %d AND course_code = %s
    ", $student_id, $course_code));
    
    if ($progress) {
        return [
            'total_lessons' => intval($progress->total_lessons),
            'completed_lessons' => intval($progress->completed_lessons),
            'progress_percentage' => floatval($progress->progress_percentage),
            'completed_lessons_list' => $progress->completed_lessons_list ? 
                array_map('intval', explode(',', $progress->completed_lessons_list)) : [],
            'last_completed_lesson' => $progress->last_completed_lesson,
            'last_completion_date' => $progress->last_completion_date,
            'source' => 'direct'
        ];
    }
    
    return null;
}

/**
 * Completar lección (OPTIMIZADO)
 * Actualiza tanto la tabla original como el cache
 */
function complete_lesson_optimized($student_id, $lesson_id, $course_code = null) {
    global $wpdb;
    
    // Si no se proporciona course_code, obtenerlo
    if (!$course_code) {
        $course_code = $wpdb->get_var($wpdb->prepare("
            SELECT m.code_course 
            FROM wpic_lessons l 
            JOIN wpic_modules m ON l.code_module = m.code_module 
            WHERE l.id_lesson = %d
        ", $lesson_id));
    }
    
    if (!$course_code) {
        return ['success' => false, 'message' => 'No se pudo determinar el curso'];
    }
    
    // Verificar si existe el registro
    $existing = $wpdb->get_var($wpdb->prepare("
        SELECT id_progress FROM wpic_asg_progress 
        WHERE student_id = %d AND lesson_id = %d
    ", $student_id, $lesson_id));
    
    if ($existing) {
        // Actualizar registro existente
        $result = $wpdb->update('wpic_asg_progress', [
            'completed' => 1,
            'completion_date' => current_time('mysql'),
            'updated_at' => current_time('mysql'),
            'course_code' => $course_code  // Asegurar que course_code esté poblado
        ], [
            'id_progress' => $existing
        ]);
    } else {
        // Crear nuevo registro
        $result = $wpdb->insert('wpic_asg_progress', [
            'student_id' => $student_id,
            'lesson_id' => $lesson_id,
            'course_code' => $course_code,
            'completed' => 1,
            'completion_date' => current_time('mysql'),
            'created_at' => current_time('mysql'),
            'updated_at' => current_time('mysql')
        ]);
    }
    
    if ($result !== false) {
        // Actualizar cache manualmente si el trigger no está disponible
        $wpdb->query($wpdb->prepare("CALL UpdateProgressCache(%d, %s)", $student_id, $course_code));
        
        // Obtener progreso actualizado
        $updated_progress = get_student_course_progress_optimized($student_id, $course_code);
        
        return [
            'success' => true,
            'message' => 'Lección completada exitosamente',
            'progress' => $updated_progress
        ];
    }
    
    return ['success' => false, 'message' => 'Error al completar la lección'];
}

/**
 * Inicializar progreso para un curso (OPTIMIZADO)
 * Crea registros solo si no existen y pobla course_code
 */
function initialize_course_progress_optimized($student_id, $course_code) {
    global $wpdb;
    
    // Obtener todas las lecciones del curso
    $lessons = $wpdb->get_results($wpdb->prepare("
        SELECT l.id_lesson 
        FROM wpic_lessons l
        JOIN wpic_modules m ON l.code_module = m.code_module
        WHERE m.code_course = %s
        ORDER BY m.order_module, l.order_lesson
    ", $course_code));
    
    if (empty($lessons)) {
        return ['success' => false, 'message' => 'No se encontraron lecciones para el curso'];
    }
    
    $created_count = 0;
    $updated_count = 0;
    
    foreach ($lessons as $lesson) {
        // Verificar si ya existe
        $existing = $wpdb->get_var($wpdb->prepare("
            SELECT id_progress FROM wpic_asg_progress 
            WHERE student_id = %d AND lesson_id = %d
        ", $student_id, $lesson->id_lesson));
        
        if (!$existing) {
            // Crear nuevo registro
            $result = $wpdb->insert('wpic_asg_progress', [
                'student_id' => $student_id,
                'lesson_id' => $lesson->id_lesson,
                'course_code' => $course_code,
                'completed' => 0,
                'created_at' => current_time('mysql'),
                'updated_at' => current_time('mysql')
            ]);
            
            if ($result) $created_count++;
        } else {
            // Actualizar course_code si no existe
            $updated = $wpdb->update('wpic_asg_progress', [
                'course_code' => $course_code,
                'updated_at' => current_time('mysql')
            ], [
                'id_progress' => $existing
            ], ['%s', '%s'], ['%d']);
            
            if ($updated) $updated_count++;
        }
    }
    
    // Actualizar cache
    $wpdb->query($wpdb->prepare("CALL UpdateProgressCache(%d, %s)", $student_id, $course_code));
    
    return [
        'success' => true,
        'message' => "Progreso inicializado: {$created_count} creados, {$updated_count} actualizados",
        'created' => $created_count,
        'updated' => $updated_count,
        'total_lessons' => count($lessons)
    ];
}

/**
 * Obtener progreso de todos los cursos de un estudiante (OPTIMIZADO)
 */
function get_student_all_courses_progress_optimized($student_id) {
    global $wpdb;
    
    // Usar cache si está disponible
    $cached_courses = $wpdb->get_results($wpdb->prepare("
        SELECT 
            pc.*,
            c.name_course,
            c.cover_img,
            c.description_course
        FROM wpic_asg_progress_cache pc
        JOIN wpic_courses c ON pc.course_code = c.code_course
        WHERE pc.student_id = %d
        ORDER BY pc.last_completion_date DESC, c.name_course
    ", $student_id));
    
    if (!empty($cached_courses)) {
        $result = [];
        foreach ($cached_courses as $course) {
            $result[] = [
                'course_code' => $course->course_code,
                'course_name' => $course->name_course,
                'course_image' => $course->cover_img,
                'course_description' => $course->description_course,
                'total_lessons' => intval($course->total_lessons),
                'completed_lessons' => intval($course->completed_lessons),
                'progress_percentage' => floatval($course->progress_percentage),
                'completed_lessons_list' => $course->completed_lessons_list ? 
                    array_map('intval', explode(',', $course->completed_lessons_list)) : [],
                'last_completed_lesson' => $course->last_completed_lesson,
                'last_completion_date' => $course->last_completion_date,
                'source' => 'cache'
            ];
        }
        return $result;
    }
    
    // Fallback a consulta directa
    $courses = $wpdb->get_results($wpdb->prepare("
        SELECT 
            p.course_code,
            c.name_course,
            c.cover_img,
            c.description_course,
            COUNT(*) as total_lessons,
            SUM(p.completed) as completed_lessons,
            ROUND((SUM(p.completed) / COUNT(*)) * 100, 2) as progress_percentage,
            GROUP_CONCAT(CASE WHEN p.completed = 1 THEN p.lesson_id END ORDER BY p.lesson_id) as completed_lessons_list,
            MAX(CASE WHEN p.completed = 1 THEN p.lesson_id END) as last_completed_lesson,
            MAX(CASE WHEN p.completed = 1 THEN p.completion_date END) as last_completion_date
        FROM wpic_asg_progress p
        JOIN wpic_courses c ON p.course_code = c.code_course
        WHERE p.student_id = %d AND p.course_code IS NOT NULL
        GROUP BY p.course_code, c.name_course, c.cover_img, c.description_course
        ORDER BY last_completion_date DESC, c.name_course
    ", $student_id));
    
    $result = [];
    foreach ($courses as $course) {
        $result[] = [
            'course_code' => $course->course_code,
            'course_name' => $course->name_course,
            'course_image' => $course->cover_img,
            'course_description' => $course->description_course,
            'total_lessons' => intval($course->total_lessons),
            'completed_lessons' => intval($course->completed_lessons),
            'progress_percentage' => floatval($course->progress_percentage),
            'completed_lessons_list' => $course->completed_lessons_list ? 
                array_map('intval', explode(',', $course->completed_lessons_list)) : [],
            'last_completed_lesson' => $course->last_completed_lesson,
            'last_completion_date' => $course->last_completion_date,
            'source' => 'direct'
        ];
    }
    
    return $result;
}

/**
 * Poblar course_code en registros existentes (UTILIDAD)
 */
function populate_missing_course_codes() {
    global $wpdb;
    
    $updated = $wpdb->query("
        UPDATE wpic_asg_progress p
        JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
        JOIN wpic_modules m ON l.code_module = m.code_module
        SET p.course_code = m.code_course
        WHERE p.course_code IS NULL
    ");
    
    return [
        'success' => true,
        'message' => "Se actualizaron {$updated} registros con course_code",
        'updated_count' => $updated
    ];
}

/**
 * Reconstruir cache completo (UTILIDAD)
 */
function rebuild_progress_cache() {
    global $wpdb;
    
    // Limpiar cache existente
    $wpdb->query("TRUNCATE TABLE wpic_asg_progress_cache");
    
    // Poblar desde vista
    $inserted = $wpdb->query("
        INSERT INTO wpic_asg_progress_cache 
        (student_id, course_code, total_lessons, completed_lessons, progress_percentage, 
         completed_lessons_list, last_completed_lesson, last_completion_date)
        SELECT 
            student_id,
            course_code,
            total_lessons,
            completed_lessons,
            progress_percentage,
            IFNULL(completed_lessons_list, '') as completed_lessons_list,
            last_completed_lesson,
            last_completion_date
        FROM v_user_course_progress
    ");
    
    return [
        'success' => true,
        'message' => "Cache reconstruido con {$inserted} registros",
        'cache_records' => $inserted
    ];
}
?>
