/**
 * ASG Edit Course - Fully Optimized Version
 * Uses ONLY optimized /api endpoints for maximum performance
 * Created: 2025-01-07
 * Author: ING. <PERSON>
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Create optimized edit course page
 */
function asg_create_edit_course_optimized_page() {
    // Verify permissions
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have permission to access this page.'));
    }
    
    // Get site URL
    $site_url = get_site_url();
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Edit Course - ASG Optimized</title>
        
        <!-- Bootstrap 5 -->
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">
        
        <!-- Tailwind CSS -->
        <script src="https://cdn.tailwindcss.com"></script>
        <script>
            tailwind.config = {
                theme: {
                    extend: {
                        colors: {
                            primary: '#2563eb',
                            secondary: '#64748b',
                            success: '#10b981',
                            warning: '#f59e0b',
                            danger: '#ef4444',
                        }
                    }
                }
            }
        </script>
        
        <style>
            .wg-li weglot-lang weglot-language weglot-flags flag-3 wg-es{
                display: none !important;
            }

            /* Navbar - keeping original styles */
            .navbar {
                background: linear-gradient(135deg, #1e3a5f 0%, #2563eb 100%);
                height: 70px;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1000;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }
            .navbar-brand img { width: 130px; height: auto; }
            .navbar-nav .nav-link {
                color: white !important;
                font-weight: 500;
                padding: 0.5rem 1rem;
                border-radius: 6px;
                transition: all 0.3s ease;
            }
            .navbar-nav .nav-link:hover {
                background-color: rgba(255,255,255,0.1);
                transform: translateY(-1px);
            }

            /* Essential styles only - most styling handled by Tailwind */
            .hero-image-overlay {
                opacity: 0;
                transition: opacity 0.3s ease;
            }

            .hero-image-container:hover .hero-image-overlay {
                opacity: 1;
            }

            /* Line clamp utility for text truncation */
            .line-clamp-2 {
                display: -webkit-box;
                -webkit-line-clamp: 2;
                -webkit-box-orient: vertical;
                overflow: hidden;
            }

            /* Quiz Type Selection Styles */
            .quiz-type-option {
                cursor: pointer;
                transition: all 0.3s ease;
                border-radius: 8px;
                position: relative;
            }

            .quiz-type-option input[type="radio"] {
                display: none;
            }

            .quiz-type-label {
                display: block;
                padding: 1rem;
                border: 2px solid #e5e7eb;
                border-radius: 8px;
                text-align: center;
                transition: all 0.3s ease;
                cursor: pointer;
                background: white;
            }

            .quiz-type-option:hover .quiz-type-label {
                border-color: #3b82f6;
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
            }

            .quiz-type-option input[type="radio"]:checked + .quiz-type-label {
                border-color: #3b82f6;
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
                box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
            }

            .quiz-type-icon {
                font-size: 2rem;
                margin-bottom: 0.5rem;
            }

            .quiz-type-title {
                font-weight: 600;
                color: #374151;
                margin-bottom: 0.25rem;
            }

            .quiz-type-desc {
                font-size: 0.875rem;
                color: #6b7280;
                line-height: 1.4;
            }

            .quiz-type-option input[type="radio"]:checked + .quiz-type-label .quiz-type-title {
                color: #1d4ed8;
            }

            /* Video preview styles */
            .aspect-video {
                aspect-ratio: 16 / 9;
            }

            /* Preview badge animation */
            .preview-badge {
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0%, 100% { opacity: 1; }
                50% { opacity: 0.8; }
            }

            .loading-skeleton {
                background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
                background-size: 200% 100%;
                animation: loading 1.5s infinite;
            }

            @keyframes loading {
                0% { background-position: 200% 0; }
                100% { background-position: -200% 0; }
            }

            /* Course status colors */
            .status-published { background: #d1fae5; color: #065f46; }
            .status-draft { background: #fef3c7; color: #92400e; }
            .status-archived { background: #f3f4f6; color: #374151; }

            /* Toast Container */
            .toast-container {
                position: fixed;
                top: 90px;
                right: 20px;
                z-index: 9999;
            }

            .toast {
                background: white;
                border-radius: 8px;
                padding: 1rem 1.5rem;
                margin-bottom: 0.5rem;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                border-left: 4px solid #2563eb;
                animation: slideIn 0.3s ease-out;
                cursor: pointer;
                max-width: 400px;
            }

            .toast.success { border-left-color: #10b981; }
            .toast.error { border-left-color: #ef4444; }
            .toast.warning { border-left-color: #f59e0b; }

            @keyframes slideIn {
                from { transform: translateX(100%); opacity: 0; }
                to { transform: translateX(0); opacity: 1; }
            }

            /* Step Progress Styles */
            .step-progress {
                display: flex;
                justify-content: center;
                align-items: center;
                gap: 2rem;
                margin-bottom: 2rem;
            }

            .step {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: 0.5rem;
                opacity: 0.5;
                transition: opacity 0.3s ease;
            }

            .step.active {
                opacity: 1;
            }

            .step.completed {
                opacity: 1;
                color: #10b981;
            }

            .step-number {
                width: 40px;
                height: 40px;
                border-radius: 50%;
                background: #e5e7eb;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: 600;
                transition: all 0.3s ease;
            }

            .step.active .step-number {
                background: #2563eb;
                color: white;
            }

            .step.completed .step-number {
                background: #10b981;
                color: white;
            }

            .step-label {
                font-size: 0.875rem;
                font-weight: 500;
            }

            /* Lesson Item Styles */
            .lesson-item {
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 1rem;
                margin-bottom: 1rem;
                background: #f8fafc;
                transition: all 0.2s ease;
            }

            .lesson-item:hover {
                border-color: #2563eb;
                box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
            }

            .lesson-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 0.75rem;
            }

            .lesson-type-badge {
                display: inline-flex;
                align-items: center;
                gap: 0.25rem;
                padding: 0.25rem 0.5rem;
                border-radius: 4px;
                font-size: 0.75rem;
                font-weight: 500;
            }

            .lesson-type-text {
                background: #dbeafe;
                color: #1e40af;
            }

            .lesson-type-video {
                background: #fef3c7;
                color: #92400e;
            }

            .lesson-type-quiz {
                background: #d1fae5;
                color: #065f46;
            }

            /* Quiz Editor Styles */
            .quiz-question-item {
                transition: all 0.2s ease;
            }

            .quiz-question-item:hover {
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
                transform: translateY(-1px);
            }

            .quiz-question-item h5 {
                color: #1f2937;
                font-weight: 600;
            }

            .quiz-question-item .bi-hash {
                color: #3b82f6;
            }

            .quiz-question-item input[type="radio"]:checked + span {
                background-color: #3b82f6;
                color: white;
            }

            .quiz-question-item textarea:focus,
            .quiz-question-item input:focus {
                outline: none;
                ring: 2px;
                ring-color: #3b82f6;
                border-color: #3b82f6;
            }

            #noQuestionsMessage {
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border: 2px dashed #cbd5e1;
                border-radius: 12px;
            }

            #noQuestionsMessage i {
                color: #64748b;
            }

            /* Step Content */
            .step-content {
                min-height: 300px;
            }

            /* Lessons Empty State */
            .lessons-empty {
                text-align: center;
                padding: 3rem 1rem;
                color: #6b7280;
            }

            .lessons-empty i {
                font-size: 3rem;
                margin-bottom: 1rem;
                display: block;
                color: #9ca3af;
            }
        </style>
    </head>
    <body>
        <!-- Loading Screen -->
        <div id="loadingScreen" class="fixed inset-0 bg-white z-50 flex items-center justify-center">
            <div class="text-center">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mb-4"></div>
                <h2 class="text-xl font-semibold text-gray-800 mb-2">Loading Course Data</h2>
                <p class="text-gray-600">Please wait while we load your course information...</p>
                <div class="mt-4 flex justify-center space-x-1">
                    <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce"></div>
                    <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0.1s"></div>
                    <div class="w-2 h-2 bg-blue-600 rounded-full animate-bounce" style="animation-delay: 0.2s"></div>
                </div>
            </div>
        </div>

        <!-- Main Content (initially hidden) -->
        <div id="mainContent" class="hidden">
            <!-- Toast Container -->
            <div class="toast-container" id="toastContainer"></div>

        <!-- Navbar -->
        <nav class="navbar navbar-expand-lg">
            <div class="container-fluid">
                <a class="navbar-brand" href="<?php echo $site_url; ?>/admin-dashboard/">
                    <img src="https://abilityseminarsgroup.com/wp-content/uploads/2023/09/Asset-6-4-768x355.png" alt="ASG Logo" style="width:130px;height:auto;">
                </a>

                <div class="collapse navbar-collapse">
                    <ul class="navbar-nav ms-auto">
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/admin-dashboard/">
                                <i class="bi bi-speedometer2"></i> Dashboard
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/all-courses/">
                                <i class="bi bi-collection"></i> All Courses
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="<?php echo $site_url; ?>/new-course/">
                                <i class="bi bi-plus-circle"></i> New Course
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <div class="mt-20 p-8 min-h-screen">
            <div class="max-w-6xl mx-auto px-4">
                <!-- 1. Course Image Hero Section -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="relative h-80 bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center overflow-hidden" id="heroImageContainer">
                        <img id="heroImage" class="w-full h-full object-cover" style="display: none;" alt="Course Image">
                        <div class="text-center text-white" id="heroPlaceholder">
                            <i class="bi bi-image text-6xl mb-4 block"></i>
                            <h3 class="text-2xl font-bold mb-2">Course Image</h3>
                            <p class="text-blue-100">Upload a compelling image for your course</p>
                        </div>
                        <div class="hero-image-overlay absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                            <button class="bg-white bg-opacity-90 text-gray-800 px-6 py-3 rounded-lg font-medium hover:bg-white hover:-translate-y-1 transition-all duration-300" onclick="triggerImageUpload()">
                                <i class="bi bi-camera mr-2"></i> Change Image
                            </button>
                        </div>
                    </div>
                    <input type="file" id="imageUploadInput" accept="image/*" style="display: none;">
                </div>

                <!-- 2. Course Overview -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-8 py-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
                            <i class="bi bi-info-circle text-blue-600"></i>
                            Course Overview
                        </h2>
                    </div>
                    <div class="p-8">
                        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
                            <div class="lg:col-span-2">
                                <h1 id="courseTitle" class="text-3xl font-bold text-gray-800 mb-2">Loading Course...</h1>
                                <p id="courseDescription" class="text-gray-600 text-lg mb-4">Loading description...</p>
                                <div class="flex gap-4 items-center flex-wrap">
                                    <span class="course-status px-4 py-2 rounded-full text-sm font-medium" id="courseStatus">Loading...</span>
                                    <span id="courseCode" class="text-gray-500 text-sm font-mono"></span>
                                </div>
                            </div>
                            <div class="text-center p-6 bg-gray-50 rounded-xl">
                                <div id="modulesCount" class="text-4xl font-bold text-blue-600 mb-2">0</div>
                                <div class="text-gray-600 text-sm font-medium">Modules</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 3. Basic Information -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="bg-gradient-to-r from-gray-50 to-blue-50 px-8 py-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
                            <i class="bi bi-pencil-square text-blue-600"></i>
                            Basic Information
                        </h2>
                    </div>
                    <div class="p-8">
                        <form id="basicForm">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                <div class="space-y-2">
                                    <label for="courseTitleInput" class="block text-sm font-semibold text-gray-700">Course Title *</label>
                                    <input type="text" id="courseTitleInput" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" required>
                                    <p class="text-sm text-gray-500">Enter a compelling title for your course</p>
                                </div>
                                <div class="space-y-2">
                                    <label for="courseLanguage" class="block text-sm font-semibold text-gray-700">Language</label>
                                    <select id="courseLanguage" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors">
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                        <option value="pt">Portuguese</option>
                                    </select>
                                </div>
                            </div>
                            <div class="mt-6 space-y-2">
                                <label for="courseDescriptionInput" class="block text-sm font-semibold text-gray-700">Course Description *</label>
                                <textarea id="courseDescriptionInput" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" rows="4" required></textarea>
                                <p class="text-sm text-gray-500">Provide a detailed description of what students will learn</p>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 4. Pricing & Details -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="bg-gradient-to-r from-gray-50 to-green-50 px-8 py-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
                            <i class="bi bi-currency-euro text-green-600"></i>
                            Pricing & Details
                        </h2>
                    </div>
                    <div class="p-8">
                        <form id="pricingForm">
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div class="space-y-2">
                                    <label for="coursePrice" class="block text-sm font-semibold text-gray-700">Price ($)</label>
                                    <input type="number" id="coursePrice" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="0.00" step="0.01" min="0">
                                    <p class="text-sm text-gray-500">Course price in USD</p>
                                </div>
                                <div class="space-y-2">
                                    <label for="courseDuration" class="block text-sm font-semibold text-gray-700">Duration (hours)</label>
                                    <input type="number" id="courseDuration" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="10" min="1">
                                    <p class="text-sm text-gray-500">Estimated completion time</p>
                                </div>

                            </div>
                            <div class="mt-6 space-y-2">
                                <label class="block text-sm font-semibold text-gray-700">Category *</label>
                                <div class="flex flex-wrap gap-3" id="categoryPills">
                                    <!-- Loaded dynamically -->
                                </div>
                                <input type="hidden" id="courseCategory" required>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 5. Learning Objectives -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="bg-gradient-to-r from-gray-50 to-purple-50 px-8 py-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
                            <i class="bi bi-target text-purple-600"></i>
                            Learning Objectives
                        </h2>
                    </div>
                    <div class="p-8">
                        <form id="objectivesForm">
                            <div class="space-y-2">
                                <label for="courseObjectives" class="block text-sm font-semibold text-gray-700">What will students learn? *</label>
                                <p class="text-sm text-gray-500 mb-3">Enter each learning objective on a new line</p>
                                <textarea id="courseObjectives" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="• Master the fundamentals of...&#10;• Learn how to implement...&#10;• Understand the principles of...&#10;• Apply best practices for..."></textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 6. Course Benefits -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="bg-gradient-to-r from-gray-50 to-yellow-50 px-8 py-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
                            <i class="bi bi-star text-yellow-600"></i>
                            Course Benefits
                        </h2>
                    </div>
                    <div class="p-8">
                        <form id="benefitsForm">
                            <div class="space-y-2">
                                <label for="courseBenefits" class="block text-sm font-semibold text-gray-700">Why should students take this course?</label>
                                <p class="text-sm text-gray-500 mb-3">Enter each benefit on a new line</p>
                                <textarea id="courseBenefits" rows="5" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="• Get job-ready skills in...&#10;• Boost your career prospects&#10;• Learn from industry experts&#10;• Access to exclusive resources"></textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- 7. Modules Management -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="bg-gradient-to-r from-gray-50 to-indigo-50 px-8 py-6 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-800 flex items-center gap-3">
                            <i class="bi bi-collection text-indigo-600"></i>
                            Course Modules
                            <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium" id="modulesCountBadge">0</span>
                        </h2>
                    </div>
                    <div class="p-8">
                        <div class="text-center py-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
                            <i class="bi bi-collection text-5xl text-gray-400 mb-4 block"></i>
                            <h3 class="text-xl font-semibold text-gray-700 mb-2">Module Management</h3>
                            <p class="text-gray-500 mb-8 max-w-md mx-auto">Modules are managed separately for better organization and detailed content creation.</p>
                            <div class="flex gap-4 justify-center flex-wrap">
                                <button type="button" class="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium" onclick="event.preventDefault(); openModuleModal();">
                                    <i class="bi bi-plus-circle"></i> Create New Module
                                </button>
                                <button type="button" class="inline-flex items-center gap-2 border border-blue-300 text-blue-700 px-6 py-3 rounded-lg hover:bg-blue-50 transition-colors font-medium" onclick="event.preventDefault(); viewAllModules();">
                                    <i class="bi bi-eye"></i> View All Modules
                                </button>
                            </div>
                        </div>
                        <div id="modulesList" class="mt-6">
                            <!-- Modules summary will be loaded here -->
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="bg-white rounded-2xl shadow-lg mb-8 overflow-hidden border border-gray-200">
                    <div class="p-8">
                        <div class="flex gap-4 justify-between items-center flex-wrap">
                            <a href="<?php echo $site_url; ?>/all-courses/" class="inline-flex items-center gap-2 border border-gray-300 text-gray-700 px-6 py-3 rounded-lg hover:bg-gray-50 transition-colors font-medium">
                                <i class="bi bi-arrow-left"></i> Back to Courses
                            </a>
                            <div class="flex gap-4 flex-wrap">
                                <!-- Save Draft button removed as requested -->
                                <button type="button" class="inline-flex items-center gap-2 bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors font-medium" onclick="saveChanges()" id="saveChangesBtn">
                                    <i class="bi bi-save"></i> Save Changes
                                </button>
                                <button type="button" class="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium" onclick="confirmPublishCourse()" id="publishBtn">
                                    <i class="bi bi-rocket"></i> Publish Course
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>


        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
        
        <script>
            // ========================================
            // CONFIGURATION AND GLOBAL VARIABLES
            // ========================================
            const ASG_CONFIG = {
                apiUrl: '/wp-json/asg/v1',
                nonce: '<?php echo wp_create_nonce('wp_rest'); ?>',
                siteUrl: '<?php echo $site_url; ?>'
            };

            let currentCourse = null;
            let hasUnsavedChanges = false;

            console.log('🚀 ASG Edit Course Optimized v1.0.0 loaded');

            // ========================================
            // OPTIMIZED API CLIENT
            // ========================================
            class OptimizedAPIClient {
                constructor() {
                    this.baseUrl = ASG_CONFIG.apiUrl;
                    this.nonce = ASG_CONFIG.nonce;
                }

                async request(endpoint, options = {}) {
                    const url = `${this.baseUrl}${endpoint}`;
                    const defaultOptions = {
                        method: 'GET',
                        headers: {
                            'Content-Type': 'application/json',
                            'Accept': 'application/json',
                            'X-WP-Nonce': this.nonce
                        }
                    };

                    try {
                        console.log(`📡 API Request: ${url}`, options);
                        const response = await fetch(url, { ...defaultOptions, ...options });

                        if (!response.ok) {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }

                        const data = await response.json();
                        console.log(`✅ API Success:`, data);
                        return data;
                    } catch (error) {
                        console.error(`❌ API Error:`, error);
                        throw error;
                    }
                }

                // Course operations
                async getCourse(identifier) {
                    const params = new URLSearchParams({
                        include: 'modules,lessons,objectives,stats'
                    });
                    return this.request(`/courses/api/${identifier}?${params}`);
                }

                async updateCourse(identifier, data) {
                    console.log('🚀 Using optimized main endpoint...');
                    return this.request(`/courses/api/${identifier}`, {
                        method: 'PUT',
                        body: JSON.stringify(data)
                    });
                }

                // Module operations
                async createModule(courseId, data) {
                    return this.request(`/courses/${courseId}/modules/api`, {
                        method: 'POST',
                        body: JSON.stringify(data)
                    });
                }

                async updateModule(courseId, moduleId, data) {
                    return this.request(`/courses/${courseId}/modules/api/${moduleId}`, {
                        method: 'PUT',
                        body: JSON.stringify(data)
                    });
                }

                async deleteModule(courseId, moduleId) {
                    return this.request(`/courses/${courseId}/modules/api/${moduleId}`, {
                        method: 'DELETE'
                    });
                }

                // Lesson operations
                async createLesson(courseId, moduleId, data) {
                    return this.request(`/courses/${courseId}/modules/${moduleId}/lessons/api`, {
                        method: 'POST',
                        body: JSON.stringify(data)
                    });
                }

                async updateLesson(courseId, moduleId, lessonId, data) {
                    return this.request(`/courses/${courseId}/modules/${moduleId}/lessons/api/${lessonId}`, {
                        method: 'PUT',
                        body: JSON.stringify(data)
                    });
                }

                async deleteLesson(courseId, moduleId, lessonId) {
                    return this.request(`/courses/${courseId}/modules/${moduleId}/lessons/api/${lessonId}`, {
                        method: 'DELETE'
                    });
                }

                // Media operations
                async uploadImage(file, onProgress) {
                    const formData = new FormData();
                    formData.append('image', file);

                    return new Promise((resolve, reject) => {
                        const xhr = new XMLHttpRequest();

                        xhr.upload.addEventListener('progress', (e) => {
                            if (e.lengthComputable && onProgress) {
                                const percentComplete = (e.loaded / e.total) * 100;
                                onProgress(percentComplete);
                            }
                        });

                        xhr.onload = () => {
                            if (xhr.status === 200) {
                                try {
                                    const response = JSON.parse(xhr.responseText);
                                    resolve(response);
                                } catch (error) {
                                    reject(new Error('Invalid JSON response'));
                                }
                            } else {
                                reject(new Error(`Upload failed: ${xhr.status}`));
                            }
                        };

                        xhr.onerror = () => reject(new Error('Upload failed'));

                        xhr.open('POST', `${this.baseUrl}/media/api?type=course_image`);
                        xhr.setRequestHeader('X-WP-Nonce', this.nonce);
                        xhr.send(formData);
                    });
                }
            }

            // Initialize API client
            const API = new OptimizedAPIClient();

            // ========================================
            // UTILITY FUNCTIONS
            // ========================================
            function showToast(message, type = 'info') {
                const container = document.getElementById('toastContainer');
                const toast = document.createElement('div');
                toast.className = `toast ${type}`;
                toast.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-${getToastIcon(type)} me-2"></i>
                        <span>${message}</span>
                    </div>
                `;

                container.appendChild(toast);

                // Auto remove after 5 seconds
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.remove();
                    }
                }, 5000);

                // Remove on click
                toast.addEventListener('click', () => toast.remove());
            }

            function getToastIcon(type) {
                const icons = {
                    success: 'check-circle',
                    error: 'exclamation-triangle',
                    warning: 'exclamation-circle',
                    info: 'info-circle'
                };
                return icons[type] || 'info-circle';
            }

            // Función para mostrar modal de confirmación
            function showConfirmModal(title, message, confirmText = 'Confirm', type = 'primary') {
                return new Promise((resolve) => {
                    const modalId = 'confirmModal_' + Date.now();
                    const typeClass = type === 'danger' ? 'btn-danger' : 'btn-primary';
                    const iconClass = type === 'danger' ? 'bi-exclamation-triangle text-danger' : 'bi-question-circle text-primary';

                    const modalHtml = `
                        <div id="${modalId}" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                            <div class="bg-white rounded-2xl shadow-2xl max-w-md w-full">
                                <div class="p-6">
                                    <div class="flex items-center gap-4 mb-4">
                                        <div class="w-12 h-12 rounded-full bg-gray-100 flex items-center justify-center">
                                            <i class="bi ${iconClass} text-2xl"></i>
                                        </div>
                                        <div>
                                            <h3 class="text-lg font-semibold text-gray-900">${title}</h3>
                                        </div>
                                    </div>

                                    <div class="mb-6">
                                        <p class="text-gray-600 leading-relaxed">${message}</p>
                                    </div>

                                    <div class="flex gap-3 justify-end">
                                        <button type="button" class="px-4 py-2 text-gray-600 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors" onclick="closeConfirmModal('${modalId}', false)">
                                            Cancel
                                        </button>
                                        <button type="button" class="px-4 py-2 text-white ${typeClass} hover:opacity-90 rounded-lg transition-colors" onclick="closeConfirmModal('${modalId}', true)">
                                            ${confirmText}
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    `;

                    document.body.insertAdjacentHTML('beforeend', modalHtml);

                    // Guardar el resolver en el elemento para acceso global
                    window[`confirmResolver_${modalId}`] = resolve;
                });
            }

            // Función para cerrar modal de confirmación
            function closeConfirmModal(modalId, result) {
                const modal = document.getElementById(modalId);
                const resolver = window[`confirmResolver_${modalId}`];

                if (modal) {
                    modal.remove();
                }

                if (resolver) {
                    resolver(result);
                    delete window[`confirmResolver_${modalId}`];
                }
            }

            function showLoading(show = true) {
                const loadingScreen = document.getElementById('loadingScreen');
                const mainContent = document.getElementById('mainContent');

                if (show) {
                    loadingScreen.classList.remove('hidden');
                    mainContent.classList.add('hidden');
                } else {
                    loadingScreen.classList.add('hidden');
                    mainContent.classList.remove('hidden');
                }
            }

            function markAsChanged() {
                hasUnsavedChanges = true;
                const title = document.getElementById('pageTitle');
                if (title && !title.textContent.includes('*')) {
                    title.textContent += ' *';
                }
            }

            // ========================================
            // COURSE DATA LOADING
            // ========================================
            async function loadCourse(courseCode) {
                try {
                    showLoading(true);
                    console.log(`🔍 Loading course: ${courseCode}`);

                    const response = await API.getCourse(courseCode);

                    if (response.success && response.data) {
                        currentCourse = response.data;
                        console.log('📚 Course loaded:', currentCourse);

                        populateForm();
                        updateCourseOverview();
                        await loadModules(); // Load modules with lessons

                        showToast('Course loaded successfully', 'success');
                    } else {
                        throw new Error('Course not found');
                    }
                } catch (error) {
                    console.error('Error loading course:', error);
                    showToast('Error loading course: ' + error.message, 'error');
                } finally {
                    showLoading(false);
                }
            }

            // ========================================
            // FORM POPULATION AND UI UPDATES
            // ========================================
            function populateForm() {
                if (!currentCourse) return;

                console.log('🔄 Populating form with optimized data');

                // Basic Information - using optimized field names
                const titleInput = document.getElementById('courseTitleInput');
                const descInput = document.getElementById('courseDescriptionInput');
                const categoryHidden = document.getElementById('courseCategory');
                const languageSelect = document.getElementById('courseLanguage');
                const priceInput = document.getElementById('coursePrice');
                const durationInput = document.getElementById('courseDuration');

                if (titleInput) titleInput.value = currentCourse.name_course || '';
                if (descInput) descInput.value = currentCourse.description_course || '';
                if (languageSelect) languageSelect.value = currentCourse.language_course || 'en';
                if (priceInput) priceInput.value = currentCourse.price_course || 0;
                if (durationInput) durationInput.value = currentCourse.duration_course || 0;

                // Handle category selection
                if (currentCourse.category_course) {
                    if (categoryHidden) categoryHidden.value = currentCourse.category_course;
                    selectCategory(currentCourse.category_course);
                }

                // Populate objectives and benefits textareas
                const objectivesTextarea = document.getElementById('courseObjectives');
                const benefitsTextarea = document.getElementById('courseBenefits');

                if (objectivesTextarea) {
                    let objectivesText = '';
                    if (currentCourse.objectives_list && Array.isArray(currentCourse.objectives_list)) {
                        objectivesText = currentCourse.objectives_list.map(obj => obj.text || obj).join('\n');
                    } else if (currentCourse.objectives && typeof currentCourse.objectives === 'string') {
                        objectivesText = currentCourse.objectives.split('|').filter(obj => obj.trim()).join('\n');
                    }
                    objectivesTextarea.value = objectivesText;
                    console.log('📋 Objectives populated:', objectivesText);
                }

                if (benefitsTextarea) {
                    let benefitsText = '';
                    if (currentCourse.benefits_list && Array.isArray(currentCourse.benefits_list)) {
                        benefitsText = currentCourse.benefits_list.map(ben => ben.text || ben).join('\n');
                    } else if (currentCourse.benefits && typeof currentCourse.benefits === 'string') {
                        benefitsText = currentCourse.benefits.split('|').filter(ben => ben.trim()).join('\n');
                    }
                    benefitsTextarea.value = benefitsText;
                    console.log('📋 Benefits populated:', benefitsText);
                }

                // Hero Image - usar imagen de alta calidad
                const heroImage = document.getElementById('heroImage');
                const heroPlaceholder = document.getElementById('heroPlaceholder');

                // Priorizar large_url > cover_img > medium_url para mejor calidad
                const imageUrl = currentCourse.large_url || currentCourse.cover_img || currentCourse.medium_url;

                if (imageUrl) {
                    heroImage.src = imageUrl;
                    heroImage.style.display = 'block';
                    heroPlaceholder.style.display = 'none';
                    console.log('🖼️ Hero image loaded:', imageUrl);
                }

                console.log('✅ Form populated successfully');
            }

            function updateCourseOverview() {
                if (!currentCourse) return;

                // Update overview section
                const titleElement = document.getElementById('courseTitle');
                const descElement = document.getElementById('courseDescription');
                const codeElement = document.getElementById('courseCode');
                const statusElement = document.getElementById('courseStatus');

                if (titleElement) titleElement.textContent = currentCourse.name_course || 'Untitled Course';
                if (descElement) descElement.textContent = currentCourse.description_course || 'No description';
                if (codeElement) codeElement.textContent = currentCourse.code_course || '';

                // Update status with original styling
                if (statusElement) {
                    const status = currentCourse.status_course || 'draft';
                    statusElement.className = `course-status px-4 py-2 rounded-full text-sm font-medium status-${status}`;
                    statusElement.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                }

                // Update stats
                const modulesCount = document.getElementById('modulesCount');
                const modulesCountBadge = document.getElementById('modulesCountBadge');

                const moduleCount = currentCourse.stats?.modules_count || 0;
                if (modulesCount) modulesCount.textContent = moduleCount;
                if (modulesCountBadge) modulesCountBadge.textContent = moduleCount;
            }



            // ========================================
            // MODULES AND LESSONS MANAGEMENT
            // ========================================

            async function loadModules() {
                try {
                    console.log('📚 Loading modules for course:', currentCourse.code_course);

                    const response = await API.request(`/courses/${currentCourse.code_course}/modules/api?include=lessons`, {
                        method: 'GET'
                    });

                    if (response.success && response.data) {
                        currentCourse.modules = response.data;
                        renderModulesList();
                        updateModulesCount();
                        console.log('📚 Modules loaded:', response.data);
                    } else {
                        console.warn('📚 No modules found or error loading modules');
                        currentCourse.modules = [];
                        renderModulesList();
                    }
                } catch (error) {
                    console.error('📚 Error loading modules:', error);
                    showToast('Error loading modules: ' + error.message, 'error');
                    currentCourse.modules = [];
                    renderModulesList();
                }
            }

            function renderModulesList() {
                const container = document.getElementById('modulesList');
                if (!container) return;

                if (!currentCourse.modules || currentCourse.modules.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-8 text-gray-500">
                            <i class="bi bi-collection text-4xl mb-3 block"></i>
                            <p>No modules created yet. Create your first module to get started.</p>
                        </div>
                    `;
                    return;
                }

                const modulesHtml = currentCourse.modules.map((module, index) => `
                    <div class="bg-gray-50 rounded-xl p-6 border border-gray-200 hover:border-blue-300 transition-colors" data-module-id="${module.id_modules}">
                        <div class="flex items-start gap-4">
                            <!-- Module Image -->
                            ${module.cover_img ? `
                                <div class="flex-shrink-0">
                                    <div class="w-20 h-20 rounded-lg overflow-hidden bg-gray-100">
                                        <img src="${module.cover_img}" alt="Module cover" class="w-full h-full object-cover">
                                    </div>
                                </div>
                            ` : ''}

                            <!-- Module Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-3 mb-2">
                                            <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">Module ${index + 1}</span>
                                            <h4 class="text-lg font-semibold text-gray-800">${module.title_module || 'Untitled Module'}</h4>
                                        </div>
                                        <p class="text-gray-600 mb-3">${module.description_module || 'No description'}</p>
                                        <div class="flex items-center gap-4 text-sm text-gray-500">
                                            <span><i class="bi bi-clock"></i> ${module.duration_module || 0} min</span>
                                            <span><i class="bi bi-book"></i> ${module.lessons ? module.lessons.length : 0} lessons</span>
                                            <span><i class="bi bi-hash"></i> Order ${module.order_module || 1}</span>
                                            ${module.cover_img ? '<span class="text-green-600"><i class="bi bi-image"></i> Image</span>' : ''}
                                        </div>
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex items-center gap-2 ml-4">
                                        <button type="button" class="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors" onclick="editModule(${module.id_modules})" title="Edit Module">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="p-2 text-green-600 hover:bg-green-100 rounded-lg transition-colors" onclick="manageLessons(${module.id_modules})" title="Manage Lessons">
                                            <i class="bi bi-book"></i>
                                        </button>
                                        <button type="button" class="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors" onclick="deleteModule(${module.id_modules})" title="Delete Module">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        ${module.lessons && module.lessons.length > 0 ? `
                            <div class="mt-4 pt-4 border-t border-gray-200">
                                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-3">
                                    ${module.lessons.map((lesson, lessonIndex) => `
                                        <div class="bg-white p-3 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
                                            <div class="flex items-center justify-between">
                                                <div class="flex-1 min-w-0">
                                                    <p class="text-sm font-medium text-gray-800 truncate">${lesson.title_lesson || 'Untitled Lesson'}</p>
                                                    <p class="text-xs text-gray-500">${lesson.lesson_type || 'text'} • ${lesson.duration_lesson || 0} min</p>
                                                </div>
                                                <button type="button" class="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors" onclick="editLesson(${module.id_modules}, ${lesson.id_lesson})" title="Edit Lesson">
                                                    <i class="bi bi-pencil text-xs"></i>
                                                </button>
                                            </div>
                                        </div>
                                    `).join('')}
                                </div>
                            </div>
                        ` : ''}
                    </div>
                `).join('');

                container.innerHTML = modulesHtml;
            }

            function updateModulesCount() {
                const count = currentCourse.modules ? currentCourse.modules.length : 0;
                const badge = document.getElementById('modulesCountBadge');
                if (badge) badge.textContent = count;
            }

            // ========================================
            // MODULE MANAGEMENT FUNCTIONS
            // ========================================

            function openModuleModal(moduleId = null) {
                const isEdit = moduleId !== null;
                const module = isEdit ? currentCourse.modules.find(m => m.id_modules == moduleId) : null;

                const modalHtml = `
                    <div id="moduleModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                        <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                            <div class="bg-gradient-to-r from-blue-50 to-indigo-50 px-8 py-6 border-b border-gray-200">
                                <h3 class="text-2xl font-semibold text-gray-800 flex items-center gap-3">
                                    <i class="bi bi-collection text-blue-600"></i>
                                    ${isEdit ? 'Edit Module' : 'Create New Module'}
                                </h3>
                            </div>
                            <div class="p-8">
                                <form id="moduleForm" class="space-y-6">
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2">Module Title *</label>
                                        <input type="text" id="moduleTitle" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="Enter module title" value="${module ? module.title_module || '' : ''}" required>
                                    </div>
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2">Module Description</label>
                                        <textarea id="moduleDescription" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="Describe what students will learn in this module">${module ? module.description_module || '' : ''}</textarea>
                                    </div>

                                    <!-- Module Cover Image -->
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2">Module Cover Image</label>
                                        <div class="space-y-4">
                                            <!-- Current Image Preview -->
                                            <div id="moduleImagePreview" class="relative ${module && module.cover_img ? 'block' : 'hidden'}">
                                                <div class="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
                                                    <img id="moduleCurrentImage" src="${module ? module.cover_img || '' : ''}" alt="Module cover" class="w-full h-full object-cover">
                                                    <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                                                        <button type="button" onclick="removeModuleImage()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                                                            <i class="bi bi-trash"></i> Remove Image
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Upload Area -->
                                            <div id="moduleImageUpload" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-400 transition-colors">
                                                <div class="space-y-2">
                                                    <i class="bi bi-cloud-upload text-3xl text-gray-400"></i>
                                                    <div>
                                                        <button type="button" onclick="document.getElementById('moduleImageInput').click()" class="text-blue-600 hover:text-blue-700 font-medium">
                                                            Choose module image
                                                        </button>
                                                        <span class="text-gray-500"> or drag and drop</span>
                                                    </div>
                                                    <p class="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
                                                </div>
                                                <input type="file" id="moduleImageInput" accept="image/*" class="hidden" onchange="handleModuleImageUpload(this)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-2">Duration (minutes)</label>
                                            <input type="number" id="moduleDuration" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="0" value="${module ? module.duration_module || '' : ''}" min="0">
                                        </div>
                                        <div>
                                            <label class="block text-sm font-semibold text-gray-700 mb-2">Module Order</label>
                                            <input type="number" id="moduleOrder" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-colors" placeholder="1" value="${module ? module.order_module || (currentCourse.modules ? currentCourse.modules.length + 1 : 1) : (currentCourse.modules ? currentCourse.modules.length + 1 : 1)}" min="1">
                                        </div>
                                    </div>

                                </form>
                            </div>
                            <div class="bg-gray-50 px-8 py-6 border-t border-gray-200 flex justify-end gap-4">
                                <button type="button" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium" onclick="closeModuleModal()">
                                    Cancel
                                </button>
                                <button type="button" class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium" onclick="saveModule(${moduleId})">
                                    <i class="bi bi-save"></i> ${isEdit ? 'Update Module' : 'Create Module'}
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // Focus on title input
                setTimeout(() => {
                    document.getElementById('moduleTitle')?.focus();
                }, 100);
            }

            function closeModuleModal() {
                const modal = document.getElementById('moduleModal');
                if (modal) {
                    modal.remove();
                }
            }

            async function saveModule(moduleId = null) {
                const isEdit = moduleId !== null;
                const form = document.getElementById('moduleForm');

                if (!form.checkValidity()) {
                    form.reportValidity();
                    return;
                }

                const moduleData = {
                    title_module: document.getElementById('moduleTitle').value.trim(),
                    description_module: document.getElementById('moduleDescription').value.trim(),
                    duration_module: parseInt(document.getElementById('moduleDuration').value) || 0,
                    order_module: parseInt(document.getElementById('moduleOrder').value) || 1,
                    course_code: currentCourse.code_course
                };

                // Agregar datos de imagen si existe
                if (window.moduleImageData) {
                    moduleData.cover_img = window.moduleImageData.url;
                    moduleData.image_record_id = window.moduleImageData.id;
                }

                try {
                    showToast('Saving module...', 'info');

                    let response;
                    if (isEdit) {
                        response = await API.request(`/courses/${currentCourse.code_course}/modules/api/${moduleId}`, {
                            method: 'PUT',
                            body: JSON.stringify(moduleData)
                        });
                    } else {
                        response = await API.request(`/courses/${currentCourse.code_course}/modules/api`, {
                            method: 'POST',
                            body: JSON.stringify(moduleData)
                        });
                    }

                    if (response.success) {
                        showToast(`Module ${isEdit ? 'updated' : 'created'} successfully!`, 'success');
                        closeModuleModal();
                        await loadModules(); // Reload modules
                        markAsChanged();
                    } else {
                        throw new Error(response.message || `Failed to ${isEdit ? 'update' : 'create'} module`);
                    }
                } catch (error) {
                    console.error('Error saving module:', error);
                    showToast('Error saving module: ' + error.message, 'error');
                }
            }

            function editModule(moduleId) {
                openModuleModal(moduleId);
            }

            // Funciones para manejo de imágenes de módulos
            async function handleModuleImageUpload(input) {
                const file = input.files[0];
                if (!file) return;

                // Validar tipo de archivo
                if (!file.type.startsWith('image/')) {
                    showToast('Please select a valid image file', 'error');
                    return;
                }

                // Validar tamaño (10MB max)
                if (file.size > 10 * 1024 * 1024) {
                    showToast('Image size must be less than 10MB', 'error');
                    return;
                }

                try {
                    showToast('Uploading module image...', 'info');

                    // Crear FormData para upload
                    const formData = new FormData();
                    formData.append('image', file); // Cambiar 'file' por 'image'
                    formData.append('type', 'module_cover'); // Cambiar 'file_type' por 'type'
                    formData.append('course_id', currentCourse.id_course); // Agregar course_id

                    // Upload imagen
                    const response = await API.request('/media/api', {
                        method: 'POST',
                        body: formData,
                        headers: {} // Dejar que el browser maneje Content-Type para FormData
                    });

                    if (response.success && response.data) {
                        // Mostrar preview de la imagen
                        const preview = document.getElementById('moduleImagePreview');
                        const currentImage = document.getElementById('moduleCurrentImage');
                        const uploadArea = document.getElementById('moduleImageUpload');

                        currentImage.src = response.data.url;
                        preview.classList.remove('hidden');
                        uploadArea.classList.add('hidden');

                        // Guardar referencia para cuando se guarde el módulo
                        window.moduleImageData = {
                            url: response.data.url,
                            id: response.data.id
                        };

                        showToast('Module image uploaded successfully!', 'success');
                    } else {
                        throw new Error(response.message || 'Failed to upload image');
                    }
                } catch (error) {
                    console.error('Error uploading module image:', error);
                    showToast('Error uploading image: ' + error.message, 'error');
                }
            }

            function removeModuleImage() {
                const preview = document.getElementById('moduleImagePreview');
                const uploadArea = document.getElementById('moduleImageUpload');
                const input = document.getElementById('moduleImageInput');

                preview.classList.add('hidden');
                uploadArea.classList.remove('hidden');
                input.value = '';

                // Limpiar datos de imagen
                window.moduleImageData = null;

                showToast('Module image removed', 'info');
            }

            // Funciones para manejo de video URL en lecciones
            function handleVideoUrlChange(url) {
                const videoPreview = document.getElementById('videoPreview');
                const videoIframe = document.getElementById('videoIframe');

                if (!url || url.trim() === '') {
                    videoPreview.classList.add('hidden');
                    videoIframe.innerHTML = '';
                    return;
                }

                // Generar iframe basado en el tipo de URL
                const embedUrl = generateVideoEmbedUrl(url);
                if (embedUrl) {
                    videoIframe.innerHTML = `
                        <iframe
                            src="${embedUrl}"
                            class="w-full h-full"
                            frameborder="0"
                            allowfullscreen
                            allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture">
                        </iframe>
                    `;
                    videoPreview.classList.remove('hidden');
                } else {
                    videoIframe.innerHTML = `
                        <div class="w-full h-full flex items-center justify-center text-white">
                            <div class="text-center">
                                <i class="bi bi-exclamation-triangle text-4xl mb-2"></i>
                                <p>Invalid video URL format</p>
                                <p class="text-sm opacity-75">Supported: YouTube, Vimeo, direct video URLs</p>
                            </div>
                        </div>
                    `;
                    videoPreview.classList.remove('hidden');
                }
            }

            function generateVideoEmbedUrl(url) {
                // YouTube
                const youtubeRegex = /(?:youtube\.com\/(?:[^\/]+\/.+\/|(?:v|e(?:mbed)?)\/|.*[?&]v=)|youtu\.be\/)([^"&?\/\s]{11})/;
                const youtubeMatch = url.match(youtubeRegex);
                if (youtubeMatch) {
                    return `https://www.youtube.com/embed/${youtubeMatch[1]}`;
                }

                // Vimeo
                const vimeoRegex = /(?:vimeo\.com\/)([0-9]+)/;
                const vimeoMatch = url.match(vimeoRegex);
                if (vimeoMatch) {
                    return `https://player.vimeo.com/video/${vimeoMatch[1]}`;
                }

                // Direct video URL (mp4, webm, etc.)
                const videoExtensions = /\.(mp4|webm|ogg|mov|avi)(\?.*)?$/i;
                if (videoExtensions.test(url)) {
                    return url; // Para videos directos, usaremos un elemento video en lugar de iframe
                }

                return null;
            }

            // Funciones para manejo de imágenes de lecciones
            async function handleLessonImageUpload(input) {
                const file = input.files[0];
                if (!file) return;

                // Validar tipo de archivo
                if (!file.type.startsWith('image/')) {
                    showToast('Please select a valid image file', 'error');
                    return;
                }

                // Validar tamaño (10MB max)
                if (file.size > 10 * 1024 * 1024) {
                    showToast('Image size must be less than 10MB', 'error');
                    return;
                }

                try {
                    showToast('Uploading lesson image...', 'info');

                    // Crear FormData para upload
                    const formData = new FormData();
                    formData.append('image', file); // Cambiar 'file' por 'image'
                    formData.append('type', 'lesson_cover'); // Cambiar 'file_type' por 'type'
                    formData.append('course_id', currentCourse.id_course); // Agregar course_id

                    // Upload imagen
                    const response = await API.request('/media/api', {
                        method: 'POST',
                        body: formData,
                        headers: {} // Dejar que el browser maneje Content-Type para FormData
                    });

                    if (response.success && response.data) {
                        // Mostrar preview de la imagen
                        const preview = document.getElementById('lessonImagePreview');
                        const currentImage = document.getElementById('lessonCurrentImage');
                        const uploadArea = document.getElementById('lessonImageUpload');

                        currentImage.src = response.data.url;
                        preview.classList.remove('hidden');
                        uploadArea.classList.add('hidden');

                        // Guardar referencia para cuando se guarde la lección
                        window.lessonImageData = {
                            url: response.data.url,
                            id: response.data.id
                        };

                        showToast('Lesson image uploaded successfully!', 'success');
                    } else {
                        throw new Error(response.message || 'Failed to upload image');
                    }
                } catch (error) {
                    console.error('Error uploading lesson image:', error);
                    showToast('Error uploading image: ' + error.message, 'error');
                }
            }

            function removeLessonImage() {
                const preview = document.getElementById('lessonImagePreview');
                const uploadArea = document.getElementById('lessonImageUpload');
                const input = document.getElementById('lessonImageInput');

                preview.classList.add('hidden');
                uploadArea.classList.remove('hidden');
                input.value = '';

                // Limpiar datos de imagen
                window.lessonImageData = null;

                showToast('Lesson image removed', 'info');
            }

            function openLessonModal(moduleId, lessonId = null) {
                const isEdit = lessonId !== null;
                const module = currentCourse.modules.find(m => m.id_modules == moduleId);
                const lesson = isEdit ? module?.lessons?.find(l => l.id_lesson == lessonId) : null;

                if (!module) {
                    showToast('Module not found', 'error');
                    return;
                }

                const modalHtml = `
                    <div id="lessonModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                        <div class="bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
                            <div class="bg-gradient-to-r from-green-50 to-emerald-50 px-8 py-6 border-b border-gray-200">
                                <h3 class="text-2xl font-semibold text-gray-800 flex items-center gap-3">
                                    <i class="bi bi-book text-green-600"></i>
                                    ${isEdit ? 'Edit Lesson' : 'Create New Lesson'}
                                </h3>
                                <p class="text-gray-600 mt-2">Module: ${module.title_module}</p>
                            </div>
                            <div class="p-8">
                                <form id="lessonForm" class="space-y-6">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="space-y-2">
                                            <label for="lessonTitle" class="block text-sm font-semibold text-gray-700">Lesson Title *</label>
                                            <input type="text" id="lessonTitle" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" placeholder="Enter lesson title" value="${lesson?.title_lesson || ''}" required>
                                        </div>
                                        <div class="space-y-2">
                                            <label for="lessonType" class="block text-sm font-semibold text-gray-700">Lesson Type</label>
                                            <select id="lessonType" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors">
                                                <option value="text" ${lesson?.lesson_type === 'text' ? 'selected' : ''}>📝 Text Lesson</option>
                                                <option value="video" ${lesson?.lesson_type === 'video' ? 'selected' : ''}>🎥 Video Lesson</option>
                                                <option value="quiz" ${lesson?.lesson_type === 'quiz' ? 'selected' : ''}>❓ Quiz Lesson</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="space-y-2">
                                            <label for="lessonDuration" class="block text-sm font-semibold text-gray-700">Duration (minutes)</label>
                                            <input type="number" id="lessonDuration" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" placeholder="0" min="0" value="${lesson?.duration_lesson || 0}">
                                        </div>
                                        <div class="space-y-2">
                                            <label for="lessonOrder" class="block text-sm font-semibold text-gray-700">Lesson Order</label>
                                            <input type="number" id="lessonOrder" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" placeholder="1" min="1" value="${lesson?.order_lesson || (module.lessons ? module.lessons.length + 1 : 1)}">
                                        </div>
                                    </div>
                                    <div class="space-y-2">
                                        <label for="lessonDescription" class="block text-sm font-semibold text-gray-700">Lesson Description</label>
                                        <textarea id="lessonDescription" rows="4" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" placeholder="Describe what students will learn in this lesson">${lesson?.description_lesson || ''}</textarea>
                                    </div>

                                    <!-- Video URL Field -->
                                    <div class="space-y-2" id="videoUrlSection">
                                        <label for="lessonVideoUrl" class="block text-sm font-semibold text-gray-700">Video URL</label>
                                        <input type="url" id="lessonVideoUrl" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" placeholder="https://youtube.com/watch?v=... or https://vimeo.com/..." value="${lesson?.video_url || ''}" onchange="handleVideoUrlChange(this.value)">
                                        <p class="text-sm text-gray-500">Supports YouTube, Vimeo, and direct video URLs</p>

                                        <!-- Video Preview -->
                                        <div id="videoPreview" class="mt-4 ${lesson?.video_url ? 'block' : 'hidden'}">
                                            <div class="bg-gray-100 rounded-lg p-4">
                                                <h4 class="text-sm font-semibold text-gray-700 mb-2">Video Preview:</h4>
                                                <div id="videoIframe" class="aspect-video bg-black rounded-lg overflow-hidden">
                                                    <!-- Iframe will be inserted here -->
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Is Preview Checkbox -->
                                    <div class="flex items-center space-x-3">
                                        <input type="checkbox" id="lessonIsPreview" class="w-4 h-4 text-green-600 bg-gray-100 border-gray-300 rounded focus:ring-green-500 focus:ring-2" ${lesson?.is_preview ? 'checked' : ''}>
                                        <label for="lessonIsPreview" class="text-sm font-semibold text-gray-700">
                                            🎁 Free Preview Lesson
                                            <span class="block text-xs text-gray-500 font-normal">Allow students to view this lesson without purchasing the course</span>
                                        </label>
                                    </div>

                                    <!-- Lesson Cover Image -->
                                    <div>
                                        <label class="block text-sm font-semibold text-gray-700 mb-2">Lesson Cover Image</label>
                                        <div class="space-y-4">
                                            <!-- Current Image Preview -->
                                            <div id="lessonImagePreview" class="relative ${lesson && lesson.cover_img ? 'block' : 'hidden'}">
                                                <div class="relative w-full h-48 bg-gray-100 rounded-lg overflow-hidden">
                                                    <img id="lessonCurrentImage" src="${lesson ? lesson.cover_img || '' : ''}" alt="Lesson cover" class="w-full h-full object-cover">
                                                    <div class="absolute inset-0 bg-black bg-opacity-40 opacity-0 hover:opacity-100 transition-opacity flex items-center justify-center">
                                                        <button type="button" onclick="removeLessonImage()" class="bg-red-500 text-white px-4 py-2 rounded-lg hover:bg-red-600 transition-colors">
                                                            <i class="bi bi-trash"></i> Remove Image
                                                        </button>
                                                    </div>
                                                </div>
                                            </div>

                                            <!-- Upload Area -->
                                            <div id="lessonImageUpload" class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-green-400 transition-colors">
                                                <div class="space-y-2">
                                                    <i class="bi bi-cloud-upload text-3xl text-gray-400"></i>
                                                    <div>
                                                        <button type="button" onclick="document.getElementById('lessonImageInput').click()" class="text-green-600 hover:text-green-700 font-medium">
                                                            Choose lesson image
                                                        </button>
                                                        <span class="text-gray-500"> or drag and drop</span>
                                                    </div>
                                                    <p class="text-sm text-gray-500">PNG, JPG, GIF up to 10MB</p>
                                                </div>
                                                <input type="file" id="lessonImageInput" accept="image/*" class="hidden" onchange="handleLessonImageUpload(this)">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="space-y-2" id="lessonContentSection">
                                        <label for="lessonContent" class="block text-sm font-semibold text-gray-700">Lesson Content</label>
                                        <textarea id="lessonContent" rows="6" class="w-full px-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-green-500 focus:border-green-500 transition-colors" placeholder="Enter the lesson content here...">${lesson?.content_lesson || ''}</textarea>
                                    </div>

                                    <!-- Quiz Editor Section (only visible when lesson type is quiz) -->
                                    <div class="space-y-4" id="quizEditorSection" style="display: none;">
                                        <!-- Quiz Type Selection -->
                                        <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                                            <h4 class="font-semibold text-blue-800 mb-3">
                                                <i class="bi bi-list-check me-2"></i>Quiz Type
                                            </h4>
                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <div class="quiz-type-option" onclick="selectQuizType('multiple_choice')">
                                                    <input type="radio" name="quizType" value="multiple_choice" id="quizTypeMultiple" checked>
                                                    <label for="quizTypeMultiple" class="quiz-type-label">
                                                        <div class="quiz-type-icon">📝</div>
                                                        <div class="quiz-type-title">Multiple Choice</div>
                                                        <div class="quiz-type-desc">Questions with multiple options and correct answers</div>
                                                    </label>
                                                </div>
                                                <div class="quiz-type-option" onclick="selectQuizType('true_false')">
                                                    <input type="radio" name="quizType" value="true_false" id="quizTypeTrueFalse">
                                                    <label for="quizTypeTrueFalse" class="quiz-type-label">
                                                        <div class="quiz-type-icon">✅</div>
                                                        <div class="quiz-type-title">True/False</div>
                                                        <div class="quiz-type-desc">Simple true or false questions</div>
                                                    </label>
                                                </div>
                                                <div class="quiz-type-option" onclick="selectQuizType('subjective')">
                                                    <input type="radio" name="quizType" value="subjective" id="quizTypeSubjective">
                                                    <label for="quizTypeSubjective" class="quiz-type-label">
                                                        <div class="quiz-type-icon">🎭</div>
                                                        <div class="quiz-type-title">Opinion/Feedback</div>
                                                        <div class="quiz-type-desc">Collect opinions - all answers are valid</div>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>

                                        <!-- Quiz Configuration -->
                                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4" id="quizConfigSection">
                                            <h4 class="font-semibold text-blue-800 mb-3">
                                                <i class="bi bi-gear me-2"></i>Quiz Configuration
                                            </h4>
                                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                                <div id="passingScoreSection">
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Passing Score (%)</label>
                                                    <input type="number" id="quizPassingScore" min="0" max="100" value="70"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                    <p class="text-xs text-gray-500 mt-1">Minimum score to pass the quiz</p>
                                                </div>
                                                <div>
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Max Attempts</label>
                                                    <input type="number" id="quizMaxAttempts" min="1" max="10" value="3"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                    <p class="text-xs text-gray-500 mt-1">Number of attempts allowed</p>
                                                </div>
                                                <div id="timeLimitSection">
                                                    <label class="block text-sm font-medium text-gray-700 mb-1">Time Limit (min)</label>
                                                    <input type="number" id="quizTimeLimit" min="0" value="0" placeholder="0 = No limit"
                                                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                    <p class="text-xs text-gray-500 mt-1">0 means no time limit</p>
                                                </div>
                                            </div>

                                            <!-- Subjective Quiz Notice -->
                                            <div id="subjectiveNotice" class="mt-4 p-3 bg-purple-50 border border-purple-200 rounded-lg" style="display: none;">
                                                <div class="flex items-center text-purple-800">
                                                    <i class="bi bi-info-circle me-2"></i>
                                                    <span class="font-medium">Opinion/Feedback Quiz</span>
                                                </div>
                                                <p class="text-sm text-purple-700 mt-1">
                                                    This quiz type collects opinions and feedback. All answers are considered valid and students automatically pass with 100%.
                                                    Passing score and time limit settings don't apply.
                                                </p>
                                            </div>
                                        </div>

                                        <!-- Quiz Questions -->
                                        <div class="space-y-4">
                                            <div class="flex items-center justify-between">
                                                <h4 class="font-semibold text-gray-800">
                                                    <i class="bi bi-question-circle me-2"></i>Quiz Questions
                                                </h4>
                                                <button type="button" class="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors font-medium" onclick="addQuizQuestion()">
                                                    <i class="bi bi-plus-circle me-2"></i>Add Question
                                                </button>
                                            </div>

                                            <div id="quizQuestionsList" class="space-y-4">
                                                <!-- Questions will be added here dynamically -->
                                            </div>

                                            <div id="noQuestionsMessage" class="text-center py-8 text-gray-500">
                                                <i class="bi bi-question-circle text-4xl mb-2"></i>
                                                <p>No questions added yet. Click "Add Question" to get started.</p>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="bg-gray-50 px-8 py-6 border-t border-gray-200 flex items-center justify-end gap-4">
                                <button type="button" class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-100 transition-colors font-medium" onclick="closeLessonModal()">
                                    Cancel
                                </button>
                                <button type="button" class="px-6 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors font-medium" onclick="saveLesson(${moduleId}, ${lessonId || 'null'})">
                                    <i class="bi bi-check-circle mr-2"></i>
                                    ${isEdit ? 'Update Lesson' : 'Create Lesson'}
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHtml);

                // Initialize quiz editor
                setTimeout(() => {
                    // Add event listener for lesson type change
                    const lessonTypeSelect = document.getElementById('lessonType');
                    if (lessonTypeSelect) {
                        lessonTypeSelect.addEventListener('change', showHideQuizEditor);

                        // Load quiz data if editing a quiz lesson
                        if (lesson) {
                            loadQuizData(lesson);
                        }

                        // Show/hide appropriate sections based on current type
                        showHideQuizEditor();
                    }

                    // Focus on title input
                    document.getElementById('lessonTitle')?.focus();
                }, 100);
            }

            function closeLessonModal() {
                const modal = document.getElementById('lessonModal');
                if (modal) {
                    modal.remove();

                    // Clean up quiz data
                    quizQuestions = [];
                    window.lessonImageData = null;
                }
            }

            // ===== QUIZ EDITOR FUNCTIONALITY =====
            let quizQuestions = [];
            let currentQuizType = 'multiple_choice'; // Track current quiz type

            function showHideQuizEditor() {
                const lessonType = document.getElementById('lessonType').value;
                const quizSection = document.getElementById('quizEditorSection');
                const contentSection = document.getElementById('lessonContentSection');
                const videoSection = document.getElementById('videoUrlSection');

                if (lessonType === 'quiz') {
                    quizSection.style.display = 'block';
                    contentSection.style.display = 'none';
                    if (videoSection) videoSection.style.display = 'none';
                } else {
                    quizSection.style.display = 'none';
                    contentSection.style.display = 'block';
                    if (videoSection) {
                        videoSection.style.display = lessonType === 'video' ? 'block' : 'none';
                    }
                }

                updateNoQuestionsMessage();
            }

            // Select quiz type and update UI accordingly
            function selectQuizType(type) {
                currentQuizType = type;

                // Update radio button selection
                document.querySelectorAll('input[name="quizType"]').forEach(radio => {
                    radio.checked = radio.value === type;
                });

                // Show/hide configuration sections based on quiz type
                const passingScoreSection = document.getElementById('passingScoreSection');
                const timeLimitSection = document.getElementById('timeLimitSection');
                const subjectiveNotice = document.getElementById('subjectiveNotice');

                if (type === 'subjective') {
                    // Hide scoring-related configs for subjective quizzes
                    passingScoreSection.style.opacity = '0.5';
                    passingScoreSection.style.pointerEvents = 'none';
                    timeLimitSection.style.opacity = '0.5';
                    timeLimitSection.style.pointerEvents = 'none';
                    subjectiveNotice.style.display = 'block';
                } else {
                    // Show all configs for objective quizzes
                    passingScoreSection.style.opacity = '1';
                    passingScoreSection.style.pointerEvents = 'auto';
                    timeLimitSection.style.opacity = '1';
                    timeLimitSection.style.pointerEvents = 'auto';
                    subjectiveNotice.style.display = 'none';
                }

                // Clear existing questions when changing quiz type
                if (quizQuestions.length > 0) {
                    if (confirm('Changing quiz type will remove all existing questions. Continue?')) {
                        quizQuestions = [];
                        document.getElementById('quizQuestionsList').innerHTML = '';
                        updateNoQuestionsMessage();
                    } else {
                        // Revert to previous selection
                        document.querySelectorAll('input[name="quizType"]').forEach(radio => {
                            if (radio.value === currentQuizType) {
                                radio.checked = true;
                            } else {
                                radio.checked = false;
                            }
                        });
                        return;
                    }
                }
            }

            function addQuizQuestion() {
                const questionId = Date.now();

                // Create question based on current quiz type
                const question = {
                    id: questionId,
                    type: currentQuizType,
                    question: '',
                    options: currentQuizType === 'true_false' ? [] : ['', ''], // No options for true/false
                    correct_answer: currentQuizType === 'true_false' ? [true] :
                                   currentQuizType === 'subjective' ? [] : [0],
                    allow_multiple: false,
                    is_subjective: currentQuizType === 'subjective',
                    points: currentQuizType === 'subjective' ? 0 : 1,
                    explanation: ''
                };

                quizQuestions.push(question);
                renderQuizQuestion(question, quizQuestions.length - 1);
                updateNoQuestionsMessage();
            }

            function renderQuizQuestion(question, index) {
                const container = document.getElementById('quizQuestionsList');

                // Get quiz type display info
                const typeInfo = {
                    'multiple_choice': { icon: '📝', label: 'Multiple Choice', color: 'blue' },
                    'true_false': { icon: '✅', label: 'True/False', color: 'green' },
                    'subjective': { icon: '🎭', label: 'Opinion', color: 'purple' }
                };
                const currentTypeInfo = typeInfo[question.type] || typeInfo['multiple_choice'];

                const questionHtml = `
                    <div class="quiz-question-item border border-gray-200 rounded-lg p-4 bg-white shadow-sm" data-question-id="${question.id}">
                        <div class="flex items-center justify-between mb-4">
                            <h5 class="font-medium text-gray-800 flex items-center">
                                <i class="bi bi-hash me-2 text-blue-600"></i>
                                Question ${index + 1}
                                <span class="ml-2 px-2 py-1 bg-${currentTypeInfo.color}-100 text-${currentTypeInfo.color}-800 text-xs rounded-full">
                                    ${currentTypeInfo.icon} ${currentTypeInfo.label}
                                </span>
                                ${question.allow_multiple && question.type === 'multiple_choice' ? '<span class="ml-2 px-2 py-1 bg-green-100 text-green-800 text-xs rounded-full">Multi-Select</span>' : ''}
                            </h5>
                            <div class="flex items-center gap-2">
                                <button type="button" class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50 transition-colors" onclick="removeQuizQuestion(${question.id})" title="Delete Question">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>

                        <div class="space-y-4">
                            <!-- Question Settings - Only show relevant options based on quiz type -->
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 p-3 bg-gray-50 rounded-lg">
                                ${question.type === 'multiple_choice' ? `
                                    <div class="flex items-center">
                                        <input type="checkbox" id="multiple-${question.id}"
                                               ${question.allow_multiple ? 'checked' : ''}
                                               onchange="updateQuestionMultiple(${question.id}, this.checked)"
                                               class="mr-2 text-green-600">
                                        <label for="multiple-${question.id}" class="text-sm text-gray-700">
                                            <i class="bi bi-check2-square me-1"></i>Multiple Selection
                                        </label>
                                    </div>
                                ` : ''}

                                ${question.type !== 'subjective' ? `
                                    <div class="flex items-center">
                                        <label class="text-sm text-gray-700 mr-2">Points:</label>
                                        <input type="number" min="0" max="10" value="${question.points || 1}"
                                               onchange="updateQuestionPoints(${question.id}, parseInt(this.value))"
                                               class="w-16 px-2 py-1 border border-gray-300 rounded text-sm focus:ring-2 focus:ring-blue-500">
                                    </div>
                                ` : `
                                    <div class="flex items-center text-purple-600">
                                        <i class="bi bi-info-circle me-2"></i>
                                        <span class="text-sm">Opinion questions don't affect scoring</span>
                                    </div>
                                `}

                                <div></div> <!-- Empty div for grid alignment -->
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">Question Text *</label>
                                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          rows="2" placeholder="Enter your question here..."
                                          onchange="updateQuestionText(${question.id}, this.value)">${question.question}</textarea>
                            </div>

                            <div id="options-${question.id}">
                                ${renderQuestionOptions(question)}
                            </div>

                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">
                                    Explanation (Optional)
                                    ${question.is_subjective ? '<span class="text-purple-600 text-xs">(For opinion questions, explain that all answers are valid)</span>' : ''}
                                </label>
                                <textarea class="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                          rows="2" placeholder="${question.is_subjective ? 'All opinions are valid for this question.' : 'Explain why this answer is correct...'}"
                                          onchange="updateQuestionExplanation(${question.id}, this.value)">${question.explanation}</textarea>
                            </div>
                        </div>
                    </div>
                `;

                container.insertAdjacentHTML('beforeend', questionHtml);
            }

            function renderQuestionOptions(question) {
                if (currentQuizType === 'true_false') {
                    const correctAnswers = Array.isArray(question.correct_answer) ? question.correct_answer : [question.correct_answer];
                    const trueSelected = correctAnswers.includes(true) || correctAnswers.includes('true') || correctAnswers[0] === true;
                    const falseSelected = correctAnswers.includes(false) || correctAnswers.includes('false') || correctAnswers[0] === false;

                    return `
                        <div class="space-y-2">
                            <label class="block text-sm font-medium text-gray-700">Correct Answer *</label>
                            <div class="space-y-2">
                                <label class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                    <input type="radio" name="correct-${question.id}" value="true"
                                           ${trueSelected ? 'checked' : ''}
                                           onchange="updateCorrectAnswer(${question.id}, 'true', true)" class="mr-3 text-blue-600">
                                    <i class="bi bi-check-circle text-green-600 mr-2"></i>
                                    True
                                </label>
                                <label class="flex items-center p-2 border border-gray-200 rounded-md hover:bg-gray-50 cursor-pointer">
                                    <input type="radio" name="correct-${question.id}" value="false"
                                           ${falseSelected ? 'checked' : ''}
                                           onchange="updateCorrectAnswer(${question.id}, 'false', true)" class="mr-3 text-blue-600">
                                    <i class="bi bi-x-circle text-red-600 mr-2"></i>
                                    False
                                </label>
                            </div>
                        </div>
                    `;
                } else {
                    const correctAnswers = Array.isArray(question.correct_answer) ? question.correct_answer : [question.correct_answer];
                    const inputType = question.allow_multiple ? 'checkbox' : 'radio';
                    const selectionText = currentQuizType === 'subjective' ?
                        '(All answers are valid for opinion questions)' :
                        question.allow_multiple ?
                            '(Select all correct answers)' :
                            '(Select the correct answer)';

                    return `
                        <div class="space-y-2">
                            <div class="flex items-center justify-between">
                                <label class="block text-sm font-medium text-gray-700">
                                    Answer Options *
                                    <span class="text-xs text-gray-500">${selectionText}</span>
                                </label>
                                <div class="flex items-center gap-2">
                                    <button type="button" onclick="addQuestionOption(${question.id})"
                                            class="px-2 py-1 bg-green-100 text-green-700 rounded text-xs hover:bg-green-200 transition-colors">
                                        <i class="bi bi-plus"></i> Add Option
                                    </button>
                                    ${question.options.length > 2 ? `
                                        <button type="button" onclick="removeQuestionOption(${question.id})"
                                                class="px-2 py-1 bg-red-100 text-red-700 rounded text-xs hover:bg-red-200 transition-colors">
                                            <i class="bi bi-dash"></i> Remove
                                        </button>
                                    ` : ''}
                                </div>
                            </div>
                            <div class="space-y-2">
                                ${question.options.map((option, index) => `
                                    <div class="flex items-center gap-2 p-2 border border-gray-200 rounded-md hover:bg-gray-50">
                                        <input type="${inputType}"
                                               name="correct-${question.id}"
                                               value="${index}"
                                               ${correctAnswers.includes(index) ? 'checked' : ''}
                                               onchange="updateCorrectAnswer(${question.id}, ${index}, this.checked)"
                                               class="text-blue-600 ${currentQuizType === 'subjective' ? 'opacity-50 pointer-events-none' : ''}">
                                        <span class="w-6 h-6 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center text-sm font-medium">
                                            ${String.fromCharCode(65 + index)}
                                        </span>
                                        <input type="text" class="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                                               placeholder="Option ${String.fromCharCode(65 + index)}" value="${option}"
                                               onchange="updateQuestionOption(${question.id}, ${index}, this.value)">
                                        ${question.options.length > 2 ? `
                                            <button type="button" onclick="removeSpecificOption(${question.id}, ${index})"
                                                    class="text-red-600 hover:text-red-800 p-1 rounded hover:bg-red-50 transition-colors"
                                                    title="Remove this option">
                                                <i class="bi bi-x-lg"></i>
                                            </button>
                                        ` : ''}
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                }
            }

            // Quiz question management functions

            function updateQuestionText(questionId, text) {
                const question = quizQuestions.find(q => q.id === questionId);
                if (question) question.question = text;
            }

            function updateQuestionOption(questionId, optionIndex, value) {
                const question = quizQuestions.find(q => q.id === questionId);
                if (question) question.options[optionIndex] = value;
            }

            function updateCorrectAnswer(questionId, answer, checked) {
                console.log('🔄 updateCorrectAnswer called:', { questionId, answer, checked });
                console.log('🔍 Available questions:', quizQuestions.map(q => ({ id: q.id, type: q.type })));

                const question = quizQuestions.find(q => q.id === questionId);
                if (!question) {
                    console.error('❌ Question not found:', questionId);
                    return;
                }

                // Handle based on current quiz type
                if (currentQuizType === 'true_false') {
                    // For true/false, answer is 'true' or 'false' string
                    question.correct_answer = [answer === 'true' || answer === true ? true : false];
                    console.log('True/False answer updated:', question.correct_answer);
                } else if (currentQuizType === 'subjective') {
                    // Subjective questions don't have correct answers
                    question.correct_answer = [];
                } else if (currentQuizType === 'multiple_choice') {
                    // Multiple choice questions
                    const answerIndex = parseInt(answer);

                    if (question.allow_multiple) {
                        // Handle multiple selection
                        if (!Array.isArray(question.correct_answer)) {
                            question.correct_answer = [];
                        }

                        if (checked) {
                            if (!question.correct_answer.includes(answerIndex)) {
                                question.correct_answer.push(answerIndex);
                            }
                        } else {
                            question.correct_answer = question.correct_answer.filter(idx => idx !== answerIndex);
                        }
                    } else {
                        // Handle single selection
                        question.correct_answer = [answerIndex];
                    }
                    console.log('Multiple choice answer updated:', question.correct_answer);
                }
            }

            function updateQuestionMultiple(questionId, allowMultiple) {
                const question = quizQuestions.find(q => q.id === questionId);
                // Only allow multiple selection for multiple choice questions
                if (question && question.type === 'multiple_choice') {
                    question.allow_multiple = allowMultiple;
                    if (!allowMultiple && Array.isArray(question.correct_answer) && question.correct_answer.length > 1) {
                        question.correct_answer = [question.correct_answer[0]]; // Keep only first answer
                    }

                    // Re-render options
                    const optionsContainer = document.getElementById(`options-${questionId}`);
                    optionsContainer.innerHTML = renderQuestionOptions(question);
                }
            }

            function updateQuestionPoints(questionId, points) {
                const question = quizQuestions.find(q => q.id === questionId);
                // Only allow points for objective quiz types
                if (question && question.type !== 'subjective') {
                    question.points = Math.max(0, points);
                }
            }

            function addQuestionOption(questionId) {
                const question = quizQuestions.find(q => q.id === questionId);
                if (question && question.type === 'multiple_choice') {
                    question.options.push('');

                    // Re-render options
                    const optionsContainer = document.getElementById(`options-${questionId}`);
                    optionsContainer.innerHTML = renderQuestionOptions(question);
                }
            }

            function removeQuestionOption(questionId) {
                const question = quizQuestions.find(q => q.id === questionId);
                if (question && question.options.length > 2) {
                    const lastIndex = question.options.length - 1;
                    question.options.pop();

                    // Remove from correct answers if it was selected
                    if (Array.isArray(question.correct_answer)) {
                        question.correct_answer = question.correct_answer.filter(idx => idx !== lastIndex);
                    }

                    // Re-render options
                    const optionsContainer = document.getElementById(`options-${questionId}`);
                    optionsContainer.innerHTML = renderQuestionOptions(question);
                }
            }

            function removeSpecificOption(questionId, optionIndex) {
                const question = quizQuestions.find(q => q.id === questionId);
                if (question && question.options.length > 2) {
                    question.options.splice(optionIndex, 1);

                    // Update correct answers indices
                    if (Array.isArray(question.correct_answer)) {
                        question.correct_answer = question.correct_answer
                            .filter(idx => idx !== optionIndex)
                            .map(idx => idx > optionIndex ? idx - 1 : idx);
                    }

                    // Re-render options
                    const optionsContainer = document.getElementById(`options-${questionId}`);
                    optionsContainer.innerHTML = renderQuestionOptions(question);
                }
            }

            function updateQuestionExplanation(questionId, explanation) {
                const question = quizQuestions.find(q => q.id === questionId);
                if (question) question.explanation = explanation;
            }

            function removeQuizQuestion(questionId) {
                if (confirm('Are you sure you want to delete this question?')) {
                    quizQuestions = quizQuestions.filter(q => q.id !== questionId);
                    document.querySelector(`[data-question-id="${questionId}"]`).remove();

                    // Re-number questions
                    document.querySelectorAll('.quiz-question-item').forEach((item, index) => {
                        const questionIdAttr = item.getAttribute('data-question-id');
                        item.querySelector('h5').innerHTML = `
                            <i class="bi bi-hash me-2 text-blue-600"></i>
                            Question ${index + 1}
                        `;
                    });

                    updateNoQuestionsMessage();
                }
            }

            function updateNoQuestionsMessage() {
                const noQuestionsMsg = document.getElementById('noQuestionsMessage');
                const questionsList = document.getElementById('quizQuestionsList');

                if (quizQuestions.length === 0) {
                    noQuestionsMsg.style.display = 'block';
                } else {
                    noQuestionsMsg.style.display = 'none';
                }
            }

            function loadQuizData(lesson) {
                quizQuestions = [];

                if (lesson && lesson.lesson_type === 'quiz' && lesson.content_lesson) {
                    try {
                        const quizData = JSON.parse(lesson.content_lesson);

                        // Load quiz type and configuration
                        const quizType = quizData.quiz_type || 'multiple_choice';
                        currentQuizType = quizType;
                        selectQuizType(quizType);

                        if (quizData.passing_score) document.getElementById('quizPassingScore').value = quizData.passing_score;
                        if (quizData.max_attempts) document.getElementById('quizMaxAttempts').value = quizData.max_attempts;
                        if (quizData.time_limit) document.getElementById('quizTimeLimit').value = quizData.time_limit;

                        // Load questions with enhanced structure support
                        if (quizData.questions && Array.isArray(quizData.questions)) {
                            quizData.questions.forEach((questionData, index) => {
                                // Normalize correct_answer to array format
                                let correctAnswer = questionData.correct_answer;
                                if (!Array.isArray(correctAnswer)) {
                                    correctAnswer = correctAnswer !== undefined ? [correctAnswer] : [];
                                }

                                const question = {
                                    id: questionData.id || (Date.now() + index),
                                    type: questionData.type || 'multiple_choice',
                                    question: questionData.question || '',
                                    options: questionData.options || (questionData.type === 'true_false' ? [] : ['', '']),
                                    correct_answer: correctAnswer,
                                    allow_multiple: questionData.allow_multiple || false,
                                    is_subjective: questionData.is_subjective || false,
                                    points: questionData.points !== undefined ? questionData.points : 1,
                                    explanation: questionData.explanation || ''
                                };

                                console.log('📥 Loading question:', question.id, 'Type:', question.type, 'Original correct_answer from DB:', questionData.correct_answer, 'Normalized:', question.correct_answer);

                                // Handle subjective questions
                                if (question.is_subjective) {
                                    question.correct_answer = [];
                                    question.points = 0;
                                }

                                // Ensure minimum options for multiple choice
                                if (question.type === 'multiple_choice' && question.options.length < 2) {
                                    while (question.options.length < 2) {
                                        question.options.push('');
                                    }
                                }

                                quizQuestions.push(question);
                                renderQuizQuestion(question, quizQuestions.length - 1);
                            });

                            // Ensure all true/false questions have a default correct answer
                            ensureTrueFalseDefaults();
                        }
                    } catch (e) {
                        console.error('Error parsing quiz data:', e);
                    }
                }

                updateNoQuestionsMessage();
            }

            function ensureTrueFalseDefaults() {
                quizQuestions.forEach(question => {
                    if (question.type === 'true_false') {
                        // Only fix if correct_answer is truly empty or undefined
                        if (!Array.isArray(question.correct_answer) || question.correct_answer.length === 0) {
                            question.correct_answer = [true]; // Default to true
                            console.log('🔧 Fixed empty correct_answer for true/false question:', question.id, 'set to [true]');
                        } else {
                            console.log('✅ True/false question already has correct_answer:', question.id, question.correct_answer);
                        }
                    }
                });
            }

            function validateQuizData() {
                if (quizQuestions.length === 0) {
                    showToast('Please add at least one question to the quiz', 'error');
                    return false;
                }

                for (let i = 0; i < quizQuestions.length; i++) {
                    const question = quizQuestions[i];

                    if (!question.question.trim()) {
                        showToast(`Question ${i + 1} text is required`, 'error');
                        return false;
                    }

                    if (question.type === 'multiple_choice') {
                        const filledOptions = question.options.filter(opt => opt.trim());
                        if (filledOptions.length < 2) {
                            showToast(`Question ${i + 1} needs at least 2 answer options`, 'error');
                            return false;
                        }
                    }
                }

                return true;
            }

            async function saveLesson(moduleId, lessonId = null) {
                try {
                    const isEdit = lessonId !== null;

                    // Validate required fields
                    const title = document.getElementById('lessonTitle').value.trim();
                    if (!title) {
                        showToast('Lesson title is required', 'error');
                        return;
                    }

                    // Collect lesson data
                    const lessonType = document.getElementById('lessonType').value;
                    let contentLesson = '';

                    // Handle different lesson types
                    if (lessonType === 'quiz') {
                        // Validate quiz data
                        if (!validateQuizData()) {
                            return;
                        }

                        // Prepare quiz data
                        const quizData = {
                            title: title,
                            description: document.getElementById('lessonDescription').value.trim(),
                            quiz_type: currentQuizType, // Add quiz type to data
                            passing_score: currentQuizType === 'subjective' ? 100 : (parseInt(document.getElementById('quizPassingScore').value) || 70),
                            max_attempts: parseInt(document.getElementById('quizMaxAttempts').value) || 3,
                            time_limit: currentQuizType === 'subjective' ? 0 : (parseInt(document.getElementById('quizTimeLimit').value) || 0),
                            questions: quizQuestions.map(q => ({
                                id: q.id,
                                type: q.type,
                                question: q.question,
                                options: q.options,
                                correct_answer: q.correct_answer,
                                explanation: q.explanation
                            }))
                        };

                        contentLesson = JSON.stringify(quizData);
                    } else {
                        contentLesson = document.getElementById('lessonContent').value.trim();
                    }

                    const lessonData = {
                        title_lesson: title,
                        description_lesson: document.getElementById('lessonDescription').value.trim(),
                        content_lesson: contentLesson,
                        lesson_type: lessonType,
                        duration_lesson: parseInt(document.getElementById('lessonDuration').value) || 0,
                        order_lesson: parseInt(document.getElementById('lessonOrder').value) || 1,
                        video_url: document.getElementById('lessonVideoUrl').value.trim(),
                        is_preview: document.getElementById('lessonIsPreview').checked ? 1 : 0,
                        module_id: moduleId
                    };

                    // Agregar datos de imagen si existe
                    if (window.lessonImageData) {
                        lessonData.cover_img = window.lessonImageData.url;
                        lessonData.image_record_id = window.lessonImageData.id;
                    }

                    console.log('💾 Saving lesson:', lessonData);

                    // Determine API endpoint
                    const endpoint = isEdit
                        ? `/courses/${currentCourse.code_course}/modules/${moduleId}/lessons/${lessonId}/api`
                        : `/courses/${currentCourse.code_course}/modules/${moduleId}/lessons/api`;

                    const method = isEdit ? 'PUT' : 'POST';

                    // Save lesson
                    const response = await API.request(endpoint, {
                        method: method,
                        body: JSON.stringify(lessonData)
                    });

                    if (response.success) {
                        // Show debug logs if available (for quiz lessons)
                        if (response.debug_logs && response.debug_logs.length > 0) {
                            console.log('🔍 DEBUG LOGS FROM LESSON SAVE:');
                            response.debug_logs.forEach(log => console.log('   ' + log));
                        }

                        showToast(isEdit ? 'Lesson updated successfully' : 'Lesson created successfully', 'success');
                        closeLessonModal();

                        // Check if manage lessons modal is open and refresh it
                        const manageLessonsModal = document.getElementById('lessonsModal');
                        if (manageLessonsModal) {
                            // Refresh the manage lessons modal content
                            await refreshManageLessonsModal(moduleId);
                        }

                        // Reload modules to show updated data in main view
                        await loadModules();
                    } else {
                        throw new Error(response.error || 'Failed to save lesson');
                    }

                } catch (error) {
                    console.error('💾 Error saving lesson:', error);
                    showToast('Error saving lesson: ' + error.message, 'error');
                }
            }

            function manageLessons(moduleId) {
                // Open the full manage lessons modal (defined later in the file)
                // This will be handled by the function at line ~1496
                const module = currentCourse.modules.find(m => m.id_modules == moduleId);
                if (!module) return;

                const modalHtml = `
                    <div id="lessonsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                        <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                            <div class="bg-gradient-to-r from-green-50 to-blue-50 px-8 py-6 border-b border-gray-200">
                                <h3 class="text-2xl font-semibold text-gray-800 flex items-center gap-3">
                                    <i class="bi bi-book text-green-600"></i>
                                    Manage Lessons - ${module.title_module}
                                </h3>
                                <p class="text-gray-600 mt-2">Create and organize lessons for this module</p>
                            </div>
                            <div class="p-8">
                                <div class="flex justify-between items-center mb-6">
                                    <div class="flex items-center gap-4">
                                        <span class="text-lg font-medium text-gray-700">
                                            ${module.lessons ? module.lessons.length : 0} Lessons
                                        </span>
                                    </div>
                                    <button type="button" class="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium" onclick="openLessonModal(${moduleId})">
                                        <i class="bi bi-plus-circle"></i> Add Lesson
                                    </button>
                                </div>
                                <div id="lessonsList-${moduleId}" class="space-y-4">
                                    ${renderLessonsForModal(module.lessons || [])}
                                </div>
                            </div>
                            <div class="bg-gray-50 px-8 py-6 border-t border-gray-200 flex justify-end">
                                <button type="button" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium" onclick="closeLessonsModal()">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHtml);
            }

            function editLesson(moduleId, lessonId) {
                openLessonModal(moduleId, lessonId);
            }

            async function refreshManageLessonsModal(moduleId) {
                try {
                    console.log('🔄 Refreshing manage lessons modal for module:', moduleId);

                    // Get updated lessons from API
                    const response = await API.request(`/courses/${currentCourse.code_course}/modules/${moduleId}/lessons/api`, {
                        method: 'GET'
                    });

                    if (response.success && response.data) {
                        // Update the module's lessons in currentCourse
                        const moduleIndex = currentCourse.modules.findIndex(m => m.id_modules == moduleId);
                        if (moduleIndex !== -1) {
                            currentCourse.modules[moduleIndex].lessons = response.data;
                        }

                        // Find and update the lessons container in the modal
                        const lessonsContainer = document.getElementById(`lessonsList-${moduleId}`);
                        if (lessonsContainer) {
                            const module = currentCourse.modules.find(m => m.id_modules == moduleId);
                            lessonsContainer.innerHTML = renderLessonsForModal(response.data);

                            // Update lesson count in modal header - find the span that contains "X Lessons"
                            const lessonCountElement = document.querySelector('#lessonsModal span.text-lg');
                            if (lessonCountElement) {
                                lessonCountElement.textContent = `${response.data.length} Lessons`;
                            }

                            console.log('✅ Manage lessons modal refreshed successfully');
                        }
                    }
                } catch (error) {
                    console.error('❌ Error refreshing manage lessons modal:', error);
                }
            }

            function renderLessonsListHTML(module) {
                if (!module.lessons || module.lessons.length === 0) {
                    return `
                        <div class="text-center py-12 text-gray-500">
                            <i class="bi bi-book text-4xl mb-4 block"></i>
                            <p class="text-lg font-medium mb-2">No lessons yet</p>
                            <p class="text-sm">Start building your module by adding the first lesson</p>
                        </div>
                    `;
                }

                return module.lessons.map((lesson, index) => `
                    <div class="lesson-item bg-white border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                        <div class="flex items-start gap-4">
                            <!-- Lesson Image/Icon -->
                            <div class="flex-shrink-0">
                                ${lesson.cover_img ? `
                                    <div class="w-16 h-16 rounded-lg overflow-hidden bg-gray-100">
                                        <img src="${lesson.cover_img}" alt="Lesson cover" class="w-full h-full object-cover">
                                    </div>
                                ` : `
                                    <div class="w-16 h-16 bg-blue-600 text-white rounded-lg flex items-center justify-center font-semibold text-lg">
                                        ${index + 1}
                                    </div>
                                `}
                            </div>

                            <!-- Lesson Content -->
                            <div class="flex-1 min-w-0">
                                <div class="flex items-start justify-between">
                                    <div class="flex-1">
                                        <div class="flex items-center gap-2 mb-1">
                                            <h4 class="font-semibold text-gray-800 truncate">${lesson.title_lesson || 'Untitled Lesson'}</h4>
                                            ${lesson.is_preview ? '<span class="bg-green-100 text-green-800 text-xs px-2 py-1 rounded-full font-medium">🎁 Preview</span>' : ''}
                                        </div>

                                        <div class="flex items-center gap-4 text-sm text-gray-500 mb-2">
                                            <span><i class="bi bi-clock"></i> ${lesson.duration_lesson || 0} min</span>
                                            <span><i class="bi bi-tag"></i> ${lesson.lesson_type || 'text'}</span>
                                            <span><i class="bi bi-hash"></i> Order ${lesson.order_lesson || index + 1}</span>
                                            ${lesson.video_url ? '<span class="text-red-500"><i class="bi bi-play-circle-fill"></i> Video</span>' : ''}
                                        </div>

                                        ${lesson.description_lesson ? `
                                            <p class="text-sm text-gray-600 line-clamp-2">${lesson.description_lesson}</p>
                                        ` : ''}
                                    </div>

                                    <!-- Action Buttons -->
                                    <div class="flex items-center gap-2 ml-4">
                                        <button type="button" class="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors" onclick="editLesson(${module.id_modules}, ${lesson.id_lesson})" title="Edit Lesson">
                                            <i class="bi bi-pencil"></i>
                                        </button>
                                        <button type="button" class="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors" onclick="deleteLesson(${module.id_modules}, ${lesson.id_lesson})" title="Delete Lesson">
                                            <i class="bi bi-trash"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            async function deleteModule(moduleId) {
                const module = currentCourse.modules.find(m => m.id_modules == moduleId);
                if (!module) return;

                const confirmed = await showConfirmModal(
                    'Delete Module',
                    `Are you sure you want to delete "${module.title_module}"? This will also delete all lessons in this module. This action cannot be undone.`,
                    'Delete',
                    'danger'
                );

                if (!confirmed) return;

                try {
                    showToast('Deleting module...', 'info');

                    const response = await API.request(`/courses/${currentCourse.code_course}/modules/api/${moduleId}`, {
                        method: 'DELETE'
                    });

                    if (response.success) {
                        showToast('Module deleted successfully!', 'success');
                        await loadModules(); // Reload modules
                        markAsChanged();
                    } else {
                        throw new Error(response.message || 'Failed to delete module');
                    }
                } catch (error) {
                    console.error('Error deleting module:', error);
                    showToast('Error deleting module: ' + error.message, 'error');
                }
            }

            async function deleteLesson(moduleId, lessonId) {
                // Si solo se pasa un parámetro, asumimos que es lessonId y buscamos el módulo
                if (arguments.length === 1) {
                    lessonId = moduleId;
                    moduleId = null;

                    // Buscar el módulo que contiene esta lección
                    for (const module of currentCourse.modules) {
                        if (module.lessons && module.lessons.find(l => l.id_lesson == lessonId)) {
                            moduleId = module.id_modules;
                            break;
                        }
                    }
                }

                if (!moduleId) {
                    showToast('Module not found for this lesson', 'error');
                    return;
                }

                const module = currentCourse.modules.find(m => m.id_modules == moduleId);
                const lesson = module?.lessons?.find(l => l.id_lesson == lessonId);

                if (!lesson) {
                    showToast('Lesson not found', 'error');
                    return;
                }

                const confirmed = await showConfirmModal(
                    'Delete Lesson',
                    `Are you sure you want to delete "${lesson.title_lesson}"? This action cannot be undone.`,
                    'Delete',
                    'danger'
                );

                if (!confirmed) return;

                try {
                    showToast('Deleting lesson...', 'info');

                    const response = await API.request(`/courses/${currentCourse.code_course}/modules/${moduleId}/lessons/${lessonId}/api`, {
                        method: 'DELETE'
                    });

                    if (response.success) {
                        showToast('Lesson deleted successfully!', 'success');
                        await loadModules(); // Reload modules
                        markAsChanged();
                    } else {
                        throw new Error(response.message || 'Failed to delete lesson');
                    }
                } catch (error) {
                    console.error('Error deleting lesson:', error);
                    showToast('Error deleting lesson: ' + error.message, 'error');
                }
            }

            function viewAllModules() {
                const container = document.getElementById('modulesList');
                if (!container) return;

                // Toggle between compact and expanded view
                const isExpanded = container.classList.contains('expanded-view');

                if (isExpanded) {
                    // Switch back to compact view
                    container.classList.remove('expanded-view');
                    renderModulesList(); // Render compact view

                    // Update button text
                    const button = document.querySelector('button[onclick*="viewAllModules"]');
                    if (button) {
                        button.innerHTML = '<i class="bi bi-eye"></i> View All Modules';
                    }
                } else {
                    // Switch to expanded view
                    container.classList.add('expanded-view');
                    renderExpandedModulesView();

                    // Update button text
                    const button = document.querySelector('button[onclick*="viewAllModules"]');
                    if (button) {
                        button.innerHTML = '<i class="bi bi-list"></i> Compact View';
                    }
                }
            }

            function renderExpandedModulesView() {
                const container = document.getElementById('modulesList');
                if (!container) return;

                if (!currentCourse.modules || currentCourse.modules.length === 0) {
                    container.innerHTML = `
                        <div class="text-center py-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
                            <i class="bi bi-collection text-5xl text-gray-400 mb-4 block"></i>
                            <h3 class="text-xl font-semibold text-gray-700 mb-2">No Modules Created</h3>
                            <p class="text-gray-500 mb-6">Start building your course by creating your first module</p>
                            <button type="button" class="inline-flex items-center gap-2 bg-blue-600 text-white px-6 py-3 rounded-lg hover:bg-blue-700 transition-colors font-medium" onclick="openModuleModal()">
                                <i class="bi bi-plus-circle"></i> Create First Module
                            </button>
                        </div>
                    `;
                    return;
                }

                const expandedHtml = `
                    <div class="space-y-6">
                        <div class="flex items-center justify-between mb-6">
                            <h3 class="text-2xl font-semibold text-gray-800">Course Modules (${currentCourse.modules.length})</h3>
                            <button type="button" class="inline-flex items-center gap-2 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium" onclick="openModuleModal()">
                                <i class="bi bi-plus-circle"></i> Add Module
                            </button>
                        </div>

                        ${currentCourse.modules.map((module, index) => `
                            <div class="bg-white rounded-xl border border-gray-200 shadow-sm hover:shadow-md transition-shadow">
                                <div class="p-6 border-b border-gray-200">
                                    <div class="flex items-start justify-between">
                                        <div class="flex-1">
                                            <div class="flex items-center gap-3 mb-3">
                                                <span class="bg-blue-600 text-white px-4 py-2 rounded-full text-sm font-medium">
                                                    Module ${index + 1}
                                                </span>
                                                <h4 class="text-xl font-semibold text-gray-800">${module.title_module || 'Untitled Module'}</h4>
                                            </div>
                                            <p class="text-gray-600 mb-4 leading-relaxed">${module.description_module || 'No description provided'}</p>
                                            <div class="flex items-center gap-6 text-sm">
                                                <span class="flex items-center gap-2 text-gray-500">
                                                    <i class="bi bi-clock text-blue-600"></i>
                                                    <strong>${module.duration_module || 0}</strong> minutes
                                                </span>
                                                <span class="flex items-center gap-2 text-gray-500">
                                                    <i class="bi bi-book text-green-600"></i>
                                                    <strong>${module.lessons ? module.lessons.length : 0}</strong> lessons
                                                </span>
                                                <span class="flex items-center gap-2 text-gray-500">
                                                    <i class="bi bi-hash text-purple-600"></i>
                                                    Order: <strong>${module.order_module || index + 1}</strong>
                                                </span>
                                                <span class="flex items-center gap-2 text-gray-500">
                                                    <i class="bi bi-calendar text-blue-600"></i>
                                                    <strong>Created:</strong> ${module.date_module ? new Date(module.date_module).toLocaleDateString() : 'N/A'}
                                                </span>
                                            </div>
                                        </div>
                                        <div class="flex items-center gap-2 ml-6">
                                            <button type="button" class="p-3 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors" onclick="editModule(${module.id_modules})" title="Edit Module">
                                                <i class="bi bi-pencil text-lg"></i>
                                            </button>
                                            <button type="button" class="p-3 text-green-600 hover:bg-green-100 rounded-lg transition-colors" onclick="manageLessons(${module.id_modules})" title="Manage Lessons">
                                                <i class="bi bi-book text-lg"></i>
                                            </button>
                                            <button type="button" class="p-3 text-red-600 hover:bg-red-100 rounded-lg transition-colors" onclick="deleteModule(${module.id_modules})" title="Delete Module">
                                                <i class="bi bi-trash text-lg"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>

                                ${module.lessons && module.lessons.length > 0 ? `
                                    <div class="p-6">
                                        <h5 class="text-lg font-semibold text-gray-800 mb-4 flex items-center gap-2">
                                            <i class="bi bi-list-ul text-blue-600"></i>
                                            Lessons (${module.lessons.length})
                                        </h5>
                                        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                                            ${module.lessons.map((lesson, lessonIndex) => `
                                                <div class="bg-gray-50 p-4 rounded-lg border border-gray-200 hover:border-blue-300 transition-colors">
                                                    <div class="flex items-start justify-between">
                                                        <div class="flex-1 min-w-0">
                                                            <div class="flex items-center gap-2 mb-2">
                                                                <span class="bg-gray-600 text-white px-2 py-1 rounded text-xs font-medium">
                                                                    ${lessonIndex + 1}
                                                                </span>
                                                                <h6 class="font-medium text-gray-800 truncate">${lesson.title_lesson || 'Untitled Lesson'}</h6>
                                                            </div>
                                                            <div class="flex items-center gap-3 text-xs text-gray-500 mb-2">
                                                                <span class="flex items-center gap-1">
                                                                    <i class="bi bi-tag"></i>
                                                                    ${lesson.lesson_type || 'text'}
                                                                </span>
                                                                <span class="flex items-center gap-1">
                                                                    <i class="bi bi-clock"></i>
                                                                    ${lesson.duration_lesson || 0}min
                                                                </span>
                                                            </div>
                                                        </div>
                                                        <button type="button" class="p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors ml-2" onclick="editLesson(${module.id_modules}, ${lesson.id_lesson})" title="Edit Lesson">
                                                            <i class="bi bi-pencil text-sm"></i>
                                                        </button>
                                                    </div>
                                                </div>
                                            `).join('')}
                                        </div>
                                    </div>
                                ` : `
                                    <div class="p-6 text-center text-gray-500">
                                        <i class="bi bi-book text-2xl mb-2 block"></i>
                                        <p class="text-sm">No lessons in this module yet</p>
                                        <button type="button" class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-700 text-sm font-medium mt-2" onclick="manageLessons(${module.id_modules})">
                                            <i class="bi bi-plus-circle"></i> Add First Lesson
                                        </button>
                                    </div>
                                `}
                            </div>
                        `).join('')}
                    </div>
                `;

                container.innerHTML = expandedHtml;
            }

            // ========================================
            // LESSONS MANAGEMENT FUNCTIONS
            // ========================================

            function manageLessons(moduleId) {
                const module = currentCourse.modules.find(m => m.id_modules == moduleId);
                if (!module) return;

                const modalHtml = `
                    <div id="lessonsModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
                        <div class="bg-white rounded-2xl shadow-2xl max-w-6xl w-full max-h-[90vh] overflow-y-auto">
                            <div class="bg-gradient-to-r from-green-50 to-blue-50 px-8 py-6 border-b border-gray-200">
                                <h3 class="text-2xl font-semibold text-gray-800 flex items-center gap-3">
                                    <i class="bi bi-book text-green-600"></i>
                                    Manage Lessons - ${module.title_module}
                                </h3>
                                <p class="text-gray-600 mt-2">Create and organize lessons for this module</p>
                            </div>
                            <div class="p-8">
                                <div class="flex justify-between items-center mb-6">
                                    <div class="flex items-center gap-4">
                                        <span class="text-lg font-medium text-gray-700">
                                            ${module.lessons ? module.lessons.length : 0} Lessons
                                        </span>
                                    </div>
                                    <button type="button" class="inline-flex items-center gap-2 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium" onclick="openLessonModal(${moduleId})">
                                        <i class="bi bi-plus-circle"></i> Add Lesson
                                    </button>
                                </div>
                                <div id="lessonsList-${moduleId}" class="space-y-4">
                                    ${renderLessonsForModal(module.lessons || [])}
                                </div>
                            </div>
                            <div class="bg-gray-50 px-8 py-6 border-t border-gray-200 flex justify-end">
                                <button type="button" class="px-6 py-3 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors font-medium" onclick="closeLessonsModal()">
                                    Close
                                </button>
                            </div>
                        </div>
                    </div>
                `;

                document.body.insertAdjacentHTML('beforeend', modalHtml);
            }

            function renderLessonsForModal(lessons) {
                if (!lessons || lessons.length === 0) {
                    return `
                        <div class="text-center py-12 bg-gray-50 rounded-xl border-2 border-dashed border-gray-300">
                            <i class="bi bi-book text-4xl text-gray-400 mb-3 block"></i>
                            <p class="text-gray-600">No lessons created yet. Add your first lesson to get started.</p>
                        </div>
                    `;
                }

                return lessons.map((lesson, index) => `
                    <div class="bg-gray-50 rounded-lg p-4 border border-gray-200 hover:border-blue-300 transition-colors">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4 flex-1">
                                <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                                    ${index + 1}
                                </span>
                                <div class="flex-1">
                                    <h4 class="font-semibold text-gray-800">${lesson.title_lesson || 'Untitled Lesson'}</h4>
                                    <div class="flex items-center gap-4 text-sm text-gray-500 mt-1">
                                        <span><i class="bi bi-clock"></i> ${lesson.duration_lesson || 0} min</span>
                                        <span><i class="bi bi-tag"></i> ${lesson.lesson_type || 'text'}</span>
                                        <span><i class="bi bi-hash"></i> Order ${lesson.order_lesson || 'N/A'}</span>
                                    </div>
                                </div>
                            </div>
                            <div class="flex items-center gap-2">
                                <button type="button" class="p-2 text-blue-600 hover:bg-blue-100 rounded-lg transition-colors" onclick="editLesson(${lesson.id_lesson})" title="Edit Lesson">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button type="button" class="p-2 text-red-600 hover:bg-red-100 rounded-lg transition-colors" onclick="deleteLesson(${lesson.id_lesson})" title="Delete Lesson">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                `).join('');
            }

            function closeLessonsModal() {
                const modal = document.getElementById('lessonsModal');
                if (modal) {
                    modal.remove();
                }
            }

            // ========================================
            // OBJECTIVES AND BENEFITS MANAGEMENT






            // ========================================
            // IMAGE UPLOAD FUNCTIONALITY
            // ========================================
            function triggerImageUpload() {
                document.getElementById('imageUploadInput').click();
            }

            async function handleImageUpload(event) {
                const file = event.target.files[0];
                if (!file) return;

                // Validate file
                if (!file.type.startsWith('image/')) {
                    showToast('Please select a valid image file', 'error');
                    return;
                }

                if (file.size > 5 * 1024 * 1024) { // 5MB limit
                    showToast('Image size must be less than 5MB', 'error');
                    return;
                }

                try {
                    showToast('Uploading image...', 'info');

                    const result = await API.uploadImage(file, (progress) => {
                        console.log(`Upload progress: ${progress}%`);
                    });

                    if (result.success && result.data) {
                        // Update hero image con la mejor calidad disponible
                        const heroImage = document.getElementById('heroImage');
                        const heroPlaceholder = document.getElementById('heroPlaceholder');

                        // Usar large_url para mejor calidad en el hero
                        const imageUrl = result.data.large_url || result.data.url || result.data.medium_url;

                        heroImage.src = imageUrl;
                        heroImage.style.display = 'block';
                        heroPlaceholder.style.display = 'none';

                        // Update current course data con todas las URLs
                        currentCourse.cover_img = result.data.url;
                        currentCourse.large_url = result.data.large_url;
                        currentCourse.medium_url = result.data.medium_url;
                        currentCourse.thumbnail_url = result.data.thumbnail_url;

                        markAsChanged();
                        showToast('High quality image uploaded successfully', 'success');
                        console.log('🖼️ Image uploaded with URLs:', result.data);
                    } else {
                        throw new Error('Upload failed');
                    }
                } catch (error) {
                    console.error('Upload error:', error);
                    showToast('Error uploading image: ' + error.message, 'error');
                }
            }

            // ========================================
            // SAVE FUNCTIONALITY
            // ========================================
            async function saveChanges() {
                try {
                    console.log('💾 Starting save process...');

                    if (!validateForm()) {
                        showToast('Please fill in all required fields', 'error');
                        return;
                    }

                    showToast('Saving changes...', 'info');

                    const formData = collectFormData();
                    console.log('💾 Sending data to API:', formData);
                    console.log('💾 Course code:', currentCourse.code_course);

                    const response = await API.updateCourse(currentCourse.code_course, formData);
                    console.log('💾 API Response:', response);

                    if (response.success) {
                        hasUnsavedChanges = false;
                        const title = document.getElementById('pageTitle');
                        if (title) title.textContent = title.textContent.replace(' *', '');

                        // Update current course with saved data
                        Object.assign(currentCourse, formData);

                        showToast('Changes saved successfully! 🎉', 'success');
                        console.log('💾 Save completed successfully');
                    } else {
                        throw new Error(response.message || 'Save failed');
                    }
                } catch (error) {
                    console.error('💾 Save error:', error);
                    showToast('Error saving changes: ' + error.message, 'error');
                }
            }

            async function saveDraft() {
                try {
                    showToast('Saving draft...', 'info');

                    const formData = collectFormData();
                    formData.status_course = 'draft';

                    const response = await API.updateCourse(currentCourse.code_course, formData);

                    if (response.success) {
                        hasUnsavedChanges = false;
                        const title = document.getElementById('pageTitle');
                        if (title) title.textContent = title.textContent.replace(' *', '');

                        updateCourseOverview(); // Refresh status
                        showToast('Draft saved successfully', 'success');
                    } else {
                        throw new Error('Save failed');
                    }
                } catch (error) {
                    console.error('Save error:', error);
                    showToast('Error saving draft: ' + error.message, 'error');
                }
            }

            async function publishCourse() {
                try {
                    if (!validateForm()) {
                        showToast('Please fill in all required fields before publishing', 'error');
                        return;
                    }

                    if (!confirm('Are you sure you want to publish this course? It will be visible to students.')) {
                        return;
                    }

                    showToast('Publishing course...', 'info');

                    const formData = collectFormData();
                    formData.status_course = 'published';

                    const response = await API.updateCourse(currentCourse.code_course, formData);

                    if (response.success) {
                        hasUnsavedChanges = false;
                        const title = document.getElementById('pageTitle');
                        if (title) title.textContent = title.textContent.replace(' *', '');

                        updateCourseOverview(); // Refresh status
                        showToast('Course published successfully! 🎉', 'success');
                    } else {
                        throw new Error('Publish failed');
                    }
                } catch (error) {
                    console.error('Publish error:', error);
                    showToast('Error publishing course: ' + error.message, 'error');
                }
            }

            // ========================================
            // HELPER FUNCTIONS
            // ========================================
            function collectFormData() {
                console.log('📝 Collecting form data...');

                // Get all form values
                const titleValue = document.getElementById('courseTitleInput')?.value || '';
                const descValue = document.getElementById('courseDescriptionInput')?.value || '';
                const categoryValue = document.getElementById('courseCategory')?.value || '';
                const languageValue = document.getElementById('courseLanguage')?.value || 'en';
                const priceValue = document.getElementById('coursePrice')?.value || '0';
                const durationValue = document.getElementById('courseDuration')?.value || '0';


               

                const data = {
                    name_course: titleValue,
                    description_course: descValue,
                    category_course: categoryValue,
                    language_course: languageValue,
                    price_course: parseFloat(priceValue) || 0,
                    duration_course: parseInt(durationValue) || 0
                
                };

                // Get objectives and benefits from textareas
                const objectivesValue = document.getElementById('courseObjectives')?.value || '';
                const benefitsValue = document.getElementById('courseBenefits')?.value || '';

                console.log('📋 Raw objectives value:', objectivesValue);
                console.log('📋 Raw benefits value:', benefitsValue);

                // Convert newlines to | separator for database storage
                data.objectives = objectivesValue.split('\n').filter(obj => obj.trim()).join('|');
                data.benefits = benefitsValue.split('\n').filter(ben => ben.trim()).join('|');

                console.log('📋 Formatted objectives:', data.objectives);
                console.log('📋 Formatted benefits:', data.benefits);
                console.log('📤 Final data being sent:', data);

                // Add image data if available
                if (currentCourse.cover_img) {
                    data.cover_img = currentCourse.cover_img;
                }
                if (currentCourse.large_url) {
                    data.large_url = currentCourse.large_url;
                }
                if (currentCourse.medium_url) {
                    data.medium_url = currentCourse.medium_url;
                }

                console.log('📝 Final data to send:', data);
                return data;
            }

            function validateForm() {
                const requiredFields = [
                    { id: 'courseTitleInput', name: 'Course Title' },
                    { id: 'courseDescriptionInput', name: 'Course Description' },
                    { id: 'courseCategory', name: 'Category' }
                ];

                for (const field of requiredFields) {
                    const element = document.getElementById(field.id);
                    if (!element || !element.value.trim()) {
                        showToast(`${field.name} is required`, 'error');
                        element?.focus();
                        return false;
                    }
                }

                return true;
            }





            // ========================================
            // CATEGORY MANAGEMENT
            // ========================================
            function loadCategories() {
                const categories = {
                    finance: { label: 'Finance', icon: '💰' },
                    marketing: { label: 'Marketing', icon: '📱' },
                    'personal-development': { label: 'Personal Development', icon: '🧠' },
                    technology: { label: 'Technology', icon: '💻' },
                    business: { label: 'Business', icon: '📊' },
                    health: { label: 'Health & Wellness', icon: '🏥' },
                    education: { label: 'Education', icon: '📚' },
                    'arts-design': { label: 'Arts & Design', icon: '🎨' }
                };

                const container = document.getElementById('categoryPills');
                if (!container) return;

                container.innerHTML = Object.entries(categories).map(([key, category]) => `
                    <button type="button" class="category-pill px-4 py-2 border border-gray-300 rounded-full text-sm font-medium hover:bg-gray-50 transition-colors"
                            data-category="${key}" onclick="selectCategory('${key}')">
                        ${category.icon} ${category.label}
                    </button>
                `).join('');
            }

            function selectCategory(categoryKey) {
                // Remove active class from all pills
                document.querySelectorAll('.category-pill').forEach(pill => {
                    pill.classList.remove('bg-blue-600', 'text-white', 'border-blue-600');
                    pill.classList.add('border-gray-300', 'hover:bg-gray-50');
                });

                // Add active class to selected pill
                const selectedPill = document.querySelector(`[data-category="${categoryKey}"]`);
                if (selectedPill) {
                    selectedPill.classList.add('bg-blue-600', 'text-white', 'border-blue-600');
                    selectedPill.classList.remove('border-gray-300', 'hover:bg-gray-50');
                }

                // Update hidden input
                const hiddenInput = document.getElementById('courseCategory');
                if (hiddenInput) {
                    hiddenInput.value = categoryKey;
                }

                markAsChanged();
            }

            function confirmPublishCourse() {
                if (!validateForm()) {
                    showToast('Please fill in all required fields before publishing', 'error');
                    return;
                }

                if (confirm('Are you sure you want to publish this course? It will be visible to students.')) {
                    publishCourse();
                }
            }

            function previewCourse() {
                if (currentCourse && currentCourse.code_course) {
                    const previewUrl = `${ASG_CONFIG.siteUrl}/student-courses/#${currentCourse.code_course}`;
                    window.open(previewUrl, '_blank');
                } else {
                    showToast('Please save the course first', 'warning');
                }
            }

            function duplicateCourse() {
                showToast('Course duplication feature coming soon', 'info');
            }

            // ========================================
            // INITIALIZATION
            // ========================================
            document.addEventListener('DOMContentLoaded', function() {
                console.log('🎯 Initializing Edit Course Optimized with Original UI/UX');

                // Load categories first
                loadCategories();

                // Setup image upload handler
                const imageInput = document.getElementById('imageUploadInput');
                if (imageInput) {
                    imageInput.addEventListener('change', handleImageUpload);
                }

                // Setup form change tracking for all forms and individual inputs
                const forms = ['basicForm', 'pricingForm', 'objectivesForm', 'benefitsForm'];
                forms.forEach(formId => {
                    const form = document.getElementById(formId);
                    if (form) {
                        form.addEventListener('input', markAsChanged);
                        form.addEventListener('change', markAsChanged);
                    }
                });

                // Setup individual input listeners for critical fields
                const criticalInputs = [
                    'courseTitleInput', 'courseDescriptionInput', 'courseCategory',
                    'courseLanguage', 'coursePrice', 'courseDuration',
                    'courseObjectives', 'courseBenefits'
                ];

                criticalInputs.forEach(inputId => {
                    const input = document.getElementById(inputId);
                    if (input) {
                        input.addEventListener('input', function() {
                            console.log(`📝 Field changed: ${inputId} = ${this.value}`);
                            markAsChanged();
                        });
                        input.addEventListener('change', function() {
                            console.log(`📝 Field changed: ${inputId} = ${this.value}`);
                            markAsChanged();
                        });
                    } else {
                        console.warn(`⚠️ Input not found: ${inputId}`);
                    }
                });

                // Get course code from URL hash
                const courseCode = window.location.hash.replace('#', '');
                if (courseCode) {
                    console.log(`🔍 Loading course from hash: ${courseCode}`);
                    loadCourse(courseCode);
                } else {
                    showLoading(false);
                    showToast('No course code provided', 'error');
                }

                // Setup hash change listener
                window.addEventListener('hashchange', function() {
                    const newCourseCode = window.location.hash.replace('#', '');
                    if (newCourseCode && newCourseCode !== currentCourse?.code_course) {
                        if (hasUnsavedChanges) {
                            if (confirm('You have unsaved changes. Do you want to continue?')) {
                                loadCourse(newCourseCode);
                            } else {
                                window.location.hash = currentCourse.code_course;
                            }
                        } else {
                            loadCourse(newCourseCode);
                        }
                    }
                });

                // Setup beforeunload warning
                window.addEventListener('beforeunload', function(e) {
                    if (hasUnsavedChanges) {
                        e.preventDefault();
                        e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                    }
                });

                console.log('✅ Edit Course Optimized initialized successfully');
            });
        </script>
    </body>
    </html>
    <?php
} // End of asg_create_edit_course_optimized_page()

/**
 * Register optimized edit course page
 */
function asg_register_edit_course_optimized_page() {
    $page_slug = 'edit-course';
    $page = get_page_by_path($page_slug);

    if (!$page) {
        $page_data = array(
            'post_title'    => 'Edit Course - ASG Optimized',
            'post_content'  => '[asg_edit_course]',
            'post_status'   => 'publish',
            'post_type'     => 'page',
            'post_name'     => $page_slug
        );

        $page_id = wp_insert_post($page_data);
        if ($page_id) {
            error_log('✅ ASG: Optimized edit-course page created with ID: ' . $page_id);
        }
    } else {
        // Update existing page
        wp_update_post(array(
            'ID' => $page->ID,
            'post_content' => '[asg_edit_course]'
        ));
        error_log('✅ ASG: Edit-course page updated with optimized version');
    }
}

/**
 * Shortcode for optimized edit course page
 */
function asg_edit_course_optimized_shortcode($atts) {
    error_log('🎯 ASG: Optimized edit course shortcode executed');
    ob_start();
    asg_create_edit_course_optimized_page();
    return ob_get_clean();
}

// Register hooks for optimized version
add_action('init', 'asg_register_edit_course_optimized_page');
add_shortcode('asg_edit_course', 'asg_edit_course_optimized_shortcode');

// Optional: Handle direct access
add_action('template_redirect', function() {
    if (is_page('edit-course')) {
        asg_create_edit_course_optimized_page();
        exit;
    }
});

error_log('✅ ASG: Edit Course Optimized loaded successfully');
