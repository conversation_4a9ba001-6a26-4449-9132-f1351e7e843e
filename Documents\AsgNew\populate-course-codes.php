<?php
/**
 * SCRIPT PARA POBLAR course_code EN REGISTROS EXISTENTES
 * Ejecutar una sola vez después de la optimización de la tabla
 */

// Solo ejecutar si es admin y se pasa el parámetro correcto
if (!current_user_can('manage_options') || !isset($_GET['populate_course_codes']) || $_GET['populate_course_codes'] !== 'confirm') {
    wp_die('Acceso denegado. Solo administradores pueden ejecutar este script.');
}

global $wpdb;

echo "<h2>🚀 Optimizando tabla wpic_asg_progress...</h2>";

// PASO 0: Verificar si la columna course_code existe
$column_exists = $wpdb->get_var("
    SELECT COUNT(*)
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = DATABASE()
    AND TABLE_NAME = 'wpic_asg_progress'
    AND COLUMN_NAME = 'course_code'
");

echo "<p><strong>Verificando estructura de tabla:</strong></p>";
echo "<ul>";
echo "<li>Columna course_code existe: " . ($column_exists ? "✅ SÍ" : "❌ NO") . "</li>";
echo "</ul>";

// Si no existe la columna, crearla
if (!$column_exists) {
    echo "<p>🔧 Agregando columna course_code...</p>";

    $alter_sql = "ALTER TABLE wpic_asg_progress ADD COLUMN course_code VARCHAR(100) NULL AFTER lesson_id";
    $alter_result = $wpdb->query($alter_sql);

    if ($alter_result === false) {
        echo "<p>❌ <strong>Error al agregar columna:</strong> " . $wpdb->last_error . "</p>";
        exit;
    }

    echo "<p>✅ Columna course_code agregada exitosamente</p>";

    // Crear índices
    $indexes = [
        "CREATE INDEX IF NOT EXISTS idx_student_lesson ON wpic_asg_progress (student_id, lesson_id)",
        "CREATE INDEX IF NOT EXISTS idx_student_completed ON wpic_asg_progress (student_id, completed)",
        "CREATE INDEX IF NOT EXISTS idx_student_course ON wpic_asg_progress (student_id, course_code)",
        "CREATE INDEX IF NOT EXISTS idx_student_course_completed ON wpic_asg_progress (student_id, course_code, completed)"
    ];

    echo "<p>🔧 Creando índices optimizados...</p>";
    foreach ($indexes as $index_sql) {
        $wpdb->query($index_sql);
    }
    echo "<p>✅ Índices creados</p>";
}

// PASO 1: Verificar estado actual
$total_records = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress");
$missing_course_code = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NULL OR course_code = ''");

echo "<p><strong>Estado actual:</strong></p>";
echo "<ul>";
echo "<li>Total de registros: {$total_records}</li>";
echo "<li>Sin course_code: {$missing_course_code}</li>";
echo "<li>Con course_code: " . ($total_records - $missing_course_code) . "</li>";
echo "</ul>";

if ($missing_course_code == 0) {
    echo "<p>✅ <strong>Todos los registros ya tienen course_code. No es necesario ejecutar el script.</strong></p>";
    exit;
}

// PASO 2: Poblar course_code
echo "<p>🔄 Actualizando registros...</p>";

$sql = "
    UPDATE wpic_asg_progress p
    JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
    JOIN wpic_modules m ON l.code_module = m.code_module
    SET p.course_code = m.code_course,
        p.updated_at = NOW()
    WHERE p.course_code IS NULL
";

$updated = $wpdb->query($sql);

if ($updated === false) {
    echo "<p>❌ <strong>Error al actualizar registros:</strong> " . $wpdb->last_error . "</p>";
    exit;
}

// PASO 3: Verificar resultado
$remaining_missing = $wpdb->get_var("SELECT COUNT(*) FROM wpic_asg_progress WHERE course_code IS NULL");

echo "<p><strong>Resultado:</strong></p>";
echo "<ul>";
echo "<li>✅ Registros actualizados: {$updated}</li>";
echo "<li>📊 Registros sin course_code restantes: {$remaining_missing}</li>";
echo "</ul>";

if ($remaining_missing > 0) {
    echo "<p>⚠️ <strong>Advertencia:</strong> Algunos registros no pudieron actualizarse. Esto puede deberse a:</p>";
    echo "<ul>";
    echo "<li>Lecciones eliminadas o sin módulo asociado</li>";
    echo "<li>Módulos sin curso asociado</li>";
    echo "<li>Datos inconsistentes en la base de datos</li>";
    echo "</ul>";
    
    // Mostrar registros problemáticos
    $problematic = $wpdb->get_results("
        SELECT p.id_progress, p.student_id, p.lesson_id, l.title_lesson, l.code_module
        FROM wpic_asg_progress p
        LEFT JOIN wpic_lessons l ON p.lesson_id = l.id_lesson
        WHERE p.course_code IS NULL
        LIMIT 10
    ");
    
    if (!empty($problematic)) {
        echo "<p><strong>Primeros 10 registros problemáticos:</strong></p>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr><th>ID Progress</th><th>Student ID</th><th>Lesson ID</th><th>Lesson Title</th><th>Module Code</th></tr>";
        foreach ($problematic as $record) {
            echo "<tr>";
            echo "<td>{$record->id_progress}</td>";
            echo "<td>{$record->student_id}</td>";
            echo "<td>{$record->lesson_id}</td>";
            echo "<td>" . ($record->title_lesson ?: 'N/A') . "</td>";
            echo "<td>" . ($record->code_module ?: 'N/A') . "</td>";
            echo "</tr>";
        }
        echo "</table>";
    }
} else {
    echo "<p>🎉 <strong>¡Perfecto! Todos los registros han sido actualizados exitosamente.</strong></p>";
}

// PASO 4: Mostrar estadísticas finales
$stats = $wpdb->get_results("
    SELECT 
        course_code,
        COUNT(*) as total_records,
        COUNT(CASE WHEN completed = 1 THEN 1 END) as completed_records,
        COUNT(DISTINCT student_id) as unique_students
    FROM wpic_asg_progress 
    WHERE course_code IS NOT NULL
    GROUP BY course_code
    ORDER BY total_records DESC
");

if (!empty($stats)) {
    echo "<h3>📊 Estadísticas por curso:</h3>";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
    echo "<tr><th>Course Code</th><th>Total Records</th><th>Completed</th><th>Students</th><th>Progress %</th></tr>";
    foreach ($stats as $stat) {
        $progress_pct = $stat->total_records > 0 ? round(($stat->completed_records / $stat->total_records) * 100, 1) : 0;
        echo "<tr>";
        echo "<td>{$stat->course_code}</td>";
        echo "<td>{$stat->total_records}</td>";
        echo "<td>{$stat->completed_records}</td>";
        echo "<td>{$stat->unique_students}</td>";
        echo "<td>{$progress_pct}%</td>";
        echo "</tr>";
    }
    echo "</table>";
}

echo "<h3>✅ Proceso completado</h3>";
echo "<p><strong>Próximos pasos:</strong></p>";
echo "<ol>";
echo "<li>Los endpoints ya están optimizados para usar course_code</li>";
echo "<li>Las nuevas consultas serán mucho más rápidas</li>";
echo "<li>Los nuevos registros se crearán automáticamente con course_code</li>";
echo "</ol>";

echo "<p><em>Puedes cerrar esta página. El script no necesita ejecutarse nuevamente.</em></p>";
?>

<style>
body { font-family: Arial, sans-serif; margin: 20px; }
table { width: 100%; max-width: 800px; }
th, td { padding: 8px; text-align: left; border: 1px solid #ddd; }
th { background-color: #f2f2f2; }
ul, ol { margin: 10px 0; }
li { margin: 5px 0; }
</style>
